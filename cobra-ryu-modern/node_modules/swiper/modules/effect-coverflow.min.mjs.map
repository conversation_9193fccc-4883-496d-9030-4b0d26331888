{"version": 3, "file": "effect-coverflow.mjs.mjs", "names": ["createShadow", "effectInit", "effect<PERSON>arget", "getSlideTransformEl", "getRotateFix", "EffectCoverflow", "_ref", "swiper", "extendParams", "on", "coverflowEffect", "rotate", "stretch", "depth", "scale", "modifier", "slideShadows", "effect", "setTranslate", "width", "swiper<PERSON><PERSON><PERSON>", "height", "swiperHeight", "slides", "slidesSizesGrid", "params", "isHorizontal", "transform", "translate", "center", "r", "i", "length", "slideEl", "slideSize", "centerOffset", "swiperSlideOffset", "offsetMultiplier", "rotateY", "rotateX", "translateZ", "Math", "abs", "indexOf", "parseFloat", "translateY", "translateX", "slideTransform", "style", "zIndex", "round", "shadowBeforeEl", "querySelector", "shadowAfterEl", "opacity", "setTransition", "duration", "map", "for<PERSON>ach", "el", "transitionDuration", "querySelectorAll", "shadowEl", "perspective", "overwriteParams", "watchSlidesProgress"], "sources": ["0"], "mappings": "YAAcA,iBAAoB,8CACpBC,eAAkB,4CAClBC,iBAAoB,8CACpBC,yBAA0BC,iBAAoB,0BAE5D,SAASC,gBAAgBC,GACvB,IAAIC,OACFA,EAAMC,aACNA,EAAYC,GACZA,GACEH,EACJE,EAAa,CACXE,gBAAiB,CACfC,OAAQ,GACRC,QAAS,EACTC,MAAO,IACPC,MAAO,EACPC,SAAU,EACVC,cAAc,KAwElBf,WAAW,CACTgB,OAAQ,YACRV,SACAE,KACAS,aAzEmB,KACnB,MACEC,MAAOC,EACPC,OAAQC,EAAYC,OACpBA,EAAMC,gBACNA,GACEjB,EACEkB,EAASlB,EAAOkB,OAAOf,gBACvBgB,EAAenB,EAAOmB,eACtBC,EAAYpB,EAAOqB,UACnBC,EAASH,EAA4BN,EAAc,EAA1BO,EAA2CL,EAAe,EAA3BK,EACxDhB,EAASe,EAAeD,EAAOd,QAAUc,EAAOd,OAChDiB,EAAYH,EAAOZ,MACnBiB,EAAI1B,aAAaG,GAEvB,IAAK,IAAIwB,EAAI,EAAGC,EAAST,EAAOS,OAAQD,EAAIC,EAAQD,GAAK,EAAG,CAC1D,MAAME,EAAUV,EAAOQ,GACjBG,EAAYV,EAAgBO,GAE5BI,GAAgBN,EADFI,EAAQG,kBACiBF,EAAY,GAAKA,EACxDG,EAA8C,mBAApBZ,EAAOV,SAA0BU,EAAOV,SAASoB,GAAgBA,EAAeV,EAAOV,SACvH,IAAIuB,EAAUZ,EAAef,EAAS0B,EAAmB,EACrDE,EAAUb,EAAe,EAAIf,EAAS0B,EAEtCG,GAAcZ,EAAYa,KAAKC,IAAIL,GACnCzB,EAAUa,EAAOb,QAEE,iBAAZA,IAAkD,IAA1BA,EAAQ+B,QAAQ,OACjD/B,EAAUgC,WAAWnB,EAAOb,SAAW,IAAMsB,GAE/C,IAAIW,EAAanB,EAAe,EAAId,EAAUyB,EAC1CS,EAAapB,EAAed,EAAUyB,EAAmB,EACzDvB,EAAQ,GAAK,EAAIW,EAAOX,OAAS2B,KAAKC,IAAIL,GAG1CI,KAAKC,IAAII,GAAc,OAAOA,EAAa,GAC3CL,KAAKC,IAAIG,GAAc,OAAOA,EAAa,GAC3CJ,KAAKC,IAAIF,GAAc,OAAOA,EAAa,GAC3CC,KAAKC,IAAIJ,GAAW,OAAOA,EAAU,GACrCG,KAAKC,IAAIH,GAAW,OAAOA,EAAU,GACrCE,KAAKC,IAAI5B,GAAS,OAAOA,EAAQ,GACrC,MAAMiC,EAAiB,eAAeD,OAAgBD,OAAgBL,iBAA0BV,EAAES,kBAAwBT,EAAEQ,gBAAsBxB,KAIlJ,GAHiBZ,aAAauB,EAAQQ,GAC7Be,MAAMrB,UAAYoB,EAC3Bd,EAAQe,MAAMC,OAAmD,EAAzCR,KAAKC,IAAID,KAAKS,MAAMb,IACxCZ,EAAOT,aAAc,CAEvB,IAAImC,EAAiBzB,EAAeO,EAAQmB,cAAc,6BAA+BnB,EAAQmB,cAAc,4BAC3GC,EAAgB3B,EAAeO,EAAQmB,cAAc,8BAAgCnB,EAAQmB,cAAc,+BAC1GD,IACHA,EAAiBnD,aAAa,YAAaiC,EAASP,EAAe,OAAS,QAEzE2B,IACHA,EAAgBrD,aAAa,YAAaiC,EAASP,EAAe,QAAU,WAE1EyB,IAAgBA,EAAeH,MAAMM,QAAUjB,EAAmB,EAAIA,EAAmB,GACzFgB,IAAeA,EAAcL,MAAMM,SAAWjB,EAAmB,GAAKA,EAAmB,EAC/F,CACF,GAgBAkB,cAdoBC,IACMjD,EAAOgB,OAAOkC,KAAIxB,GAAW9B,oBAAoB8B,KACzDyB,SAAQC,IACxBA,EAAGX,MAAMY,mBAAqB,GAAGJ,MACjCG,EAAGE,iBAAiB,gHAAgHH,SAAQI,IAC1IA,EAASd,MAAMY,mBAAqB,GAAGJ,KAAY,GACnD,GACF,EAQFO,YAAa,KAAM,EACnBC,gBAAiB,KAAM,CACrBC,qBAAqB,KAG3B,QAES5D"}