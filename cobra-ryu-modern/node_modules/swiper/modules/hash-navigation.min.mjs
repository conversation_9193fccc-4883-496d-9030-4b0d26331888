import{g as getDocument,a as getWindow}from"../shared/ssr-window.esm.min.mjs";import{e as elementChildren}from"../shared/utils.min.mjs";function HashNavigation(a){let{swiper:e,extendParams:t,emit:s,on:i}=a,n=!1;const r=getDocument(),h=getWindow();t({hashNavigation:{enabled:!1,replaceState:!1,watchState:!1,getSlideIndex(a,t){if(e.virtual&&e.params.virtual.enabled){const a=e.slides.find((a=>a.getAttribute("data-hash")===t));if(!a)return 0;return parseInt(a.getAttribute("data-swiper-slide-index"),10)}return e.getSlideIndex(elementChildren(e.slidesEl,`.${e.params.slideClass}[data-hash="${t}"], swiper-slide[data-hash="${t}"]`)[0])}}});const d=()=>{s("hashChange");const a=r.location.hash.replace("#",""),t=e.virtual&&e.params.virtual.enabled?e.slidesEl.querySelector(`[data-swiper-slide-index="${e.activeIndex}"]`):e.slides[e.activeIndex];if(a!==(t?t.getAttribute("data-hash"):"")){const t=e.params.hashNavigation.getSlideIndex(e,a);if(void 0===t||Number.isNaN(t))return;e.slideTo(t)}},l=()=>{if(!n||!e.params.hashNavigation.enabled)return;const a=e.virtual&&e.params.virtual.enabled?e.slidesEl.querySelector(`[data-swiper-slide-index="${e.activeIndex}"]`):e.slides[e.activeIndex],t=a?a.getAttribute("data-hash")||a.getAttribute("data-history"):"";e.params.hashNavigation.replaceState&&h.history&&h.history.replaceState?(h.history.replaceState(null,null,`#${t}`||""),s("hashSet")):(r.location.hash=t||"",s("hashSet"))};i("init",(()=>{e.params.hashNavigation.enabled&&(()=>{if(!e.params.hashNavigation.enabled||e.params.history&&e.params.history.enabled)return;n=!0;const a=r.location.hash.replace("#","");if(a){const t=0,s=e.params.hashNavigation.getSlideIndex(e,a);e.slideTo(s||0,t,e.params.runCallbacksOnInit,!0)}e.params.hashNavigation.watchState&&h.addEventListener("hashchange",d)})()})),i("destroy",(()=>{e.params.hashNavigation.enabled&&e.params.hashNavigation.watchState&&h.removeEventListener("hashchange",d)})),i("transitionEnd _freeModeNoMomentumRelease",(()=>{n&&l()})),i("slideChange",(()=>{n&&e.params.cssMode&&l()}))}export{HashNavigation as default};
//# sourceMappingURL=hash-navigation.min.mjs.map