import { motion } from 'framer-motion'
import { useState } from 'react'
import { ChevronLeft, ChevronRight, X, Play } from 'lucide-react'

const Gallery = () => {
  const [selectedImage, setSelectedImage] = useState(null)
  const [currentCategory, setCurrentCategory] = useState('all')

  const categories = [
    { id: 'all', name: '<PERSON><PERSON><PERSON>' },
    { id: 'training', name: '<PERSON><PERSON><PERSON><PERSON>' },
    { id: 'competitions', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
    { id: 'events', name: '<PERSON><PERSON><PERSON>' },
    { id: 'kids', name: '<PERSON><PERSON><PERSON>' }
  ]

  const galleryItems = [
    {
      id: 1,
      type: 'image',
      src: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      category: 'training',
      title: 'MMA Trénink',
      description: 'Intenzivní trénink smí<PERSON><PERSON><PERSON>ch bo<PERSON><PERSON><PERSON>ch uměn<PERSON>'
    },
    {
      id: 2,
      type: 'image',
      src: 'https://images.unsplash.com/photo-1544717297-fa95b6ee9643?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      category: 'training',
      title: 'Grappling Session',
      description: 'Technika a síla v grapplingovém tréninku'
    },
    {
      id: 3,
      type: 'image',
      src: 'https://images.unsplash.com/photo-1549719386-74dfcbf7dbed?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      category: 'competitions',
      title: 'Regionální turnaj',
      description: 'Naši bojovníci na regionálním turnaji'
    },
    {
      id: 4,
      type: 'image',
      src: 'https://images.unsplash.com/photo-1566753323558-f4e0952af115?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      category: 'training',
      title: 'Karate Kata',
      description: 'Preciznost a elegance v karate kata'
    },
    {
      id: 5,
      type: 'image',
      src: 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      category: 'kids',
      title: 'Dětský trénink',
      description: 'Mladí bojovníci se učí základy'
    },
    {
      id: 6,
      type: 'image',
      src: 'https://images.unsplash.com/photo-1552374196-1ab2a1c593e8?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      category: 'events',
      title: 'Graduační ceremonie',
      description: 'Slavnostní předávání pásů'
    },
    {
      id: 7,
      type: 'video',
      src: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      category: 'training',
      title: 'Tréninkové video',
      description: 'Ukázka našich tréninků'
    },
    {
      id: 8,
      type: 'image',
      src: 'https://images.unsplash.com/photo-1517438984742-1262db08379e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      category: 'competitions',
      title: 'Vítězné momenty',
      description: 'Radost z úspěchu na turnaji'
    }
  ]

  const filteredItems = currentCategory === 'all' 
    ? galleryItems 
    : galleryItems.filter(item => item.category === currentCategory)

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: "easeOut"
      }
    }
  }

  return (
    <section id="gallery" className="section-padding bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="container-custom">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <span className="inline-block px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-medium mb-4">
            Naše momenty
          </span>
          <h2 className="text-4xl lg:text-5xl font-display font-bold text-gray-900 mb-6">
            Foto
            <span className="text-gradient block">galerie</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Podívejte se na atmosféru našich tréninků, úspěchy našich členů 
            a nezapomenutelné momenty z našeho dojo.
          </p>
        </motion.div>

        {/* Category Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex flex-wrap justify-center gap-4 mb-12"
        >
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setCurrentCategory(category.id)}
              className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                currentCategory === category.id
                  ? 'bg-primary-600 text-white shadow-lg'
                  : 'bg-white text-gray-700 hover:bg-primary-50 hover:text-primary-600'
              }`}
            >
              {category.name}
            </button>
          ))}
        </motion.div>

        {/* Gallery Grid */}
        <motion.div
          key={currentCategory}
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
        >
          {filteredItems.map((item, index) => (
            <motion.div
              key={item.id}
              variants={itemVariants}
              className="group relative aspect-square bg-gray-200 rounded-xl overflow-hidden cursor-pointer"
              onClick={() => setSelectedImage(item)}
            >
              <img
                src={item.src}
                alt={item.title}
                className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
              />
              
              {/* Overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="absolute bottom-4 left-4 right-4 text-white">
                  <h3 className="font-semibold mb-1">{item.title}</h3>
                  <p className="text-sm text-gray-300">{item.description}</p>
                </div>
                
                {item.type === 'video' && (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                      <Play className="w-8 h-8 text-white ml-1" />
                    </div>
                  </div>
                )}
              </div>

              {/* Category Badge */}
              <div className="absolute top-4 left-4">
                <span className="px-2 py-1 bg-primary-600 text-white text-xs font-medium rounded-full">
                  {categories.find(cat => cat.id === item.category)?.name}
                </span>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Lightbox Modal */}
        {selectedImage && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4"
            onClick={() => setSelectedImage(null)}
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              className="relative max-w-4xl max-h-full"
              onClick={(e) => e.stopPropagation()}
            >
              <img
                src={selectedImage.src}
                alt={selectedImage.title}
                className="w-full h-full object-contain rounded-lg"
              />
              
              {/* Close Button */}
              <button
                onClick={() => setSelectedImage(null)}
                className="absolute top-4 right-4 w-10 h-10 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-black/70 transition-colors duration-200"
              >
                <X className="w-6 h-6" />
              </button>

              {/* Info */}
              <div className="absolute bottom-4 left-4 right-4 bg-black/50 backdrop-blur-sm rounded-lg p-4 text-white">
                <h3 className="text-xl font-semibold mb-2">{selectedImage.title}</h3>
                <p className="text-gray-300">{selectedImage.description}</p>
              </div>
            </motion.div>
          </motion.div>
        )}

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.4, duration: 0.6 }}
          className="text-center mt-16"
        >
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Sledujte nás na sociálních sítích
          </h3>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Více fotek a videí z našich tréninků a akcí najdete na našich 
            sociálních sítích. Buďte součástí naší komunity!
          </p>
          <div className="flex justify-center gap-4">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="btn-primary"
            >
              Facebook
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="btn-outline"
            >
              Instagram
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default Gallery
