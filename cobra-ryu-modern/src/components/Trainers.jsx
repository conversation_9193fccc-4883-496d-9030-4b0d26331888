import { motion } from 'framer-motion'
import { Award, Star, Users, Calendar } from 'lucide-react'

const Trainers = () => {
  const trainers = [
    {
      name: '<PERSON>',
      title: 'Hlavní trenér MMA',
      experience: '15 let zkušenost<PERSON>',
      specialization: ['MMA', 'Kickbox', 'Grappling', 'Kondiční příprava'],
      achievements: [
        'Mistr ČR v MMA (2018, 2019)',
        'Certifikovaný trenér UFC Gym',
        '3x vítěz regionálních turnajů',
        'Trenér roku 2020'
      ],
      bio: 'Bývalý profesionální bojovník s bohatými zkušenostmi z domácích i zahraničních turnajů. Specializuje se na komplexní přípravu bojovníků všech úrovní.',
      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
      rating: 4.9,
      students: 120
    },
    {
      name: '<PERSON><PERSON><PERSON>',
      title: '<PERSON>ren<PERSON><PERSON>',
      experience: '12 let zkušenost<PERSON>',
      specialization: ['Allkampf-Jitsu', 'Sebeobrana', 'Krav Maga', 'Taktický výcvik'],
      achievements: [
        'Instruktor Allkampf-Jitsu 3. Dan',
        'Kurzy sebeobrany pro bezpečnostní složky',
        'Certifikát IKMF Krav Maga',
        'Specialista na ženskou sebeobranu'
      ],
      bio: 'Specialista na moderní sebeobranu a praktické bojové techniky. Pracuje s bezpečnostními složkami a vede kurzy sebeobrany.',
      image: 'https://images.unsplash.com/photo-1566753323558-f4e0952af115?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
      rating: 4.8,
      students: 85
    },
    {
      name: 'Jana Kratochvílová',
      title: 'Trenérka dětských skupin',
      experience: '8 let zkušeností',
      specialization: ['Dětské MMA', 'Karate', 'Gymnastika', 'Sportovní psychologie'],
      achievements: [
        'Trenérka mládeže 1. třídy',
        'Kurzy dětské sportovní psychologie',
        'Organizátorka dětských turnajů',
        'Specialistka na práci s dětmi s ADHD'
      ],
      bio: 'Odbornice na práci s dětmi a mládeží. Vytváří pozitivní a bezpečné prostředí, kde se děti učí nejen bojové techniky, ale i životní hodnoty.',
      image: 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
      rating: 5.0,
      students: 95
    },
    {
      name: 'Pavel Dvořák',
      title: 'Trenér Karate Kyokushin',
      experience: '20 let zkušeností',
      specialization: ['Karate Kyokushin', 'Kata', 'Kumite', 'Meditace'],
      achievements: [
        'Mistr Karate 4. Dan',
        'Mezinárodní rozhodčí IKO',
        'Účastník MS v Karate (3x)',
        'Držitel černého pásu od 1998'
      ],
      bio: 'Veterán karate s dvacetiletou praxí. Kombinuje tradiční přístup s moderními metodami výuky. Důraz klade na disciplínu a duchovní rozvoj.',
      image: 'https://images.unsplash.com/photo-1552374196-1ab2a1c593e8?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
      rating: 4.9,
      students: 65
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  }

  const cardVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  }

  return (
    <section id="trainers" className="section-padding bg-white">
      <div className="container-custom">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <span className="inline-block px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-medium mb-4">
            Náš tým
          </span>
          <h2 className="text-4xl lg:text-5xl font-display font-bold text-gray-900 mb-6">
            Poznejte naše
            <span className="text-gradient block">trenéry</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Náš tým tvoří zkušení a kvalifikovaní trenéři, kteří vás provedou 
            světem bojových umění s trpělivostí, odborností a vášní.
          </p>
        </motion.div>

        {/* Trainers Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          className="grid grid-cols-1 md:grid-cols-2 gap-8"
        >
          {trainers.map((trainer, index) => (
            <motion.div
              key={index}
              variants={cardVariants}
              className="group bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100"
            >
              {/* Image Section */}
              <div className="relative h-64 overflow-hidden">
                <img
                  src={trainer.image}
                  alt={trainer.name}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                
                {/* Stats Overlay */}
                <div className="absolute bottom-4 left-4 right-4 flex justify-between items-end">
                  <div className="text-white">
                    <div className="flex items-center space-x-1 mb-1">
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      <span className="text-sm font-medium">{trainer.rating}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Users className="w-4 h-4" />
                      <span className="text-sm">{trainer.students} studentů</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                {/* Header */}
                <div className="mb-4">
                  <h3 className="text-xl font-bold text-gray-900 mb-1">
                    {trainer.name}
                  </h3>
                  <p className="text-primary-600 font-medium mb-2">
                    {trainer.title}
                  </p>
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <Calendar className="w-4 h-4" />
                    <span>{trainer.experience}</span>
                  </div>
                </div>

                {/* Bio */}
                <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                  {trainer.bio}
                </p>

                {/* Specialization */}
                <div className="mb-4">
                  <h4 className="font-semibold text-gray-900 mb-2 text-sm">Specializace:</h4>
                  <div className="flex flex-wrap gap-2">
                    {trainer.specialization.map((spec, specIndex) => (
                      <span
                        key={specIndex}
                        className="px-2 py-1 bg-primary-100 text-primary-700 rounded-full text-xs font-medium"
                      >
                        {spec}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Achievements */}
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2 text-sm flex items-center">
                    <Award className="w-4 h-4 mr-1" />
                    Úspěchy:
                  </h4>
                  <ul className="space-y-1">
                    {trainer.achievements.slice(0, 2).map((achievement, achIndex) => (
                      <li key={achIndex} className="flex items-start space-x-2">
                        <div className="w-1.5 h-1.5 bg-primary-500 rounded-full mt-2 flex-shrink-0"></div>
                        <span className="text-xs text-gray-600">{achievement}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.4, duration: 0.6 }}
          className="text-center mt-16"
        >
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Chcete se stát trenérem?
          </h3>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Hledáme nadšené a kvalifikované trenéry, kteří by rozšířili náš tým. 
            Pokud máte zkušenosti s bojovými uměními a chuť učit, ozvěte se nám!
          </p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="btn-primary"
          >
            Kontaktujte nás
          </motion.button>
        </motion.div>
      </div>
    </section>
  )
}

export default Trainers
