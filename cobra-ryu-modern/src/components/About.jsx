import { motion } from 'framer-motion'
import { CheckCircle, Target, Heart, Zap } from 'lucide-react'

const About = () => {
  const features = [
    {
      icon: Target,
      title: '<PERSON><PERSON>len<PERSON> příprava',
      description: 'Individuální přístup k každému členovi podle jeho cíl<PERSON> a možností'
    },
    {
      icon: Heart,
      title: '<PERSON><PERSON><PERSON><PERSON>',
      description: 'Vytv<PERSON><PERSON>í<PERSON> silnou a podporují<PERSON> komunitu, kde se každý cítí jako doma'
    },
    {
      icon: Zap,
      title: 'Moderní metody',
      description: 'Kombinujeme tradiční techniky s nejnovějšími tréninkovými metodami'
    }
  ]

  const values = [
    'Respekt k sobě i ostatním',
    'Disciplína a vytrvalost',
    'Neustálé zlepšování',
    'Bezpečnost na prvním místě',
    'Týmový duch a fair play',
    'Osobní růst ka<PERSON><PERSON><PERSON>'
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  }

  return (
    <section id="about" className="section-padding bg-white">
      <div className="container-custom">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Left Content */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
          >
            <motion.div variants={itemVariants} className="mb-8">
              <span className="inline-block px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-medium mb-4">
                Proč si vybrat COBRA RYU?
              </span>
              <h2 className="text-4xl lg:text-5xl font-display font-bold text-gray-900 mb-6">
                Více než jen
                <span className="text-gradient block">bojové umění</span>
              </h2>
              <p className="text-xl text-gray-600 leading-relaxed">
                COBRA RYU Strakonice není jen místo, kde se učíte bojovat. 
                Je to komunita, kde se formují charaktery, buduje sebevědomí 
                a vytváří přátelství na celý život.
              </p>
            </motion.div>

            <motion.div variants={itemVariants} className="space-y-6 mb-8">
              {features.map((feature, index) => (
                <div key={index} className="flex items-start space-x-4 group">
                  <div className="flex-shrink-0 w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center group-hover:bg-primary-500 transition-colors duration-300">
                    <feature.icon className="w-6 h-6 text-primary-600 group-hover:text-white transition-colors duration-300" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {feature.title}
                    </h3>
                    <p className="text-gray-600">
                      {feature.description}
                    </p>
                  </div>
                </div>
              ))}
            </motion.div>

            <motion.div variants={itemVariants}>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                Naše hodnoty
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {values.map((value, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <CheckCircle className="w-5 h-5 text-primary-500 flex-shrink-0" />
                    <span className="text-gray-700">{value}</span>
                  </div>
                ))}
              </div>
            </motion.div>
          </motion.div>

          {/* Right Content - Images */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="relative"
          >
            {/* Main Image */}
            <div className="relative z-10 rounded-2xl overflow-hidden shadow-2xl">
              <img
                src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80"
                alt="Training Session"
                className="w-full h-96 object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
            </div>

            {/* Floating Card */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.4, duration: 0.6 }}
              className="absolute -bottom-8 -left-8 bg-white rounded-xl shadow-xl p-6 border border-gray-100"
            >
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-primary-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-lg">15+</span>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Let zkušeností</p>
                  <p className="font-semibold text-gray-900">v bojových uměních</p>
                </div>
              </div>
            </motion.div>

            {/* Background Elements */}
            <div className="absolute -top-4 -right-4 w-24 h-24 bg-primary-500/10 rounded-full blur-xl"></div>
            <div className="absolute -bottom-4 -right-4 w-32 h-32 bg-primary-400/5 rounded-full blur-2xl"></div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default About
