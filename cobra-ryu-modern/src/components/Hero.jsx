import { motion } from 'framer-motion'
import { <PERSON> } from 'react-router-dom'
import { Play, ArrowRight, Shield, Target, Users } from 'lucide-react'

const Hero = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  }

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut"
      }
    }
  }

  const floatingVariants = {
    animate: {
      y: [-10, 10, -10],
      transition: {
        duration: 6,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  }

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Image with Overlay */}
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-black/80 via-primary-900/60 to-black/90 z-10"></div>
        <img
          src="https://images.unsplash.com/photo-1544717297-fa95b6ee9643?ixlib=rb-4.0.3&auto=format&fit=crop&w=2069&q=80"
          alt="Martial Arts Training"
          className="w-full h-full object-cover"
        />
      </div>

      {/* Floating Elements */}
      <motion.div
        variants={floatingVariants}
        animate="animate"
        className="absolute top-20 left-10 w-20 h-20 bg-primary-500/20 rounded-full blur-xl"
      />
      <motion.div
        variants={floatingVariants}
        animate="animate"
        className="absolute bottom-32 right-16 w-32 h-32 bg-primary-400/10 rounded-full blur-2xl"
        style={{ animationDelay: '2s' }}
      />

      {/* Content */}
      <div className="relative z-20 container-custom text-center">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="max-w-5xl mx-auto"
        >
          {/* Badge */}
          <motion.div
            variants={itemVariants}
            className="inline-flex items-center px-4 py-2 bg-primary-500/20 backdrop-blur-sm border border-primary-400/30 rounded-full text-primary-300 text-sm font-medium mb-8"
          >
            <Shield className="w-4 h-4 mr-2" />
            Moderní škola bojových umění
          </motion.div>

          {/* Main Heading */}
          <motion.h1
            variants={itemVariants}
            className="text-5xl md:text-7xl lg:text-8xl font-display font-bold text-white mb-6 leading-tight"
          >
            COBRA
            <span className="block text-gradient">RYU</span>
            <span className="block text-2xl md:text-3xl lg:text-4xl font-normal text-gray-300 mt-4">
              Strakonice
            </span>
          </motion.h1>

          {/* Subtitle */}
          <motion.p
            variants={itemVariants}
            className="text-xl md:text-2xl text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed"
          >
            Objevte sílu, disciplínu a sebevědomí prostřednictvím 
            <span className="text-primary-400 font-semibold"> MMA</span>,
            <span className="text-primary-400 font-semibold"> Allkampf-Jitsu</span> a
            <span className="text-primary-400 font-semibold"> Karate</span>. 
            Připojte se k naší komunitě bojovníků.
          </motion.p>

          {/* CTA Buttons */}
          <motion.div
            variants={itemVariants}
            className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16"
          >
            <Link
              to="/contact"
              className="group btn-primary text-lg px-10 py-4 flex items-center"
            >
              Přidej se k nám
              <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-200" />
            </Link>
            
            <button className="group btn-secondary text-lg px-10 py-4 flex items-center">
              <Play className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-200" />
              Sleduj video
            </button>
          </motion.div>

          {/* Features */}
          <motion.div
            variants={itemVariants}
            className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto"
          >
            <div className="glass-effect p-6 text-center group hover:bg-white/20 transition-all duration-300">
              <Target className="w-8 h-8 text-primary-400 mx-auto mb-4 group-hover:scale-110 transition-transform duration-300" />
              <h3 className="text-white font-semibold text-lg mb-2">Profesionální výuka</h3>
              <p className="text-gray-300 text-sm">Zkušení trenéři s mezinárodními certifikáty</p>
            </div>
            
            <div className="glass-effect p-6 text-center group hover:bg-white/20 transition-all duration-300">
              <Users className="w-8 h-8 text-primary-400 mx-auto mb-4 group-hover:scale-110 transition-transform duration-300" />
              <h3 className="text-white font-semibold text-lg mb-2">Všechny věkové kategorie</h3>
              <p className="text-gray-300 text-sm">Od dětí po dospělé, každý najde svou cestu</p>
            </div>
            
            <div className="glass-effect p-6 text-center group hover:bg-white/20 transition-all duration-300">
              <Shield className="w-8 h-8 text-primary-400 mx-auto mb-4 group-hover:scale-110 transition-transform duration-300" />
              <h3 className="text-white font-semibold text-lg mb-2">Moderní vybavení</h3>
              <p className="text-gray-300 text-sm">Nejnovější tréninkové pomůcky a bezpečnost</p>
            </div>
          </motion.div>
        </motion.div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.5, duration: 0.8 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <div className="flex flex-col items-center text-white/60">
          <span className="text-sm mb-2">Scroll dolů</span>
          <motion.div
            animate={{ y: [0, 8, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center"
          >
            <div className="w-1 h-3 bg-white/60 rounded-full mt-2"></div>
          </motion.div>
        </div>
      </motion.div>
    </section>
  )
}

export default Hero
