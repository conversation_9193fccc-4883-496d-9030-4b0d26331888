import { motion } from 'framer-motion'
import { <PERSON> } from 'react-router-dom'
import { Zap, Facebook, Instagram, Youtube, Mail, Phone, MapPin, ArrowUp } from 'lucide-react'

const Footer = () => {
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  const footerLinks = {
    company: [
      { name: 'O nás', path: '/about' },
      { name: '<PERSON><PERSON><PERSON> tren<PERSON>', path: '/trainers' },
      { name: 'Galerie', path: '/gallery' },
      { name: '<PERSON>nta<PERSON>', path: '/contact' }
    ],
    programs: [
      { name: 'MMA Začátečníci', path: '/training' },
      { name: 'MMA Pokročilí', path: '/training' },
      { name: 'Allkampf-Jitsu', path: '/training' },
      { name: 'Dětské MMA', path: '/training' },
      { name: '<PERSON><PERSON>', path: '/training' },
      { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', path: '/training' }
    ],
    support: [
      { name: '<PERSON><PERSON><PERSON>', path: '/faq' },
      { name: '<PERSON><PERSON><PERSON>', path: '/pricing' },
      { name: '<PERSON><PERSON><PERSON><PERSON> dojo', path: '/rules' },
      { name: 'Bezpečnost', path: '/safety' }
    ]
  }

  const socialLinks = [
    { icon: Facebook, href: '#', label: 'Facebook' },
    { icon: Instagram, href: '#', label: 'Instagram' },
    { icon: Youtube, href: '#', label: 'YouTube' }
  ]

  return (
    <footer className="bg-gray-900 text-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-0 left-0 w-96 h-96 bg-primary-500 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-primary-400 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10">
        {/* Main Footer Content */}
        <div className="container-custom py-16">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-12">
            {/* Brand Section */}
            <div className="lg:col-span-1">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
              >
                <Link to="/" className="flex items-center space-x-3 mb-6">
                  <div className="relative">
                    <Zap className="w-10 h-10 text-primary-500" />
                    <div className="absolute inset-0 bg-primary-500 rounded-full blur-lg opacity-30"></div>
                  </div>
                  <div>
                    <span className="font-display font-bold text-2xl">COBRA RYU</span>
                    <span className="block text-sm text-gray-400">STRAKONICE</span>
                  </div>
                </Link>
                
                <p className="text-gray-400 mb-6 leading-relaxed">
                  Moderní škola bojových umění v srdci Strakonic. 
                  Objevte sílu, disciplínu a sebevědomí prostřednictvím 
                  našich specializovaných programů.
                </p>

                {/* Contact Info */}
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <MapPin className="w-5 h-5 text-primary-500 flex-shrink-0" />
                    <span className="text-gray-400 text-sm">Strakonice 386 01</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Phone className="w-5 h-5 text-primary-500 flex-shrink-0" />
                    <span className="text-gray-400 text-sm">+420 777 123 456</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Mail className="w-5 h-5 text-primary-500 flex-shrink-0" />
                    <span className="text-gray-400 text-sm"><EMAIL></span>
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Links Sections */}
            <div className="lg:col-span-3">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {/* Company Links */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: 0.1 }}
                >
                  <h3 className="font-semibold text-lg mb-6">Klub</h3>
                  <ul className="space-y-3">
                    {footerLinks.company.map((link, index) => (
                      <li key={index}>
                        <Link
                          to={link.path}
                          className="text-gray-400 hover:text-primary-400 transition-colors duration-200 text-sm"
                        >
                          {link.name}
                        </Link>
                      </li>
                    ))}
                  </ul>
                </motion.div>

                {/* Programs Links */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                >
                  <h3 className="font-semibold text-lg mb-6">Programy</h3>
                  <ul className="space-y-3">
                    {footerLinks.programs.map((link, index) => (
                      <li key={index}>
                        <Link
                          to={link.path}
                          className="text-gray-400 hover:text-primary-400 transition-colors duration-200 text-sm"
                        >
                          {link.name}
                        </Link>
                      </li>
                    ))}
                  </ul>
                </motion.div>

                {/* Support Links */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: 0.3 }}
                >
                  <h3 className="font-semibold text-lg mb-6">Podpora</h3>
                  <ul className="space-y-3 mb-6">
                    {footerLinks.support.map((link, index) => (
                      <li key={index}>
                        <Link
                          to={link.path}
                          className="text-gray-400 hover:text-primary-400 transition-colors duration-200 text-sm"
                        >
                          {link.name}
                        </Link>
                      </li>
                    ))}
                  </ul>

                  {/* Social Links */}
                  <div>
                    <h4 className="font-medium mb-4">Sledujte nás</h4>
                    <div className="flex space-x-4">
                      {socialLinks.map((social, index) => (
                        <motion.a
                          key={index}
                          href={social.href}
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-primary-600 transition-colors duration-200"
                          aria-label={social.label}
                        >
                          <social.icon className="w-5 h-5" />
                        </motion.a>
                      ))}
                    </div>
                  </div>
                </motion.div>
              </div>
            </div>
          </div>
        </div>

        {/* Newsletter Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="border-t border-gray-800"
        >
          <div className="container-custom py-8">
            <div className="flex flex-col md:flex-row items-center justify-between">
              <div className="mb-6 md:mb-0">
                <h3 className="text-xl font-semibold mb-2">Zůstaňte v obraze</h3>
                <p className="text-gray-400">
                  Přihlaste se k odběru novinek a získejte nejnovější informace o našich programech.
                </p>
              </div>
              <div className="flex w-full md:w-auto">
                <input
                  type="email"
                  placeholder="Váš email"
                  className="flex-1 md:w-64 px-4 py-3 bg-gray-800 border border-gray-700 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="px-6 py-3 bg-primary-600 hover:bg-primary-700 rounded-r-lg transition-colors duration-200"
                >
                  Odebírat
                </motion.button>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800">
          <div className="container-custom py-6">
            <div className="flex flex-col md:flex-row items-center justify-between">
              <p className="text-gray-400 text-sm mb-4 md:mb-0">
                &copy; {new Date().getFullYear()} COBRA RYU Strakonice. Všechna práva vyhrazena.
              </p>
              
              <div className="flex items-center space-x-6">
                <Link to="/privacy" className="text-gray-400 hover:text-primary-400 text-sm transition-colors duration-200">
                  Ochrana soukromí
                </Link>
                <Link to="/terms" className="text-gray-400 hover:text-primary-400 text-sm transition-colors duration-200">
                  Podmínky použití
                </Link>
                
                {/* Scroll to Top Button */}
                <motion.button
                  onClick={scrollToTop}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  className="w-10 h-10 bg-primary-600 hover:bg-primary-700 rounded-lg flex items-center justify-center transition-colors duration-200"
                  aria-label="Scroll to top"
                >
                  <ArrowUp className="w-5 h-5" />
                </motion.button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
