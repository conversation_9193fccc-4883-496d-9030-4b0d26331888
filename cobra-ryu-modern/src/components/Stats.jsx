import { motion } from 'framer-motion'
import { useInView } from 'framer-motion'
import { useRef, useEffect, useState } from 'react'
import { Trophy, Users, Clock, Star } from 'lucide-react'

const CountUp = ({ end, duration = 2 }) => {
  const [count, setCount] = useState(0)
  const [hasAnimated, setHasAnimated] = useState(false)
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true })

  useEffect(() => {
    if (isInView && !hasAnimated) {
      setHasAnimated(true)
      let startTime = null
      const animate = (currentTime) => {
        if (startTime === null) startTime = currentTime
        const progress = Math.min((currentTime - startTime) / (duration * 1000), 1)
        
        setCount(Math.floor(progress * end))
        
        if (progress < 1) {
          requestAnimationFrame(animate)
        }
      }
      requestAnimationFrame(animate)
    }
  }, [isInView, end, duration, hasAnimated])

  return <span ref={ref}>{count}</span>
}

const Stats = () => {
  const stats = [
    {
      icon: Users,
      number: 500,
      suffix: '+',
      label: 'Aktivních členů',
      description: 'Rostoucí komunita bojovníků'
    },
    {
      icon: Trophy,
      number: 15,
      suffix: '+',
      label: 'Let zkušeností',
      description: 'Tradice a odbornost'
    },
    {
      icon: Star,
      number: 50,
      suffix: '+',
      label: 'Turnajových vítězství',
      description: 'Úspěchy našich členů'
    },
    {
      icon: Clock,
      number: 24,
      suffix: '/7',
      label: 'Podpora komunity',
      description: 'Vždy tu pro vás jsme'
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  }

  return (
    <section className="section-padding bg-gradient-to-br from-gray-50 to-white">
      <div className="container-custom">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
        >
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="group relative"
            >
              <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 group-hover:border-primary-200">
                {/* Background Pattern */}
                <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-primary-500/10 to-primary-600/5 rounded-bl-2xl"></div>
                
                {/* Icon */}
                <div className="relative z-10 mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <stat.icon className="w-8 h-8 text-white" />
                  </div>
                </div>

                {/* Number */}
                <div className="relative z-10 mb-2">
                  <span className="text-4xl lg:text-5xl font-bold text-gray-900 font-display">
                    <CountUp end={stat.number} />
                    <span className="text-primary-600">{stat.suffix}</span>
                  </span>
                </div>

                {/* Label */}
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {stat.label}
                </h3>

                {/* Description */}
                <p className="text-gray-600 text-sm leading-relaxed">
                  {stat.description}
                </p>

                {/* Hover Effect */}
                <div className="absolute inset-0 bg-gradient-to-br from-primary-500/5 to-primary-600/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.8, duration: 0.6 }}
          className="text-center mt-16"
        >
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Připraveni začít svou cestu?
          </h3>
          <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
            Přidejte se k naší rostoucí komunitě a objevte, co ve vás dřímá. 
            První trénink je zdarma!
          </p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="btn-primary"
          >
            Zkusit zdarma
          </motion.button>
        </motion.div>
      </div>
    </section>
  )
}

export default Stats
