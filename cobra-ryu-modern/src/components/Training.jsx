import { motion } from 'framer-motion'
import { Clock, Users, Star, Calendar } from 'lucide-react'

const Training = () => {
  const programs = [
    {
      name: 'MMA Začátečníci',
      description: 'Základy smíšených bojových umění pro úplné začátečníky',
      level: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      age: '16+ let',
      duration: '90 min',
      schedule: 'Po, St 18:00-19:30',
      price: '1200 Kč/měsíc',
      features: ['Základní techniky', 'Kondičn<PERSON> příprava', 'Bezpečnost', 'Základy grappingu'],
      color: 'from-blue-500 to-blue-600'
    },
    {
      name: 'MMA Pokročilí',
      description: 'Pokročilé techniky a sparring pro zkušené bojovníky',
      level: 'Pokro<PERSON>ilý',
      age: '18+ let',
      duration: '90 min',
      schedule: 'Po, St 19:30-21:00',
      price: '1400 Kč/měsíc',
      features: ['Pokro<PERSON>ilé techniky', 'Sparring', '<PERSON><PERSON><PERSON> příprava', 'Individu<PERSON>ln<PERSON> k<PERSON>'],
      color: 'from-red-500 to-red-600'
    },
    {
      name: 'Allkampf-Jitsu',
      description: 'Moderní sebeobrana a praktické bojové techniky',
      level: 'Všechny úrovně',
      age: '14+ let',
      duration: '75 min',
      schedule: 'Út, Čt 19:00-20:15',
      price: '1100 Kč/měsíc',
      features: ['Sebeobrana', 'Praktické techniky', 'Mentální příprava', 'Situační výcvik'],
      color: 'from-green-500 to-green-600'
    },
    {
      name: 'Dětské MMA',
      description: 'Bojové umění pro děti - zábavnou a bezpečnou formou',
      level: 'Začátečník',
      age: '6-14 let',
      duration: '60 min',
      schedule: 'Út, Čt 17:00-18:00',
      price: '800 Kč/měsíc',
      features: ['Herní forma', 'Základní techniky', 'Disciplína', 'Sebevědomí'],
      color: 'from-purple-500 to-purple-600'
    },
    {
      name: 'Karate Kyokushin',
      description: 'Tradiční karate s důrazem na sílu a vytrvalost',
      level: 'Všechny úrovně',
      age: '12+ let',
      duration: '75 min',
      schedule: 'Pá 18:00-19:15',
      price: '1000 Kč/měsíc',
      features: ['Tradiční techniky', 'Kata', 'Kumite', 'Filozofie'],
      color: 'from-orange-500 to-orange-600'
    },
    {
      name: 'Kondiční trénink',
      description: 'Funkční trénink a příprava kondice pro bojovníky',
      level: 'Všechny úrovně',
      age: '16+ let',
      duration: '60 min',
      schedule: 'Pá 19:30-20:30',
      price: '600 Kč/měsíc',
      features: ['Funkční cvičení', 'Kardio', 'Síla', 'Flexibilita'],
      color: 'from-gray-500 to-gray-600'
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const cardVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  }

  return (
    <section id="training" className="section-padding bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="container-custom">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <span className="inline-block px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-medium mb-4">
            Naše programy
          </span>
          <h2 className="text-4xl lg:text-5xl font-display font-bold text-gray-900 mb-6">
            Rozvrh
            <span className="text-gradient block">tréninků</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Vyberte si z našich specializovaných programů. Každý trénink je navržen 
            tak, aby vás posunul na vyšší úroveň.
          </p>
        </motion.div>

        {/* Training Cards */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {programs.map((program, index) => (
            <motion.div
              key={index}
              variants={cardVariants}
              className="group relative bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden"
            >
              {/* Header with gradient */}
              <div className={`bg-gradient-to-r ${program.color} p-6 text-white relative overflow-hidden`}>
                <div className="absolute top-0 right-0 w-20 h-20 bg-white/10 rounded-full -translate-y-10 translate-x-10"></div>
                <div className="relative z-10">
                  <h3 className="text-xl font-bold mb-2">{program.name}</h3>
                  <p className="text-white/90 text-sm">{program.description}</p>
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                {/* Info Grid */}
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="flex items-center space-x-2">
                    <Star className="w-4 h-4 text-gray-400" />
                    <span className="text-sm text-gray-600">{program.level}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Users className="w-4 h-4 text-gray-400" />
                    <span className="text-sm text-gray-600">{program.age}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-gray-400" />
                    <span className="text-sm text-gray-600">{program.duration}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-4 h-4 text-gray-400" />
                    <span className="text-sm text-gray-600">{program.schedule}</span>
                  </div>
                </div>

                {/* Features */}
                <div className="mb-6">
                  <h4 className="font-semibold text-gray-900 mb-3">Co se naučíte:</h4>
                  <ul className="space-y-2">
                    {program.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-primary-500 rounded-full"></div>
                        <span className="text-sm text-gray-600">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Price and CTA */}
                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-2xl font-bold text-gray-900">{program.price}</span>
                  </div>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="btn-outline text-sm px-4 py-2"
                  >
                    Přihlásit se
                  </motion.button>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.4, duration: 0.6 }}
          className="text-center mt-16 bg-white rounded-2xl p-8 shadow-lg"
        >
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            První trénink zdarma!
          </h3>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Nejste si jisti, který program je pro vás ten pravý? Přijďte si vyzkoušet 
            jakýkoliv trénink zdarma a poznejte naši komunitu.
          </p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="btn-primary"
          >
            Rezervovat zkušební trénink
          </motion.button>
        </motion.div>
      </div>
    </section>
  )
}

export default Training
