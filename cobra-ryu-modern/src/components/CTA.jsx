import { motion } from 'framer-motion'
import { Link } from 'react-router-dom'
import { ArrowR<PERSON>, Phone, MapPin, Clock } from 'lucide-react'

const CTA = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  }

  return (
    <section className="section-padding bg-gradient-to-br from-primary-900 via-primary-800 to-primary-900 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-96 h-96 bg-primary-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-primary-400/10 rounded-full blur-3xl"></div>
      </div>

      <div className="container-custom relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          className="text-center"
        >
          {/* Main CTA */}
          <motion.div variants={itemVariants} className="mb-16">
            <h2 className="text-4xl lg:text-6xl font-display font-bold text-white mb-6">
              Připraveni začít svou
              <span className="block text-primary-300">cestu bojovníka?</span>
            </h2>
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
              Přidejte se k naší komunitě a objevte sílu, kterou v sobě máte. 
              První trénink je zdarma a bez závazků!
            </p>
            
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <Link
                to="/contact"
                className="group btn-secondary text-lg px-10 py-4 flex items-center"
              >
                Začít hned teď
                <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-200" />
              </Link>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="text-white border-2 border-white/30 hover:border-white/50 font-semibold py-4 px-10 rounded-lg transition-all duration-200 backdrop-blur-sm"
              >
                Zobrazit rozvrh
              </motion.button>
            </div>
          </motion.div>

          {/* Quick Info Cards */}
          <motion.div
            variants={containerVariants}
            className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto"
          >
            <motion.div
              variants={itemVariants}
              className="glass-effect p-6 text-center group hover:bg-white/20 transition-all duration-300"
            >
              <div className="w-16 h-16 bg-primary-500/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary-500/30 transition-colors duration-300">
                <MapPin className="w-8 h-8 text-primary-300" />
              </div>
              <h3 className="text-white font-semibold text-lg mb-2">Kde nás najdete</h3>
              <p className="text-gray-300 text-sm">
                Sportovní hala Strakonice<br />
                Strakonice 386 01
              </p>
            </motion.div>

            <motion.div
              variants={itemVariants}
              className="glass-effect p-6 text-center group hover:bg-white/20 transition-all duration-300"
            >
              <div className="w-16 h-16 bg-primary-500/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary-500/30 transition-colors duration-300">
                <Clock className="w-8 h-8 text-primary-300" />
              </div>
              <h3 className="text-white font-semibold text-lg mb-2">Otevírací doba</h3>
              <p className="text-gray-300 text-sm">
                Po-Čt: 17:00-21:00<br />
                Pá: 18:00-20:00
              </p>
            </motion.div>

            <motion.div
              variants={itemVariants}
              className="glass-effect p-6 text-center group hover:bg-white/20 transition-all duration-300"
            >
              <div className="w-16 h-16 bg-primary-500/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary-500/30 transition-colors duration-300">
                <Phone className="w-8 h-8 text-primary-300" />
              </div>
              <h3 className="text-white font-semibold text-lg mb-2">Kontakt</h3>
              <p className="text-gray-300 text-sm">
                +420 777 123 456<br />
                <EMAIL>
              </p>
            </motion.div>
          </motion.div>

          {/* Special Offer */}
          <motion.div
            variants={itemVariants}
            className="mt-16 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-8 max-w-2xl mx-auto"
          >
            <div className="text-center">
              <div className="inline-flex items-center px-4 py-2 bg-primary-500 text-white rounded-full text-sm font-medium mb-4">
                🎯 Speciální nabídka
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">
                První měsíc za polovinu!
              </h3>
              <p className="text-gray-300 mb-6">
                Pro nové členy nabízíme první měsíc tréninků za 50% cenu. 
                Platí pro všechny naše programy.
              </p>
              <div className="flex items-center justify-center space-x-4 text-sm text-gray-300">
                <span>✓ Bez vstupních poplatků</span>
                <span>✓ Včetně výpůjčky vybavení</span>
                <span>✓ Osobní konzultace zdarma</span>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}

export default CTA
