(function(){const i=document.createElement("link").relList;if(i&&i.supports&&i.supports("modulepreload"))return;for(const c of document.querySelectorAll('link[rel="modulepreload"]'))r(c);new MutationObserver(c=>{for(const d of c)if(d.type==="childList")for(const f of d.addedNodes)f.tagName==="LINK"&&f.rel==="modulepreload"&&r(f)}).observe(document,{childList:!0,subtree:!0});function s(c){const d={};return c.integrity&&(d.integrity=c.integrity),c.referrerPolicy&&(d.referrerPolicy=c.referrerPolicy),c.crossOrigin==="use-credentials"?d.credentials="include":c.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function r(c){if(c.ep)return;c.ep=!0;const d=s(c);fetch(c.href,d)}})();var Ku={exports:{}},pl={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var yp;function x1(){if(yp)return pl;yp=1;var a=Symbol.for("react.transitional.element"),i=Symbol.for("react.fragment");function s(r,c,d){var f=null;if(d!==void 0&&(f=""+d),c.key!==void 0&&(f=""+c.key),"key"in c){d={};for(var p in c)p!=="key"&&(d[p]=c[p])}else d=c;return c=d.ref,{$$typeof:a,type:r,key:f,ref:c!==void 0?c:null,props:d}}return pl.Fragment=i,pl.jsx=s,pl.jsxs=s,pl}var gp;function b1(){return gp||(gp=1,Ku.exports=x1()),Ku.exports}var g=b1(),Xu={exports:{}},ot={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var vp;function S1(){if(vp)return ot;vp=1;var a=Symbol.for("react.transitional.element"),i=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),r=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),d=Symbol.for("react.consumer"),f=Symbol.for("react.context"),p=Symbol.for("react.forward_ref"),m=Symbol.for("react.suspense"),h=Symbol.for("react.memo"),v=Symbol.for("react.lazy"),b=Symbol.iterator;function S(A){return A===null||typeof A!="object"?null:(A=b&&A[b]||A["@@iterator"],typeof A=="function"?A:null)}var E={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},M=Object.assign,V={};function _(A,G,Q){this.props=A,this.context=G,this.refs=V,this.updater=Q||E}_.prototype.isReactComponent={},_.prototype.setState=function(A,G){if(typeof A!="object"&&typeof A!="function"&&A!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,A,G,"setState")},_.prototype.forceUpdate=function(A){this.updater.enqueueForceUpdate(this,A,"forceUpdate")};function C(){}C.prototype=_.prototype;function q(A,G,Q){this.props=A,this.context=G,this.refs=V,this.updater=Q||E}var L=q.prototype=new C;L.constructor=q,M(L,_.prototype),L.isPureReactComponent=!0;var P=Array.isArray,Y={H:null,A:null,T:null,S:null,V:null},$=Object.prototype.hasOwnProperty;function at(A,G,Q,X,tt,mt){return Q=mt.ref,{$$typeof:a,type:A,key:G,ref:Q!==void 0?Q:null,props:mt}}function F(A,G){return at(A.type,G,void 0,void 0,void 0,A.props)}function rt(A){return typeof A=="object"&&A!==null&&A.$$typeof===a}function vt(A){var G={"=":"=0",":":"=2"};return"$"+A.replace(/[=:]/g,function(Q){return G[Q]})}var Yt=/\/+/g;function Gt(A,G){return typeof A=="object"&&A!==null&&A.key!=null?vt(""+A.key):G.toString(36)}function We(){}function He(A){switch(A.status){case"fulfilled":return A.value;case"rejected":throw A.reason;default:switch(typeof A.status=="string"?A.then(We,We):(A.status="pending",A.then(function(G){A.status==="pending"&&(A.status="fulfilled",A.value=G)},function(G){A.status==="pending"&&(A.status="rejected",A.reason=G)})),A.status){case"fulfilled":return A.value;case"rejected":throw A.reason}}throw A}function $t(A,G,Q,X,tt){var mt=typeof A;(mt==="undefined"||mt==="boolean")&&(A=null);var st=!1;if(A===null)st=!0;else switch(mt){case"bigint":case"string":case"number":st=!0;break;case"object":switch(A.$$typeof){case a:case i:st=!0;break;case v:return st=A._init,$t(st(A._payload),G,Q,X,tt)}}if(st)return tt=tt(A),st=X===""?"."+Gt(A,0):X,P(tt)?(Q="",st!=null&&(Q=st.replace(Yt,"$&/")+"/"),$t(tt,G,Q,"",function(bn){return bn})):tt!=null&&(rt(tt)&&(tt=F(tt,Q+(tt.key==null||A&&A.key===tt.key?"":(""+tt.key).replace(Yt,"$&/")+"/")+st)),G.push(tt)),1;st=0;var me=X===""?".":X+":";if(P(A))for(var wt=0;wt<A.length;wt++)X=A[wt],mt=me+Gt(X,wt),st+=$t(X,G,Q,mt,tt);else if(wt=S(A),typeof wt=="function")for(A=wt.call(A),wt=0;!(X=A.next()).done;)X=X.value,mt=me+Gt(X,wt++),st+=$t(X,G,Q,mt,tt);else if(mt==="object"){if(typeof A.then=="function")return $t(He(A),G,Q,X,tt);throw G=String(A),Error("Objects are not valid as a React child (found: "+(G==="[object Object]"?"object with keys {"+Object.keys(A).join(", ")+"}":G)+"). If you meant to render a collection of children, use an array instead.")}return st}function k(A,G,Q){if(A==null)return A;var X=[],tt=0;return $t(A,X,"","",function(mt){return G.call(Q,mt,tt++)}),X}function K(A){if(A._status===-1){var G=A._result;G=G(),G.then(function(Q){(A._status===0||A._status===-1)&&(A._status=1,A._result=Q)},function(Q){(A._status===0||A._status===-1)&&(A._status=2,A._result=Q)}),A._status===-1&&(A._status=0,A._result=G)}if(A._status===1)return A._result.default;throw A._result}var W=typeof reportError=="function"?reportError:function(A){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var G=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof A=="object"&&A!==null&&typeof A.message=="string"?String(A.message):String(A),error:A});if(!window.dispatchEvent(G))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",A);return}console.error(A)};function yt(){}return ot.Children={map:k,forEach:function(A,G,Q){k(A,function(){G.apply(this,arguments)},Q)},count:function(A){var G=0;return k(A,function(){G++}),G},toArray:function(A){return k(A,function(G){return G})||[]},only:function(A){if(!rt(A))throw Error("React.Children.only expected to receive a single React element child.");return A}},ot.Component=_,ot.Fragment=s,ot.Profiler=c,ot.PureComponent=q,ot.StrictMode=r,ot.Suspense=m,ot.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=Y,ot.__COMPILER_RUNTIME={__proto__:null,c:function(A){return Y.H.useMemoCache(A)}},ot.cache=function(A){return function(){return A.apply(null,arguments)}},ot.cloneElement=function(A,G,Q){if(A==null)throw Error("The argument must be a React element, but you passed "+A+".");var X=M({},A.props),tt=A.key,mt=void 0;if(G!=null)for(st in G.ref!==void 0&&(mt=void 0),G.key!==void 0&&(tt=""+G.key),G)!$.call(G,st)||st==="key"||st==="__self"||st==="__source"||st==="ref"&&G.ref===void 0||(X[st]=G[st]);var st=arguments.length-2;if(st===1)X.children=Q;else if(1<st){for(var me=Array(st),wt=0;wt<st;wt++)me[wt]=arguments[wt+2];X.children=me}return at(A.type,tt,void 0,void 0,mt,X)},ot.createContext=function(A){return A={$$typeof:f,_currentValue:A,_currentValue2:A,_threadCount:0,Provider:null,Consumer:null},A.Provider=A,A.Consumer={$$typeof:d,_context:A},A},ot.createElement=function(A,G,Q){var X,tt={},mt=null;if(G!=null)for(X in G.key!==void 0&&(mt=""+G.key),G)$.call(G,X)&&X!=="key"&&X!=="__self"&&X!=="__source"&&(tt[X]=G[X]);var st=arguments.length-2;if(st===1)tt.children=Q;else if(1<st){for(var me=Array(st),wt=0;wt<st;wt++)me[wt]=arguments[wt+2];tt.children=me}if(A&&A.defaultProps)for(X in st=A.defaultProps,st)tt[X]===void 0&&(tt[X]=st[X]);return at(A,mt,void 0,void 0,null,tt)},ot.createRef=function(){return{current:null}},ot.forwardRef=function(A){return{$$typeof:p,render:A}},ot.isValidElement=rt,ot.lazy=function(A){return{$$typeof:v,_payload:{_status:-1,_result:A},_init:K}},ot.memo=function(A,G){return{$$typeof:h,type:A,compare:G===void 0?null:G}},ot.startTransition=function(A){var G=Y.T,Q={};Y.T=Q;try{var X=A(),tt=Y.S;tt!==null&&tt(Q,X),typeof X=="object"&&X!==null&&typeof X.then=="function"&&X.then(yt,W)}catch(mt){W(mt)}finally{Y.T=G}},ot.unstable_useCacheRefresh=function(){return Y.H.useCacheRefresh()},ot.use=function(A){return Y.H.use(A)},ot.useActionState=function(A,G,Q){return Y.H.useActionState(A,G,Q)},ot.useCallback=function(A,G){return Y.H.useCallback(A,G)},ot.useContext=function(A){return Y.H.useContext(A)},ot.useDebugValue=function(){},ot.useDeferredValue=function(A,G){return Y.H.useDeferredValue(A,G)},ot.useEffect=function(A,G,Q){var X=Y.H;if(typeof Q=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return X.useEffect(A,G)},ot.useId=function(){return Y.H.useId()},ot.useImperativeHandle=function(A,G,Q){return Y.H.useImperativeHandle(A,G,Q)},ot.useInsertionEffect=function(A,G){return Y.H.useInsertionEffect(A,G)},ot.useLayoutEffect=function(A,G){return Y.H.useLayoutEffect(A,G)},ot.useMemo=function(A,G){return Y.H.useMemo(A,G)},ot.useOptimistic=function(A,G){return Y.H.useOptimistic(A,G)},ot.useReducer=function(A,G,Q){return Y.H.useReducer(A,G,Q)},ot.useRef=function(A){return Y.H.useRef(A)},ot.useState=function(A){return Y.H.useState(A)},ot.useSyncExternalStore=function(A,G,Q){return Y.H.useSyncExternalStore(A,G,Q)},ot.useTransition=function(){return Y.H.useTransition()},ot.version="19.1.0",ot}var xp;function zc(){return xp||(xp=1,Xu.exports=S1()),Xu.exports}var w=zc(),Zu={exports:{}},yl={},Qu={exports:{}},Pu={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var bp;function T1(){return bp||(bp=1,function(a){function i(k,K){var W=k.length;k.push(K);t:for(;0<W;){var yt=W-1>>>1,A=k[yt];if(0<c(A,K))k[yt]=K,k[W]=A,W=yt;else break t}}function s(k){return k.length===0?null:k[0]}function r(k){if(k.length===0)return null;var K=k[0],W=k.pop();if(W!==K){k[0]=W;t:for(var yt=0,A=k.length,G=A>>>1;yt<G;){var Q=2*(yt+1)-1,X=k[Q],tt=Q+1,mt=k[tt];if(0>c(X,W))tt<A&&0>c(mt,X)?(k[yt]=mt,k[tt]=W,yt=tt):(k[yt]=X,k[Q]=W,yt=Q);else if(tt<A&&0>c(mt,W))k[yt]=mt,k[tt]=W,yt=tt;else break t}}return K}function c(k,K){var W=k.sortIndex-K.sortIndex;return W!==0?W:k.id-K.id}if(a.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var d=performance;a.unstable_now=function(){return d.now()}}else{var f=Date,p=f.now();a.unstable_now=function(){return f.now()-p}}var m=[],h=[],v=1,b=null,S=3,E=!1,M=!1,V=!1,_=!1,C=typeof setTimeout=="function"?setTimeout:null,q=typeof clearTimeout=="function"?clearTimeout:null,L=typeof setImmediate<"u"?setImmediate:null;function P(k){for(var K=s(h);K!==null;){if(K.callback===null)r(h);else if(K.startTime<=k)r(h),K.sortIndex=K.expirationTime,i(m,K);else break;K=s(h)}}function Y(k){if(V=!1,P(k),!M)if(s(m)!==null)M=!0,$||($=!0,Gt());else{var K=s(h);K!==null&&$t(Y,K.startTime-k)}}var $=!1,at=-1,F=5,rt=-1;function vt(){return _?!0:!(a.unstable_now()-rt<F)}function Yt(){if(_=!1,$){var k=a.unstable_now();rt=k;var K=!0;try{t:{M=!1,V&&(V=!1,q(at),at=-1),E=!0;var W=S;try{e:{for(P(k),b=s(m);b!==null&&!(b.expirationTime>k&&vt());){var yt=b.callback;if(typeof yt=="function"){b.callback=null,S=b.priorityLevel;var A=yt(b.expirationTime<=k);if(k=a.unstable_now(),typeof A=="function"){b.callback=A,P(k),K=!0;break e}b===s(m)&&r(m),P(k)}else r(m);b=s(m)}if(b!==null)K=!0;else{var G=s(h);G!==null&&$t(Y,G.startTime-k),K=!1}}break t}finally{b=null,S=W,E=!1}K=void 0}}finally{K?Gt():$=!1}}}var Gt;if(typeof L=="function")Gt=function(){L(Yt)};else if(typeof MessageChannel<"u"){var We=new MessageChannel,He=We.port2;We.port1.onmessage=Yt,Gt=function(){He.postMessage(null)}}else Gt=function(){C(Yt,0)};function $t(k,K){at=C(function(){k(a.unstable_now())},K)}a.unstable_IdlePriority=5,a.unstable_ImmediatePriority=1,a.unstable_LowPriority=4,a.unstable_NormalPriority=3,a.unstable_Profiling=null,a.unstable_UserBlockingPriority=2,a.unstable_cancelCallback=function(k){k.callback=null},a.unstable_forceFrameRate=function(k){0>k||125<k?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):F=0<k?Math.floor(1e3/k):5},a.unstable_getCurrentPriorityLevel=function(){return S},a.unstable_next=function(k){switch(S){case 1:case 2:case 3:var K=3;break;default:K=S}var W=S;S=K;try{return k()}finally{S=W}},a.unstable_requestPaint=function(){_=!0},a.unstable_runWithPriority=function(k,K){switch(k){case 1:case 2:case 3:case 4:case 5:break;default:k=3}var W=S;S=k;try{return K()}finally{S=W}},a.unstable_scheduleCallback=function(k,K,W){var yt=a.unstable_now();switch(typeof W=="object"&&W!==null?(W=W.delay,W=typeof W=="number"&&0<W?yt+W:yt):W=yt,k){case 1:var A=-1;break;case 2:A=250;break;case 5:A=1073741823;break;case 4:A=1e4;break;default:A=5e3}return A=W+A,k={id:v++,callback:K,priorityLevel:k,startTime:W,expirationTime:A,sortIndex:-1},W>yt?(k.sortIndex=W,i(h,k),s(m)===null&&k===s(h)&&(V?(q(at),at=-1):V=!0,$t(Y,W-yt))):(k.sortIndex=A,i(m,k),M||E||(M=!0,$||($=!0,Gt()))),k},a.unstable_shouldYield=vt,a.unstable_wrapCallback=function(k){var K=S;return function(){var W=S;S=K;try{return k.apply(this,arguments)}finally{S=W}}}}(Pu)),Pu}var Sp;function A1(){return Sp||(Sp=1,Qu.exports=T1()),Qu.exports}var Ju={exports:{}},ae={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Tp;function j1(){if(Tp)return ae;Tp=1;var a=zc();function i(m){var h="https://react.dev/errors/"+m;if(1<arguments.length){h+="?args[]="+encodeURIComponent(arguments[1]);for(var v=2;v<arguments.length;v++)h+="&args[]="+encodeURIComponent(arguments[v])}return"Minified React error #"+m+"; visit "+h+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(){}var r={d:{f:s,r:function(){throw Error(i(522))},D:s,C:s,L:s,m:s,X:s,S:s,M:s},p:0,findDOMNode:null},c=Symbol.for("react.portal");function d(m,h,v){var b=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:c,key:b==null?null:""+b,children:m,containerInfo:h,implementation:v}}var f=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function p(m,h){if(m==="font")return"";if(typeof h=="string")return h==="use-credentials"?h:""}return ae.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=r,ae.createPortal=function(m,h){var v=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!h||h.nodeType!==1&&h.nodeType!==9&&h.nodeType!==11)throw Error(i(299));return d(m,h,null,v)},ae.flushSync=function(m){var h=f.T,v=r.p;try{if(f.T=null,r.p=2,m)return m()}finally{f.T=h,r.p=v,r.d.f()}},ae.preconnect=function(m,h){typeof m=="string"&&(h?(h=h.crossOrigin,h=typeof h=="string"?h==="use-credentials"?h:"":void 0):h=null,r.d.C(m,h))},ae.prefetchDNS=function(m){typeof m=="string"&&r.d.D(m)},ae.preinit=function(m,h){if(typeof m=="string"&&h&&typeof h.as=="string"){var v=h.as,b=p(v,h.crossOrigin),S=typeof h.integrity=="string"?h.integrity:void 0,E=typeof h.fetchPriority=="string"?h.fetchPriority:void 0;v==="style"?r.d.S(m,typeof h.precedence=="string"?h.precedence:void 0,{crossOrigin:b,integrity:S,fetchPriority:E}):v==="script"&&r.d.X(m,{crossOrigin:b,integrity:S,fetchPriority:E,nonce:typeof h.nonce=="string"?h.nonce:void 0})}},ae.preinitModule=function(m,h){if(typeof m=="string")if(typeof h=="object"&&h!==null){if(h.as==null||h.as==="script"){var v=p(h.as,h.crossOrigin);r.d.M(m,{crossOrigin:v,integrity:typeof h.integrity=="string"?h.integrity:void 0,nonce:typeof h.nonce=="string"?h.nonce:void 0})}}else h==null&&r.d.M(m)},ae.preload=function(m,h){if(typeof m=="string"&&typeof h=="object"&&h!==null&&typeof h.as=="string"){var v=h.as,b=p(v,h.crossOrigin);r.d.L(m,v,{crossOrigin:b,integrity:typeof h.integrity=="string"?h.integrity:void 0,nonce:typeof h.nonce=="string"?h.nonce:void 0,type:typeof h.type=="string"?h.type:void 0,fetchPriority:typeof h.fetchPriority=="string"?h.fetchPriority:void 0,referrerPolicy:typeof h.referrerPolicy=="string"?h.referrerPolicy:void 0,imageSrcSet:typeof h.imageSrcSet=="string"?h.imageSrcSet:void 0,imageSizes:typeof h.imageSizes=="string"?h.imageSizes:void 0,media:typeof h.media=="string"?h.media:void 0})}},ae.preloadModule=function(m,h){if(typeof m=="string")if(h){var v=p(h.as,h.crossOrigin);r.d.m(m,{as:typeof h.as=="string"&&h.as!=="script"?h.as:void 0,crossOrigin:v,integrity:typeof h.integrity=="string"?h.integrity:void 0})}else r.d.m(m)},ae.requestFormReset=function(m){r.d.r(m)},ae.unstable_batchedUpdates=function(m,h){return m(h)},ae.useFormState=function(m,h,v){return f.H.useFormState(m,h,v)},ae.useFormStatus=function(){return f.H.useHostTransitionStatus()},ae.version="19.1.0",ae}var Ap;function E1(){if(Ap)return Ju.exports;Ap=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(i){console.error(i)}}return a(),Ju.exports=j1(),Ju.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var jp;function w1(){if(jp)return yl;jp=1;var a=A1(),i=zc(),s=E1();function r(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function c(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function d(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function f(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function p(t){if(d(t)!==t)throw Error(r(188))}function m(t){var e=t.alternate;if(!e){if(e=d(t),e===null)throw Error(r(188));return e!==t?null:t}for(var n=t,l=e;;){var o=n.return;if(o===null)break;var u=o.alternate;if(u===null){if(l=o.return,l!==null){n=l;continue}break}if(o.child===u.child){for(u=o.child;u;){if(u===n)return p(o),t;if(u===l)return p(o),e;u=u.sibling}throw Error(r(188))}if(n.return!==l.return)n=o,l=u;else{for(var y=!1,x=o.child;x;){if(x===n){y=!0,n=o,l=u;break}if(x===l){y=!0,l=o,n=u;break}x=x.sibling}if(!y){for(x=u.child;x;){if(x===n){y=!0,n=u,l=o;break}if(x===l){y=!0,l=u,n=o;break}x=x.sibling}if(!y)throw Error(r(189))}}if(n.alternate!==l)throw Error(r(190))}if(n.tag!==3)throw Error(r(188));return n.stateNode.current===n?t:e}function h(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=h(t),e!==null)return e;t=t.sibling}return null}var v=Object.assign,b=Symbol.for("react.element"),S=Symbol.for("react.transitional.element"),E=Symbol.for("react.portal"),M=Symbol.for("react.fragment"),V=Symbol.for("react.strict_mode"),_=Symbol.for("react.profiler"),C=Symbol.for("react.provider"),q=Symbol.for("react.consumer"),L=Symbol.for("react.context"),P=Symbol.for("react.forward_ref"),Y=Symbol.for("react.suspense"),$=Symbol.for("react.suspense_list"),at=Symbol.for("react.memo"),F=Symbol.for("react.lazy"),rt=Symbol.for("react.activity"),vt=Symbol.for("react.memo_cache_sentinel"),Yt=Symbol.iterator;function Gt(t){return t===null||typeof t!="object"?null:(t=Yt&&t[Yt]||t["@@iterator"],typeof t=="function"?t:null)}var We=Symbol.for("react.client.reference");function He(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===We?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case M:return"Fragment";case _:return"Profiler";case V:return"StrictMode";case Y:return"Suspense";case $:return"SuspenseList";case rt:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case E:return"Portal";case L:return(t.displayName||"Context")+".Provider";case q:return(t._context.displayName||"Context")+".Consumer";case P:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case at:return e=t.displayName||null,e!==null?e:He(t.type)||"Memo";case F:e=t._payload,t=t._init;try{return He(t(e))}catch{}}return null}var $t=Array.isArray,k=i.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,K=s.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,W={pending:!1,data:null,method:null,action:null},yt=[],A=-1;function G(t){return{current:t}}function Q(t){0>A||(t.current=yt[A],yt[A]=null,A--)}function X(t,e){A++,yt[A]=t.current,t.current=e}var tt=G(null),mt=G(null),st=G(null),me=G(null);function wt(t,e){switch(X(st,e),X(mt,t),X(tt,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?Km(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=Km(e),t=Xm(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}Q(tt),X(tt,t)}function bn(){Q(tt),Q(mt),Q(st)}function Mr(t){t.memoizedState!==null&&X(me,t);var e=tt.current,n=Xm(e,t.type);e!==n&&(X(mt,t),X(tt,n))}function ql(t){mt.current===t&&(Q(tt),Q(mt)),me.current===t&&(Q(me),cl._currentValue=W)}var Rr=Object.prototype.hasOwnProperty,Dr=a.unstable_scheduleCallback,Cr=a.unstable_cancelCallback,Fg=a.unstable_shouldYield,Wg=a.unstable_requestPaint,qe=a.unstable_now,Ig=a.unstable_getCurrentPriorityLevel,jf=a.unstable_ImmediatePriority,Ef=a.unstable_UserBlockingPriority,Yl=a.unstable_NormalPriority,tv=a.unstable_LowPriority,wf=a.unstable_IdlePriority,ev=a.log,nv=a.unstable_setDisableYieldValue,vi=null,pe=null;function Sn(t){if(typeof ev=="function"&&nv(t),pe&&typeof pe.setStrictMode=="function")try{pe.setStrictMode(vi,t)}catch{}}var ye=Math.clz32?Math.clz32:lv,av=Math.log,iv=Math.LN2;function lv(t){return t>>>=0,t===0?32:31-(av(t)/iv|0)|0}var Gl=256,Kl=4194304;function $n(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function Xl(t,e,n){var l=t.pendingLanes;if(l===0)return 0;var o=0,u=t.suspendedLanes,y=t.pingedLanes;t=t.warmLanes;var x=l&134217727;return x!==0?(l=x&~u,l!==0?o=$n(l):(y&=x,y!==0?o=$n(y):n||(n=x&~t,n!==0&&(o=$n(n))))):(x=l&~u,x!==0?o=$n(x):y!==0?o=$n(y):n||(n=l&~t,n!==0&&(o=$n(n)))),o===0?0:e!==0&&e!==o&&(e&u)===0&&(u=o&-o,n=e&-e,u>=n||u===32&&(n&4194048)!==0)?e:o}function xi(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function sv(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Nf(){var t=Gl;return Gl<<=1,(Gl&4194048)===0&&(Gl=256),t}function Mf(){var t=Kl;return Kl<<=1,(Kl&62914560)===0&&(Kl=4194304),t}function Or(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function bi(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function rv(t,e,n,l,o,u){var y=t.pendingLanes;t.pendingLanes=n,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=n,t.entangledLanes&=n,t.errorRecoveryDisabledLanes&=n,t.shellSuspendCounter=0;var x=t.entanglements,T=t.expirationTimes,D=t.hiddenUpdates;for(n=y&~n;0<n;){var U=31-ye(n),H=1<<U;x[U]=0,T[U]=-1;var O=D[U];if(O!==null)for(D[U]=null,U=0;U<O.length;U++){var z=O[U];z!==null&&(z.lane&=-536870913)}n&=~H}l!==0&&Rf(t,l,0),u!==0&&o===0&&t.tag!==0&&(t.suspendedLanes|=u&~(y&~e))}function Rf(t,e,n){t.pendingLanes|=e,t.suspendedLanes&=~e;var l=31-ye(e);t.entangledLanes|=e,t.entanglements[l]=t.entanglements[l]|1073741824|n&4194090}function Df(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var l=31-ye(n),o=1<<l;o&e|t[l]&e&&(t[l]|=e),n&=~o}}function zr(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function Vr(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function Cf(){var t=K.p;return t!==0?t:(t=window.event,t===void 0?32:cp(t.type))}function ov(t,e){var n=K.p;try{return K.p=t,e()}finally{K.p=n}}var Tn=Math.random().toString(36).slice(2),ee="__reactFiber$"+Tn,oe="__reactProps$"+Tn,Sa="__reactContainer$"+Tn,_r="__reactEvents$"+Tn,uv="__reactListeners$"+Tn,cv="__reactHandles$"+Tn,Of="__reactResources$"+Tn,Si="__reactMarker$"+Tn;function Ur(t){delete t[ee],delete t[oe],delete t[_r],delete t[uv],delete t[cv]}function Ta(t){var e=t[ee];if(e)return e;for(var n=t.parentNode;n;){if(e=n[Sa]||n[ee]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=Jm(t);t!==null;){if(n=t[ee])return n;t=Jm(t)}return e}t=n,n=t.parentNode}return null}function Aa(t){if(t=t[ee]||t[Sa]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function Ti(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(r(33))}function ja(t){var e=t[Of];return e||(e=t[Of]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function Zt(t){t[Si]=!0}var zf=new Set,Vf={};function Fn(t,e){Ea(t,e),Ea(t+"Capture",e)}function Ea(t,e){for(Vf[t]=e,t=0;t<e.length;t++)zf.add(e[t])}var fv=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),_f={},Uf={};function dv(t){return Rr.call(Uf,t)?!0:Rr.call(_f,t)?!1:fv.test(t)?Uf[t]=!0:(_f[t]=!0,!1)}function Zl(t,e,n){if(dv(e))if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var l=e.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+n)}}function Ql(t,e,n){if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+n)}}function Ie(t,e,n,l){if(l===null)t.removeAttribute(n);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(n);return}t.setAttributeNS(e,n,""+l)}}var kr,kf;function wa(t){if(kr===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);kr=e&&e[1]||"",kf=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+kr+t+kf}var Br=!1;function Lr(t,e){if(!t||Br)return"";Br=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(e){var H=function(){throw Error()};if(Object.defineProperty(H.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(H,[])}catch(z){var O=z}Reflect.construct(t,[],H)}else{try{H.call()}catch(z){O=z}t.call(H.prototype)}}else{try{throw Error()}catch(z){O=z}(H=t())&&typeof H.catch=="function"&&H.catch(function(){})}}catch(z){if(z&&O&&typeof z.stack=="string")return[z.stack,O.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var o=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");o&&o.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var u=l.DetermineComponentFrameRoot(),y=u[0],x=u[1];if(y&&x){var T=y.split(`
`),D=x.split(`
`);for(o=l=0;l<T.length&&!T[l].includes("DetermineComponentFrameRoot");)l++;for(;o<D.length&&!D[o].includes("DetermineComponentFrameRoot");)o++;if(l===T.length||o===D.length)for(l=T.length-1,o=D.length-1;1<=l&&0<=o&&T[l]!==D[o];)o--;for(;1<=l&&0<=o;l--,o--)if(T[l]!==D[o]){if(l!==1||o!==1)do if(l--,o--,0>o||T[l]!==D[o]){var U=`
`+T[l].replace(" at new "," at ");return t.displayName&&U.includes("<anonymous>")&&(U=U.replace("<anonymous>",t.displayName)),U}while(1<=l&&0<=o);break}}}finally{Br=!1,Error.prepareStackTrace=n}return(n=t?t.displayName||t.name:"")?wa(n):""}function hv(t){switch(t.tag){case 26:case 27:case 5:return wa(t.type);case 16:return wa("Lazy");case 13:return wa("Suspense");case 19:return wa("SuspenseList");case 0:case 15:return Lr(t.type,!1);case 11:return Lr(t.type.render,!1);case 1:return Lr(t.type,!0);case 31:return wa("Activity");default:return""}}function Bf(t){try{var e="";do e+=hv(t),t=t.return;while(t);return e}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function je(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function Lf(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function mv(t){var e=Lf(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),l=""+t[e];if(!t.hasOwnProperty(e)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,u=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return o.call(this)},set:function(y){l=""+y,u.call(this,y)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return l},setValue:function(y){l=""+y},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function Pl(t){t._valueTracker||(t._valueTracker=mv(t))}function Hf(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),l="";return t&&(l=Lf(t)?t.checked?"true":"false":t.value),t=l,t!==n?(e.setValue(t),!0):!1}function Jl(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var pv=/[\n"\\]/g;function Ee(t){return t.replace(pv,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function Hr(t,e,n,l,o,u,y,x){t.name="",y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"?t.type=y:t.removeAttribute("type"),e!=null?y==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+je(e)):t.value!==""+je(e)&&(t.value=""+je(e)):y!=="submit"&&y!=="reset"||t.removeAttribute("value"),e!=null?qr(t,y,je(e)):n!=null?qr(t,y,je(n)):l!=null&&t.removeAttribute("value"),o==null&&u!=null&&(t.defaultChecked=!!u),o!=null&&(t.checked=o&&typeof o!="function"&&typeof o!="symbol"),x!=null&&typeof x!="function"&&typeof x!="symbol"&&typeof x!="boolean"?t.name=""+je(x):t.removeAttribute("name")}function qf(t,e,n,l,o,u,y,x){if(u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"&&(t.type=u),e!=null||n!=null){if(!(u!=="submit"&&u!=="reset"||e!=null))return;n=n!=null?""+je(n):"",e=e!=null?""+je(e):n,x||e===t.value||(t.value=e),t.defaultValue=e}l=l??o,l=typeof l!="function"&&typeof l!="symbol"&&!!l,t.checked=x?t.checked:!!l,t.defaultChecked=!!l,y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"&&(t.name=y)}function qr(t,e,n){e==="number"&&Jl(t.ownerDocument)===t||t.defaultValue===""+n||(t.defaultValue=""+n)}function Na(t,e,n,l){if(t=t.options,e){e={};for(var o=0;o<n.length;o++)e["$"+n[o]]=!0;for(n=0;n<t.length;n++)o=e.hasOwnProperty("$"+t[n].value),t[n].selected!==o&&(t[n].selected=o),o&&l&&(t[n].defaultSelected=!0)}else{for(n=""+je(n),e=null,o=0;o<t.length;o++){if(t[o].value===n){t[o].selected=!0,l&&(t[o].defaultSelected=!0);return}e!==null||t[o].disabled||(e=t[o])}e!==null&&(e.selected=!0)}}function Yf(t,e,n){if(e!=null&&(e=""+je(e),e!==t.value&&(t.value=e),n==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=n!=null?""+je(n):""}function Gf(t,e,n,l){if(e==null){if(l!=null){if(n!=null)throw Error(r(92));if($t(l)){if(1<l.length)throw Error(r(93));l=l[0]}n=l}n==null&&(n=""),e=n}n=je(e),t.defaultValue=n,l=t.textContent,l===n&&l!==""&&l!==null&&(t.value=l)}function Ma(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var yv=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Kf(t,e,n){var l=e.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?l?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":l?t.setProperty(e,n):typeof n!="number"||n===0||yv.has(e)?e==="float"?t.cssFloat=n:t[e]=(""+n).trim():t[e]=n+"px"}function Xf(t,e,n){if(e!=null&&typeof e!="object")throw Error(r(62));if(t=t.style,n!=null){for(var l in n)!n.hasOwnProperty(l)||e!=null&&e.hasOwnProperty(l)||(l.indexOf("--")===0?t.setProperty(l,""):l==="float"?t.cssFloat="":t[l]="");for(var o in e)l=e[o],e.hasOwnProperty(o)&&n[o]!==l&&Kf(t,o,l)}else for(var u in e)e.hasOwnProperty(u)&&Kf(t,u,e[u])}function Yr(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var gv=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),vv=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function $l(t){return vv.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var Gr=null;function Kr(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var Ra=null,Da=null;function Zf(t){var e=Aa(t);if(e&&(t=e.stateNode)){var n=t[oe]||null;t:switch(t=e.stateNode,e.type){case"input":if(Hr(t,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+Ee(""+e)+'"][type="radio"]'),e=0;e<n.length;e++){var l=n[e];if(l!==t&&l.form===t.form){var o=l[oe]||null;if(!o)throw Error(r(90));Hr(l,o.value,o.defaultValue,o.defaultValue,o.checked,o.defaultChecked,o.type,o.name)}}for(e=0;e<n.length;e++)l=n[e],l.form===t.form&&Hf(l)}break t;case"textarea":Yf(t,n.value,n.defaultValue);break t;case"select":e=n.value,e!=null&&Na(t,!!n.multiple,e,!1)}}}var Xr=!1;function Qf(t,e,n){if(Xr)return t(e,n);Xr=!0;try{var l=t(e);return l}finally{if(Xr=!1,(Ra!==null||Da!==null)&&(_s(),Ra&&(e=Ra,t=Da,Da=Ra=null,Zf(e),t)))for(e=0;e<t.length;e++)Zf(t[e])}}function Ai(t,e){var n=t.stateNode;if(n===null)return null;var l=n[oe]||null;if(l===null)return null;n=l[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(t=t.type,l=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!l;break t;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(r(231,e,typeof n));return n}var tn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Zr=!1;if(tn)try{var ji={};Object.defineProperty(ji,"passive",{get:function(){Zr=!0}}),window.addEventListener("test",ji,ji),window.removeEventListener("test",ji,ji)}catch{Zr=!1}var An=null,Qr=null,Fl=null;function Pf(){if(Fl)return Fl;var t,e=Qr,n=e.length,l,o="value"in An?An.value:An.textContent,u=o.length;for(t=0;t<n&&e[t]===o[t];t++);var y=n-t;for(l=1;l<=y&&e[n-l]===o[u-l];l++);return Fl=o.slice(t,1<l?1-l:void 0)}function Wl(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function Il(){return!0}function Jf(){return!1}function ue(t){function e(n,l,o,u,y){this._reactName=n,this._targetInst=o,this.type=l,this.nativeEvent=u,this.target=y,this.currentTarget=null;for(var x in t)t.hasOwnProperty(x)&&(n=t[x],this[x]=n?n(u):u[x]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?Il:Jf,this.isPropagationStopped=Jf,this}return v(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Il)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Il)},persist:function(){},isPersistent:Il}),e}var Wn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ts=ue(Wn),Ei=v({},Wn,{view:0,detail:0}),xv=ue(Ei),Pr,Jr,wi,es=v({},Ei,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Fr,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==wi&&(wi&&t.type==="mousemove"?(Pr=t.screenX-wi.screenX,Jr=t.screenY-wi.screenY):Jr=Pr=0,wi=t),Pr)},movementY:function(t){return"movementY"in t?t.movementY:Jr}}),$f=ue(es),bv=v({},es,{dataTransfer:0}),Sv=ue(bv),Tv=v({},Ei,{relatedTarget:0}),$r=ue(Tv),Av=v({},Wn,{animationName:0,elapsedTime:0,pseudoElement:0}),jv=ue(Av),Ev=v({},Wn,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),wv=ue(Ev),Nv=v({},Wn,{data:0}),Ff=ue(Nv),Mv={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Rv={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Dv={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Cv(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=Dv[t])?!!e[t]:!1}function Fr(){return Cv}var Ov=v({},Ei,{key:function(t){if(t.key){var e=Mv[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=Wl(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?Rv[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Fr,charCode:function(t){return t.type==="keypress"?Wl(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?Wl(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),zv=ue(Ov),Vv=v({},es,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Wf=ue(Vv),_v=v({},Ei,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Fr}),Uv=ue(_v),kv=v({},Wn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Bv=ue(kv),Lv=v({},es,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),Hv=ue(Lv),qv=v({},Wn,{newState:0,oldState:0}),Yv=ue(qv),Gv=[9,13,27,32],Wr=tn&&"CompositionEvent"in window,Ni=null;tn&&"documentMode"in document&&(Ni=document.documentMode);var Kv=tn&&"TextEvent"in window&&!Ni,If=tn&&(!Wr||Ni&&8<Ni&&11>=Ni),td=" ",ed=!1;function nd(t,e){switch(t){case"keyup":return Gv.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function ad(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var Ca=!1;function Xv(t,e){switch(t){case"compositionend":return ad(e);case"keypress":return e.which!==32?null:(ed=!0,td);case"textInput":return t=e.data,t===td&&ed?null:t;default:return null}}function Zv(t,e){if(Ca)return t==="compositionend"||!Wr&&nd(t,e)?(t=Pf(),Fl=Qr=An=null,Ca=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return If&&e.locale!=="ko"?null:e.data;default:return null}}var Qv={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function id(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!Qv[t.type]:e==="textarea"}function ld(t,e,n,l){Ra?Da?Da.push(l):Da=[l]:Ra=l,e=qs(e,"onChange"),0<e.length&&(n=new ts("onChange","change",null,n,l),t.push({event:n,listeners:e}))}var Mi=null,Ri=null;function Pv(t){Lm(t,0)}function ns(t){var e=Ti(t);if(Hf(e))return t}function sd(t,e){if(t==="change")return e}var rd=!1;if(tn){var Ir;if(tn){var to="oninput"in document;if(!to){var od=document.createElement("div");od.setAttribute("oninput","return;"),to=typeof od.oninput=="function"}Ir=to}else Ir=!1;rd=Ir&&(!document.documentMode||9<document.documentMode)}function ud(){Mi&&(Mi.detachEvent("onpropertychange",cd),Ri=Mi=null)}function cd(t){if(t.propertyName==="value"&&ns(Ri)){var e=[];ld(e,Ri,t,Kr(t)),Qf(Pv,e)}}function Jv(t,e,n){t==="focusin"?(ud(),Mi=e,Ri=n,Mi.attachEvent("onpropertychange",cd)):t==="focusout"&&ud()}function $v(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return ns(Ri)}function Fv(t,e){if(t==="click")return ns(e)}function Wv(t,e){if(t==="input"||t==="change")return ns(e)}function Iv(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var ge=typeof Object.is=="function"?Object.is:Iv;function Di(t,e){if(ge(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),l=Object.keys(e);if(n.length!==l.length)return!1;for(l=0;l<n.length;l++){var o=n[l];if(!Rr.call(e,o)||!ge(t[o],e[o]))return!1}return!0}function fd(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function dd(t,e){var n=fd(t);t=0;for(var l;n;){if(n.nodeType===3){if(l=t+n.textContent.length,t<=e&&l>=e)return{node:n,offset:e-t};t=l}t:{for(;n;){if(n.nextSibling){n=n.nextSibling;break t}n=n.parentNode}n=void 0}n=fd(n)}}function hd(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?hd(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function md(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=Jl(t.document);e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch{n=!1}if(n)t=e.contentWindow;else break;e=Jl(t.document)}return e}function eo(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var tx=tn&&"documentMode"in document&&11>=document.documentMode,Oa=null,no=null,Ci=null,ao=!1;function pd(t,e,n){var l=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;ao||Oa==null||Oa!==Jl(l)||(l=Oa,"selectionStart"in l&&eo(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),Ci&&Di(Ci,l)||(Ci=l,l=qs(no,"onSelect"),0<l.length&&(e=new ts("onSelect","select",null,e,n),t.push({event:e,listeners:l}),e.target=Oa)))}function In(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var za={animationend:In("Animation","AnimationEnd"),animationiteration:In("Animation","AnimationIteration"),animationstart:In("Animation","AnimationStart"),transitionrun:In("Transition","TransitionRun"),transitionstart:In("Transition","TransitionStart"),transitioncancel:In("Transition","TransitionCancel"),transitionend:In("Transition","TransitionEnd")},io={},yd={};tn&&(yd=document.createElement("div").style,"AnimationEvent"in window||(delete za.animationend.animation,delete za.animationiteration.animation,delete za.animationstart.animation),"TransitionEvent"in window||delete za.transitionend.transition);function ta(t){if(io[t])return io[t];if(!za[t])return t;var e=za[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in yd)return io[t]=e[n];return t}var gd=ta("animationend"),vd=ta("animationiteration"),xd=ta("animationstart"),ex=ta("transitionrun"),nx=ta("transitionstart"),ax=ta("transitioncancel"),bd=ta("transitionend"),Sd=new Map,lo="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");lo.push("scrollEnd");function Ue(t,e){Sd.set(t,e),Fn(e,[t])}var Td=new WeakMap;function we(t,e){if(typeof t=="object"&&t!==null){var n=Td.get(t);return n!==void 0?n:(e={value:t,source:e,stack:Bf(e)},Td.set(t,e),e)}return{value:t,source:e,stack:Bf(e)}}var Ne=[],Va=0,so=0;function as(){for(var t=Va,e=so=Va=0;e<t;){var n=Ne[e];Ne[e++]=null;var l=Ne[e];Ne[e++]=null;var o=Ne[e];Ne[e++]=null;var u=Ne[e];if(Ne[e++]=null,l!==null&&o!==null){var y=l.pending;y===null?o.next=o:(o.next=y.next,y.next=o),l.pending=o}u!==0&&Ad(n,o,u)}}function is(t,e,n,l){Ne[Va++]=t,Ne[Va++]=e,Ne[Va++]=n,Ne[Va++]=l,so|=l,t.lanes|=l,t=t.alternate,t!==null&&(t.lanes|=l)}function ro(t,e,n,l){return is(t,e,n,l),ls(t)}function _a(t,e){return is(t,null,null,e),ls(t)}function Ad(t,e,n){t.lanes|=n;var l=t.alternate;l!==null&&(l.lanes|=n);for(var o=!1,u=t.return;u!==null;)u.childLanes|=n,l=u.alternate,l!==null&&(l.childLanes|=n),u.tag===22&&(t=u.stateNode,t===null||t._visibility&1||(o=!0)),t=u,u=u.return;return t.tag===3?(u=t.stateNode,o&&e!==null&&(o=31-ye(n),t=u.hiddenUpdates,l=t[o],l===null?t[o]=[e]:l.push(e),e.lane=n|536870912),u):null}function ls(t){if(50<nl)throw nl=0,mu=null,Error(r(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var Ua={};function ix(t,e,n,l){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ve(t,e,n,l){return new ix(t,e,n,l)}function oo(t){return t=t.prototype,!(!t||!t.isReactComponent)}function en(t,e){var n=t.alternate;return n===null?(n=ve(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&65011712,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n.refCleanup=t.refCleanup,n}function jd(t,e){t.flags&=65011714;var n=t.alternate;return n===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=n.childLanes,t.lanes=n.lanes,t.child=n.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=n.memoizedProps,t.memoizedState=n.memoizedState,t.updateQueue=n.updateQueue,t.type=n.type,e=n.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function ss(t,e,n,l,o,u){var y=0;if(l=t,typeof t=="function")oo(t)&&(y=1);else if(typeof t=="string")y=s1(t,n,tt.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case rt:return t=ve(31,n,e,o),t.elementType=rt,t.lanes=u,t;case M:return ea(n.children,o,u,e);case V:y=8,o|=24;break;case _:return t=ve(12,n,e,o|2),t.elementType=_,t.lanes=u,t;case Y:return t=ve(13,n,e,o),t.elementType=Y,t.lanes=u,t;case $:return t=ve(19,n,e,o),t.elementType=$,t.lanes=u,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case C:case L:y=10;break t;case q:y=9;break t;case P:y=11;break t;case at:y=14;break t;case F:y=16,l=null;break t}y=29,n=Error(r(130,t===null?"null":typeof t,"")),l=null}return e=ve(y,n,e,o),e.elementType=t,e.type=l,e.lanes=u,e}function ea(t,e,n,l){return t=ve(7,t,l,e),t.lanes=n,t}function uo(t,e,n){return t=ve(6,t,null,e),t.lanes=n,t}function co(t,e,n){return e=ve(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var ka=[],Ba=0,rs=null,os=0,Me=[],Re=0,na=null,nn=1,an="";function aa(t,e){ka[Ba++]=os,ka[Ba++]=rs,rs=t,os=e}function Ed(t,e,n){Me[Re++]=nn,Me[Re++]=an,Me[Re++]=na,na=t;var l=nn;t=an;var o=32-ye(l)-1;l&=~(1<<o),n+=1;var u=32-ye(e)+o;if(30<u){var y=o-o%5;u=(l&(1<<y)-1).toString(32),l>>=y,o-=y,nn=1<<32-ye(e)+o|n<<o|l,an=u+t}else nn=1<<u|n<<o|l,an=t}function fo(t){t.return!==null&&(aa(t,1),Ed(t,1,0))}function ho(t){for(;t===rs;)rs=ka[--Ba],ka[Ba]=null,os=ka[--Ba],ka[Ba]=null;for(;t===na;)na=Me[--Re],Me[Re]=null,an=Me[--Re],Me[Re]=null,nn=Me[--Re],Me[Re]=null}var le=null,zt=null,gt=!1,ia=null,Ye=!1,mo=Error(r(519));function la(t){var e=Error(r(418,""));throw Vi(we(e,t)),mo}function wd(t){var e=t.stateNode,n=t.type,l=t.memoizedProps;switch(e[ee]=t,e[oe]=l,n){case"dialog":dt("cancel",e),dt("close",e);break;case"iframe":case"object":case"embed":dt("load",e);break;case"video":case"audio":for(n=0;n<il.length;n++)dt(il[n],e);break;case"source":dt("error",e);break;case"img":case"image":case"link":dt("error",e),dt("load",e);break;case"details":dt("toggle",e);break;case"input":dt("invalid",e),qf(e,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),Pl(e);break;case"select":dt("invalid",e);break;case"textarea":dt("invalid",e),Gf(e,l.value,l.defaultValue,l.children),Pl(e)}n=l.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||e.textContent===""+n||l.suppressHydrationWarning===!0||Gm(e.textContent,n)?(l.popover!=null&&(dt("beforetoggle",e),dt("toggle",e)),l.onScroll!=null&&dt("scroll",e),l.onScrollEnd!=null&&dt("scrollend",e),l.onClick!=null&&(e.onclick=Ys),e=!0):e=!1,e||la(t)}function Nd(t){for(le=t.return;le;)switch(le.tag){case 5:case 13:Ye=!1;return;case 27:case 3:Ye=!0;return;default:le=le.return}}function Oi(t){if(t!==le)return!1;if(!gt)return Nd(t),gt=!0,!1;var e=t.tag,n;if((n=e!==3&&e!==27)&&((n=e===5)&&(n=t.type,n=!(n!=="form"&&n!=="button")||Du(t.type,t.memoizedProps)),n=!n),n&&zt&&la(t),Nd(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(r(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(n=t.data,n==="/$"){if(e===0){zt=Be(t.nextSibling);break t}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++;t=t.nextSibling}zt=null}}else e===27?(e=zt,Ln(t.type)?(t=Vu,Vu=null,zt=t):zt=e):zt=le?Be(t.stateNode.nextSibling):null;return!0}function zi(){zt=le=null,gt=!1}function Md(){var t=ia;return t!==null&&(de===null?de=t:de.push.apply(de,t),ia=null),t}function Vi(t){ia===null?ia=[t]:ia.push(t)}var po=G(null),sa=null,ln=null;function jn(t,e,n){X(po,e._currentValue),e._currentValue=n}function sn(t){t._currentValue=po.current,Q(po)}function yo(t,e,n){for(;t!==null;){var l=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,l!==null&&(l.childLanes|=e)):l!==null&&(l.childLanes&e)!==e&&(l.childLanes|=e),t===n)break;t=t.return}}function go(t,e,n,l){var o=t.child;for(o!==null&&(o.return=t);o!==null;){var u=o.dependencies;if(u!==null){var y=o.child;u=u.firstContext;t:for(;u!==null;){var x=u;u=o;for(var T=0;T<e.length;T++)if(x.context===e[T]){u.lanes|=n,x=u.alternate,x!==null&&(x.lanes|=n),yo(u.return,n,t),l||(y=null);break t}u=x.next}}else if(o.tag===18){if(y=o.return,y===null)throw Error(r(341));y.lanes|=n,u=y.alternate,u!==null&&(u.lanes|=n),yo(y,n,t),y=null}else y=o.child;if(y!==null)y.return=o;else for(y=o;y!==null;){if(y===t){y=null;break}if(o=y.sibling,o!==null){o.return=y.return,y=o;break}y=y.return}o=y}}function _i(t,e,n,l){t=null;for(var o=e,u=!1;o!==null;){if(!u){if((o.flags&524288)!==0)u=!0;else if((o.flags&262144)!==0)break}if(o.tag===10){var y=o.alternate;if(y===null)throw Error(r(387));if(y=y.memoizedProps,y!==null){var x=o.type;ge(o.pendingProps.value,y.value)||(t!==null?t.push(x):t=[x])}}else if(o===me.current){if(y=o.alternate,y===null)throw Error(r(387));y.memoizedState.memoizedState!==o.memoizedState.memoizedState&&(t!==null?t.push(cl):t=[cl])}o=o.return}t!==null&&go(e,t,n,l),e.flags|=262144}function us(t){for(t=t.firstContext;t!==null;){if(!ge(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function ra(t){sa=t,ln=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function ne(t){return Rd(sa,t)}function cs(t,e){return sa===null&&ra(t),Rd(t,e)}function Rd(t,e){var n=e._currentValue;if(e={context:e,memoizedValue:n,next:null},ln===null){if(t===null)throw Error(r(308));ln=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else ln=ln.next=e;return n}var lx=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(n,l){t.push(l)}};this.abort=function(){e.aborted=!0,t.forEach(function(n){return n()})}},sx=a.unstable_scheduleCallback,rx=a.unstable_NormalPriority,Kt={$$typeof:L,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function vo(){return{controller:new lx,data:new Map,refCount:0}}function Ui(t){t.refCount--,t.refCount===0&&sx(rx,function(){t.controller.abort()})}var ki=null,xo=0,La=0,Ha=null;function ox(t,e){if(ki===null){var n=ki=[];xo=0,La=Su(),Ha={status:"pending",value:void 0,then:function(l){n.push(l)}}}return xo++,e.then(Dd,Dd),e}function Dd(){if(--xo===0&&ki!==null){Ha!==null&&(Ha.status="fulfilled");var t=ki;ki=null,La=0,Ha=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function ux(t,e){var n=[],l={status:"pending",value:null,reason:null,then:function(o){n.push(o)}};return t.then(function(){l.status="fulfilled",l.value=e;for(var o=0;o<n.length;o++)(0,n[o])(e)},function(o){for(l.status="rejected",l.reason=o,o=0;o<n.length;o++)(0,n[o])(void 0)}),l}var Cd=k.S;k.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&ox(t,e),Cd!==null&&Cd(t,e)};var oa=G(null);function bo(){var t=oa.current;return t!==null?t:Et.pooledCache}function fs(t,e){e===null?X(oa,oa.current):X(oa,e.pool)}function Od(){var t=bo();return t===null?null:{parent:Kt._currentValue,pool:t}}var Bi=Error(r(460)),zd=Error(r(474)),ds=Error(r(542)),So={then:function(){}};function Vd(t){return t=t.status,t==="fulfilled"||t==="rejected"}function hs(){}function _d(t,e,n){switch(n=t[n],n===void 0?t.push(e):n!==e&&(e.then(hs,hs),e=n),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,kd(t),t;default:if(typeof e.status=="string")e.then(hs,hs);else{if(t=Et,t!==null&&100<t.shellSuspendCounter)throw Error(r(482));t=e,t.status="pending",t.then(function(l){if(e.status==="pending"){var o=e;o.status="fulfilled",o.value=l}},function(l){if(e.status==="pending"){var o=e;o.status="rejected",o.reason=l}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,kd(t),t}throw Li=e,Bi}}var Li=null;function Ud(){if(Li===null)throw Error(r(459));var t=Li;return Li=null,t}function kd(t){if(t===Bi||t===ds)throw Error(r(483))}var En=!1;function To(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Ao(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function wn(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function Nn(t,e,n){var l=t.updateQueue;if(l===null)return null;if(l=l.shared,(xt&2)!==0){var o=l.pending;return o===null?e.next=e:(e.next=o.next,o.next=e),l.pending=e,e=ls(t),Ad(t,null,n),e}return is(t,l,e,n),ls(t)}function Hi(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194048)!==0)){var l=e.lanes;l&=t.pendingLanes,n|=l,e.lanes=n,Df(t,n)}}function jo(t,e){var n=t.updateQueue,l=t.alternate;if(l!==null&&(l=l.updateQueue,n===l)){var o=null,u=null;if(n=n.firstBaseUpdate,n!==null){do{var y={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};u===null?o=u=y:u=u.next=y,n=n.next}while(n!==null);u===null?o=u=e:u=u.next=e}else o=u=e;n={baseState:l.baseState,firstBaseUpdate:o,lastBaseUpdate:u,shared:l.shared,callbacks:l.callbacks},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}var Eo=!1;function qi(){if(Eo){var t=Ha;if(t!==null)throw t}}function Yi(t,e,n,l){Eo=!1;var o=t.updateQueue;En=!1;var u=o.firstBaseUpdate,y=o.lastBaseUpdate,x=o.shared.pending;if(x!==null){o.shared.pending=null;var T=x,D=T.next;T.next=null,y===null?u=D:y.next=D,y=T;var U=t.alternate;U!==null&&(U=U.updateQueue,x=U.lastBaseUpdate,x!==y&&(x===null?U.firstBaseUpdate=D:x.next=D,U.lastBaseUpdate=T))}if(u!==null){var H=o.baseState;y=0,U=D=T=null,x=u;do{var O=x.lane&-536870913,z=O!==x.lane;if(z?(ht&O)===O:(l&O)===O){O!==0&&O===La&&(Eo=!0),U!==null&&(U=U.next={lane:0,tag:x.tag,payload:x.payload,callback:null,next:null});t:{var it=t,et=x;O=e;var At=n;switch(et.tag){case 1:if(it=et.payload,typeof it=="function"){H=it.call(At,H,O);break t}H=it;break t;case 3:it.flags=it.flags&-65537|128;case 0:if(it=et.payload,O=typeof it=="function"?it.call(At,H,O):it,O==null)break t;H=v({},H,O);break t;case 2:En=!0}}O=x.callback,O!==null&&(t.flags|=64,z&&(t.flags|=8192),z=o.callbacks,z===null?o.callbacks=[O]:z.push(O))}else z={lane:O,tag:x.tag,payload:x.payload,callback:x.callback,next:null},U===null?(D=U=z,T=H):U=U.next=z,y|=O;if(x=x.next,x===null){if(x=o.shared.pending,x===null)break;z=x,x=z.next,z.next=null,o.lastBaseUpdate=z,o.shared.pending=null}}while(!0);U===null&&(T=H),o.baseState=T,o.firstBaseUpdate=D,o.lastBaseUpdate=U,u===null&&(o.shared.lanes=0),_n|=y,t.lanes=y,t.memoizedState=H}}function Bd(t,e){if(typeof t!="function")throw Error(r(191,t));t.call(e)}function Ld(t,e){var n=t.callbacks;if(n!==null)for(t.callbacks=null,t=0;t<n.length;t++)Bd(n[t],e)}var qa=G(null),ms=G(0);function Hd(t,e){t=hn,X(ms,t),X(qa,e),hn=t|e.baseLanes}function wo(){X(ms,hn),X(qa,qa.current)}function No(){hn=ms.current,Q(qa),Q(ms)}var Mn=0,ut=null,St=null,Lt=null,ps=!1,Ya=!1,ua=!1,ys=0,Gi=0,Ga=null,cx=0;function Ut(){throw Error(r(321))}function Mo(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!ge(t[n],e[n]))return!1;return!0}function Ro(t,e,n,l,o,u){return Mn=u,ut=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,k.H=t===null||t.memoizedState===null?Ah:jh,ua=!1,u=n(l,o),ua=!1,Ya&&(u=Yd(e,n,l,o)),qd(t),u}function qd(t){k.H=Ts;var e=St!==null&&St.next!==null;if(Mn=0,Lt=St=ut=null,ps=!1,Gi=0,Ga=null,e)throw Error(r(300));t===null||Qt||(t=t.dependencies,t!==null&&us(t)&&(Qt=!0))}function Yd(t,e,n,l){ut=t;var o=0;do{if(Ya&&(Ga=null),Gi=0,Ya=!1,25<=o)throw Error(r(301));if(o+=1,Lt=St=null,t.updateQueue!=null){var u=t.updateQueue;u.lastEffect=null,u.events=null,u.stores=null,u.memoCache!=null&&(u.memoCache.index=0)}k.H=gx,u=e(n,l)}while(Ya);return u}function fx(){var t=k.H,e=t.useState()[0];return e=typeof e.then=="function"?Ki(e):e,t=t.useState()[0],(St!==null?St.memoizedState:null)!==t&&(ut.flags|=1024),e}function Do(){var t=ys!==0;return ys=0,t}function Co(t,e,n){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~n}function Oo(t){if(ps){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}ps=!1}Mn=0,Lt=St=ut=null,Ya=!1,Gi=ys=0,Ga=null}function ce(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Lt===null?ut.memoizedState=Lt=t:Lt=Lt.next=t,Lt}function Ht(){if(St===null){var t=ut.alternate;t=t!==null?t.memoizedState:null}else t=St.next;var e=Lt===null?ut.memoizedState:Lt.next;if(e!==null)Lt=e,St=t;else{if(t===null)throw ut.alternate===null?Error(r(467)):Error(r(310));St=t,t={memoizedState:St.memoizedState,baseState:St.baseState,baseQueue:St.baseQueue,queue:St.queue,next:null},Lt===null?ut.memoizedState=Lt=t:Lt=Lt.next=t}return Lt}function zo(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Ki(t){var e=Gi;return Gi+=1,Ga===null&&(Ga=[]),t=_d(Ga,t,e),e=ut,(Lt===null?e.memoizedState:Lt.next)===null&&(e=e.alternate,k.H=e===null||e.memoizedState===null?Ah:jh),t}function gs(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return Ki(t);if(t.$$typeof===L)return ne(t)}throw Error(r(438,String(t)))}function Vo(t){var e=null,n=ut.updateQueue;if(n!==null&&(e=n.memoCache),e==null){var l=ut.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(e={data:l.data.map(function(o){return o.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),n===null&&(n=zo(),ut.updateQueue=n),n.memoCache=e,n=e.data[e.index],n===void 0)for(n=e.data[e.index]=Array(t),l=0;l<t;l++)n[l]=vt;return e.index++,n}function rn(t,e){return typeof e=="function"?e(t):e}function vs(t){var e=Ht();return _o(e,St,t)}function _o(t,e,n){var l=t.queue;if(l===null)throw Error(r(311));l.lastRenderedReducer=n;var o=t.baseQueue,u=l.pending;if(u!==null){if(o!==null){var y=o.next;o.next=u.next,u.next=y}e.baseQueue=o=u,l.pending=null}if(u=t.baseState,o===null)t.memoizedState=u;else{e=o.next;var x=y=null,T=null,D=e,U=!1;do{var H=D.lane&-536870913;if(H!==D.lane?(ht&H)===H:(Mn&H)===H){var O=D.revertLane;if(O===0)T!==null&&(T=T.next={lane:0,revertLane:0,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null}),H===La&&(U=!0);else if((Mn&O)===O){D=D.next,O===La&&(U=!0);continue}else H={lane:0,revertLane:D.revertLane,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null},T===null?(x=T=H,y=u):T=T.next=H,ut.lanes|=O,_n|=O;H=D.action,ua&&n(u,H),u=D.hasEagerState?D.eagerState:n(u,H)}else O={lane:H,revertLane:D.revertLane,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null},T===null?(x=T=O,y=u):T=T.next=O,ut.lanes|=H,_n|=H;D=D.next}while(D!==null&&D!==e);if(T===null?y=u:T.next=x,!ge(u,t.memoizedState)&&(Qt=!0,U&&(n=Ha,n!==null)))throw n;t.memoizedState=u,t.baseState=y,t.baseQueue=T,l.lastRenderedState=u}return o===null&&(l.lanes=0),[t.memoizedState,l.dispatch]}function Uo(t){var e=Ht(),n=e.queue;if(n===null)throw Error(r(311));n.lastRenderedReducer=t;var l=n.dispatch,o=n.pending,u=e.memoizedState;if(o!==null){n.pending=null;var y=o=o.next;do u=t(u,y.action),y=y.next;while(y!==o);ge(u,e.memoizedState)||(Qt=!0),e.memoizedState=u,e.baseQueue===null&&(e.baseState=u),n.lastRenderedState=u}return[u,l]}function Gd(t,e,n){var l=ut,o=Ht(),u=gt;if(u){if(n===void 0)throw Error(r(407));n=n()}else n=e();var y=!ge((St||o).memoizedState,n);y&&(o.memoizedState=n,Qt=!0),o=o.queue;var x=Zd.bind(null,l,o,t);if(Xi(2048,8,x,[t]),o.getSnapshot!==e||y||Lt!==null&&Lt.memoizedState.tag&1){if(l.flags|=2048,Ka(9,xs(),Xd.bind(null,l,o,n,e),null),Et===null)throw Error(r(349));u||(Mn&124)!==0||Kd(l,e,n)}return n}function Kd(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=ut.updateQueue,e===null?(e=zo(),ut.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function Xd(t,e,n,l){e.value=n,e.getSnapshot=l,Qd(e)&&Pd(t)}function Zd(t,e,n){return n(function(){Qd(e)&&Pd(t)})}function Qd(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!ge(t,n)}catch{return!0}}function Pd(t){var e=_a(t,2);e!==null&&Ae(e,t,2)}function ko(t){var e=ce();if(typeof t=="function"){var n=t;if(t=n(),ua){Sn(!0);try{n()}finally{Sn(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:rn,lastRenderedState:t},e}function Jd(t,e,n,l){return t.baseState=n,_o(t,St,typeof l=="function"?l:rn)}function dx(t,e,n,l,o){if(Ss(t))throw Error(r(485));if(t=e.action,t!==null){var u={payload:o,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(y){u.listeners.push(y)}};k.T!==null?n(!0):u.isTransition=!1,l(u),n=e.pending,n===null?(u.next=e.pending=u,$d(e,u)):(u.next=n.next,e.pending=n.next=u)}}function $d(t,e){var n=e.action,l=e.payload,o=t.state;if(e.isTransition){var u=k.T,y={};k.T=y;try{var x=n(o,l),T=k.S;T!==null&&T(y,x),Fd(t,e,x)}catch(D){Bo(t,e,D)}finally{k.T=u}}else try{u=n(o,l),Fd(t,e,u)}catch(D){Bo(t,e,D)}}function Fd(t,e,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(l){Wd(t,e,l)},function(l){return Bo(t,e,l)}):Wd(t,e,n)}function Wd(t,e,n){e.status="fulfilled",e.value=n,Id(e),t.state=n,e=t.pending,e!==null&&(n=e.next,n===e?t.pending=null:(n=n.next,e.next=n,$d(t,n)))}function Bo(t,e,n){var l=t.pending;if(t.pending=null,l!==null){l=l.next;do e.status="rejected",e.reason=n,Id(e),e=e.next;while(e!==l)}t.action=null}function Id(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function th(t,e){return e}function eh(t,e){if(gt){var n=Et.formState;if(n!==null){t:{var l=ut;if(gt){if(zt){e:{for(var o=zt,u=Ye;o.nodeType!==8;){if(!u){o=null;break e}if(o=Be(o.nextSibling),o===null){o=null;break e}}u=o.data,o=u==="F!"||u==="F"?o:null}if(o){zt=Be(o.nextSibling),l=o.data==="F!";break t}}la(l)}l=!1}l&&(e=n[0])}}return n=ce(),n.memoizedState=n.baseState=e,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:th,lastRenderedState:e},n.queue=l,n=bh.bind(null,ut,l),l.dispatch=n,l=ko(!1),u=Go.bind(null,ut,!1,l.queue),l=ce(),o={state:e,dispatch:null,action:t,pending:null},l.queue=o,n=dx.bind(null,ut,o,u,n),o.dispatch=n,l.memoizedState=t,[e,n,!1]}function nh(t){var e=Ht();return ah(e,St,t)}function ah(t,e,n){if(e=_o(t,e,th)[0],t=vs(rn)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var l=Ki(e)}catch(y){throw y===Bi?ds:y}else l=e;e=Ht();var o=e.queue,u=o.dispatch;return n!==e.memoizedState&&(ut.flags|=2048,Ka(9,xs(),hx.bind(null,o,n),null)),[l,u,t]}function hx(t,e){t.action=e}function ih(t){var e=Ht(),n=St;if(n!==null)return ah(e,n,t);Ht(),e=e.memoizedState,n=Ht();var l=n.queue.dispatch;return n.memoizedState=t,[e,l,!1]}function Ka(t,e,n,l){return t={tag:t,create:n,deps:l,inst:e,next:null},e=ut.updateQueue,e===null&&(e=zo(),ut.updateQueue=e),n=e.lastEffect,n===null?e.lastEffect=t.next=t:(l=n.next,n.next=t,t.next=l,e.lastEffect=t),t}function xs(){return{destroy:void 0,resource:void 0}}function lh(){return Ht().memoizedState}function bs(t,e,n,l){var o=ce();l=l===void 0?null:l,ut.flags|=t,o.memoizedState=Ka(1|e,xs(),n,l)}function Xi(t,e,n,l){var o=Ht();l=l===void 0?null:l;var u=o.memoizedState.inst;St!==null&&l!==null&&Mo(l,St.memoizedState.deps)?o.memoizedState=Ka(e,u,n,l):(ut.flags|=t,o.memoizedState=Ka(1|e,u,n,l))}function sh(t,e){bs(8390656,8,t,e)}function rh(t,e){Xi(2048,8,t,e)}function oh(t,e){return Xi(4,2,t,e)}function uh(t,e){return Xi(4,4,t,e)}function ch(t,e){if(typeof e=="function"){t=t();var n=e(t);return function(){typeof n=="function"?n():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function fh(t,e,n){n=n!=null?n.concat([t]):null,Xi(4,4,ch.bind(null,e,t),n)}function Lo(){}function dh(t,e){var n=Ht();e=e===void 0?null:e;var l=n.memoizedState;return e!==null&&Mo(e,l[1])?l[0]:(n.memoizedState=[t,e],t)}function hh(t,e){var n=Ht();e=e===void 0?null:e;var l=n.memoizedState;if(e!==null&&Mo(e,l[1]))return l[0];if(l=t(),ua){Sn(!0);try{t()}finally{Sn(!1)}}return n.memoizedState=[l,e],l}function Ho(t,e,n){return n===void 0||(Mn&1073741824)!==0?t.memoizedState=e:(t.memoizedState=n,t=ym(),ut.lanes|=t,_n|=t,n)}function mh(t,e,n,l){return ge(n,e)?n:qa.current!==null?(t=Ho(t,n,l),ge(t,e)||(Qt=!0),t):(Mn&42)===0?(Qt=!0,t.memoizedState=n):(t=ym(),ut.lanes|=t,_n|=t,e)}function ph(t,e,n,l,o){var u=K.p;K.p=u!==0&&8>u?u:8;var y=k.T,x={};k.T=x,Go(t,!1,e,n);try{var T=o(),D=k.S;if(D!==null&&D(x,T),T!==null&&typeof T=="object"&&typeof T.then=="function"){var U=ux(T,l);Zi(t,e,U,Te(t))}else Zi(t,e,l,Te(t))}catch(H){Zi(t,e,{then:function(){},status:"rejected",reason:H},Te())}finally{K.p=u,k.T=y}}function mx(){}function qo(t,e,n,l){if(t.tag!==5)throw Error(r(476));var o=yh(t).queue;ph(t,o,e,W,n===null?mx:function(){return gh(t),n(l)})}function yh(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:W,baseState:W,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:rn,lastRenderedState:W},next:null};var n={};return e.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:rn,lastRenderedState:n},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function gh(t){var e=yh(t).next.queue;Zi(t,e,{},Te())}function Yo(){return ne(cl)}function vh(){return Ht().memoizedState}function xh(){return Ht().memoizedState}function px(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var n=Te();t=wn(n);var l=Nn(e,t,n);l!==null&&(Ae(l,e,n),Hi(l,e,n)),e={cache:vo()},t.payload=e;return}e=e.return}}function yx(t,e,n){var l=Te();n={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Ss(t)?Sh(e,n):(n=ro(t,e,n,l),n!==null&&(Ae(n,t,l),Th(n,e,l)))}function bh(t,e,n){var l=Te();Zi(t,e,n,l)}function Zi(t,e,n,l){var o={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Ss(t))Sh(e,o);else{var u=t.alternate;if(t.lanes===0&&(u===null||u.lanes===0)&&(u=e.lastRenderedReducer,u!==null))try{var y=e.lastRenderedState,x=u(y,n);if(o.hasEagerState=!0,o.eagerState=x,ge(x,y))return is(t,e,o,0),Et===null&&as(),!1}catch{}finally{}if(n=ro(t,e,o,l),n!==null)return Ae(n,t,l),Th(n,e,l),!0}return!1}function Go(t,e,n,l){if(l={lane:2,revertLane:Su(),action:l,hasEagerState:!1,eagerState:null,next:null},Ss(t)){if(e)throw Error(r(479))}else e=ro(t,n,l,2),e!==null&&Ae(e,t,2)}function Ss(t){var e=t.alternate;return t===ut||e!==null&&e===ut}function Sh(t,e){Ya=ps=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function Th(t,e,n){if((n&4194048)!==0){var l=e.lanes;l&=t.pendingLanes,n|=l,e.lanes=n,Df(t,n)}}var Ts={readContext:ne,use:gs,useCallback:Ut,useContext:Ut,useEffect:Ut,useImperativeHandle:Ut,useLayoutEffect:Ut,useInsertionEffect:Ut,useMemo:Ut,useReducer:Ut,useRef:Ut,useState:Ut,useDebugValue:Ut,useDeferredValue:Ut,useTransition:Ut,useSyncExternalStore:Ut,useId:Ut,useHostTransitionStatus:Ut,useFormState:Ut,useActionState:Ut,useOptimistic:Ut,useMemoCache:Ut,useCacheRefresh:Ut},Ah={readContext:ne,use:gs,useCallback:function(t,e){return ce().memoizedState=[t,e===void 0?null:e],t},useContext:ne,useEffect:sh,useImperativeHandle:function(t,e,n){n=n!=null?n.concat([t]):null,bs(4194308,4,ch.bind(null,e,t),n)},useLayoutEffect:function(t,e){return bs(4194308,4,t,e)},useInsertionEffect:function(t,e){bs(4,2,t,e)},useMemo:function(t,e){var n=ce();e=e===void 0?null:e;var l=t();if(ua){Sn(!0);try{t()}finally{Sn(!1)}}return n.memoizedState=[l,e],l},useReducer:function(t,e,n){var l=ce();if(n!==void 0){var o=n(e);if(ua){Sn(!0);try{n(e)}finally{Sn(!1)}}}else o=e;return l.memoizedState=l.baseState=o,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:o},l.queue=t,t=t.dispatch=yx.bind(null,ut,t),[l.memoizedState,t]},useRef:function(t){var e=ce();return t={current:t},e.memoizedState=t},useState:function(t){t=ko(t);var e=t.queue,n=bh.bind(null,ut,e);return e.dispatch=n,[t.memoizedState,n]},useDebugValue:Lo,useDeferredValue:function(t,e){var n=ce();return Ho(n,t,e)},useTransition:function(){var t=ko(!1);return t=ph.bind(null,ut,t.queue,!0,!1),ce().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,n){var l=ut,o=ce();if(gt){if(n===void 0)throw Error(r(407));n=n()}else{if(n=e(),Et===null)throw Error(r(349));(ht&124)!==0||Kd(l,e,n)}o.memoizedState=n;var u={value:n,getSnapshot:e};return o.queue=u,sh(Zd.bind(null,l,u,t),[t]),l.flags|=2048,Ka(9,xs(),Xd.bind(null,l,u,n,e),null),n},useId:function(){var t=ce(),e=Et.identifierPrefix;if(gt){var n=an,l=nn;n=(l&~(1<<32-ye(l)-1)).toString(32)+n,e="«"+e+"R"+n,n=ys++,0<n&&(e+="H"+n.toString(32)),e+="»"}else n=cx++,e="«"+e+"r"+n.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:Yo,useFormState:eh,useActionState:eh,useOptimistic:function(t){var e=ce();e.memoizedState=e.baseState=t;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=n,e=Go.bind(null,ut,!0,n),n.dispatch=e,[t,e]},useMemoCache:Vo,useCacheRefresh:function(){return ce().memoizedState=px.bind(null,ut)}},jh={readContext:ne,use:gs,useCallback:dh,useContext:ne,useEffect:rh,useImperativeHandle:fh,useInsertionEffect:oh,useLayoutEffect:uh,useMemo:hh,useReducer:vs,useRef:lh,useState:function(){return vs(rn)},useDebugValue:Lo,useDeferredValue:function(t,e){var n=Ht();return mh(n,St.memoizedState,t,e)},useTransition:function(){var t=vs(rn)[0],e=Ht().memoizedState;return[typeof t=="boolean"?t:Ki(t),e]},useSyncExternalStore:Gd,useId:vh,useHostTransitionStatus:Yo,useFormState:nh,useActionState:nh,useOptimistic:function(t,e){var n=Ht();return Jd(n,St,t,e)},useMemoCache:Vo,useCacheRefresh:xh},gx={readContext:ne,use:gs,useCallback:dh,useContext:ne,useEffect:rh,useImperativeHandle:fh,useInsertionEffect:oh,useLayoutEffect:uh,useMemo:hh,useReducer:Uo,useRef:lh,useState:function(){return Uo(rn)},useDebugValue:Lo,useDeferredValue:function(t,e){var n=Ht();return St===null?Ho(n,t,e):mh(n,St.memoizedState,t,e)},useTransition:function(){var t=Uo(rn)[0],e=Ht().memoizedState;return[typeof t=="boolean"?t:Ki(t),e]},useSyncExternalStore:Gd,useId:vh,useHostTransitionStatus:Yo,useFormState:ih,useActionState:ih,useOptimistic:function(t,e){var n=Ht();return St!==null?Jd(n,St,t,e):(n.baseState=t,[t,n.queue.dispatch])},useMemoCache:Vo,useCacheRefresh:xh},Xa=null,Qi=0;function As(t){var e=Qi;return Qi+=1,Xa===null&&(Xa=[]),_d(Xa,t,e)}function Pi(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function js(t,e){throw e.$$typeof===b?Error(r(525)):(t=Object.prototype.toString.call(e),Error(r(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function Eh(t){var e=t._init;return e(t._payload)}function wh(t){function e(N,j){if(t){var R=N.deletions;R===null?(N.deletions=[j],N.flags|=16):R.push(j)}}function n(N,j){if(!t)return null;for(;j!==null;)e(N,j),j=j.sibling;return null}function l(N){for(var j=new Map;N!==null;)N.key!==null?j.set(N.key,N):j.set(N.index,N),N=N.sibling;return j}function o(N,j){return N=en(N,j),N.index=0,N.sibling=null,N}function u(N,j,R){return N.index=R,t?(R=N.alternate,R!==null?(R=R.index,R<j?(N.flags|=67108866,j):R):(N.flags|=67108866,j)):(N.flags|=1048576,j)}function y(N){return t&&N.alternate===null&&(N.flags|=67108866),N}function x(N,j,R,B){return j===null||j.tag!==6?(j=uo(R,N.mode,B),j.return=N,j):(j=o(j,R),j.return=N,j)}function T(N,j,R,B){var J=R.type;return J===M?U(N,j,R.props.children,B,R.key):j!==null&&(j.elementType===J||typeof J=="object"&&J!==null&&J.$$typeof===F&&Eh(J)===j.type)?(j=o(j,R.props),Pi(j,R),j.return=N,j):(j=ss(R.type,R.key,R.props,null,N.mode,B),Pi(j,R),j.return=N,j)}function D(N,j,R,B){return j===null||j.tag!==4||j.stateNode.containerInfo!==R.containerInfo||j.stateNode.implementation!==R.implementation?(j=co(R,N.mode,B),j.return=N,j):(j=o(j,R.children||[]),j.return=N,j)}function U(N,j,R,B,J){return j===null||j.tag!==7?(j=ea(R,N.mode,B,J),j.return=N,j):(j=o(j,R),j.return=N,j)}function H(N,j,R){if(typeof j=="string"&&j!==""||typeof j=="number"||typeof j=="bigint")return j=uo(""+j,N.mode,R),j.return=N,j;if(typeof j=="object"&&j!==null){switch(j.$$typeof){case S:return R=ss(j.type,j.key,j.props,null,N.mode,R),Pi(R,j),R.return=N,R;case E:return j=co(j,N.mode,R),j.return=N,j;case F:var B=j._init;return j=B(j._payload),H(N,j,R)}if($t(j)||Gt(j))return j=ea(j,N.mode,R,null),j.return=N,j;if(typeof j.then=="function")return H(N,As(j),R);if(j.$$typeof===L)return H(N,cs(N,j),R);js(N,j)}return null}function O(N,j,R,B){var J=j!==null?j.key:null;if(typeof R=="string"&&R!==""||typeof R=="number"||typeof R=="bigint")return J!==null?null:x(N,j,""+R,B);if(typeof R=="object"&&R!==null){switch(R.$$typeof){case S:return R.key===J?T(N,j,R,B):null;case E:return R.key===J?D(N,j,R,B):null;case F:return J=R._init,R=J(R._payload),O(N,j,R,B)}if($t(R)||Gt(R))return J!==null?null:U(N,j,R,B,null);if(typeof R.then=="function")return O(N,j,As(R),B);if(R.$$typeof===L)return O(N,j,cs(N,R),B);js(N,R)}return null}function z(N,j,R,B,J){if(typeof B=="string"&&B!==""||typeof B=="number"||typeof B=="bigint")return N=N.get(R)||null,x(j,N,""+B,J);if(typeof B=="object"&&B!==null){switch(B.$$typeof){case S:return N=N.get(B.key===null?R:B.key)||null,T(j,N,B,J);case E:return N=N.get(B.key===null?R:B.key)||null,D(j,N,B,J);case F:var ct=B._init;return B=ct(B._payload),z(N,j,R,B,J)}if($t(B)||Gt(B))return N=N.get(R)||null,U(j,N,B,J,null);if(typeof B.then=="function")return z(N,j,R,As(B),J);if(B.$$typeof===L)return z(N,j,R,cs(j,B),J);js(j,B)}return null}function it(N,j,R,B){for(var J=null,ct=null,I=j,nt=j=0,Jt=null;I!==null&&nt<R.length;nt++){I.index>nt?(Jt=I,I=null):Jt=I.sibling;var pt=O(N,I,R[nt],B);if(pt===null){I===null&&(I=Jt);break}t&&I&&pt.alternate===null&&e(N,I),j=u(pt,j,nt),ct===null?J=pt:ct.sibling=pt,ct=pt,I=Jt}if(nt===R.length)return n(N,I),gt&&aa(N,nt),J;if(I===null){for(;nt<R.length;nt++)I=H(N,R[nt],B),I!==null&&(j=u(I,j,nt),ct===null?J=I:ct.sibling=I,ct=I);return gt&&aa(N,nt),J}for(I=l(I);nt<R.length;nt++)Jt=z(I,N,nt,R[nt],B),Jt!==null&&(t&&Jt.alternate!==null&&I.delete(Jt.key===null?nt:Jt.key),j=u(Jt,j,nt),ct===null?J=Jt:ct.sibling=Jt,ct=Jt);return t&&I.forEach(function(Kn){return e(N,Kn)}),gt&&aa(N,nt),J}function et(N,j,R,B){if(R==null)throw Error(r(151));for(var J=null,ct=null,I=j,nt=j=0,Jt=null,pt=R.next();I!==null&&!pt.done;nt++,pt=R.next()){I.index>nt?(Jt=I,I=null):Jt=I.sibling;var Kn=O(N,I,pt.value,B);if(Kn===null){I===null&&(I=Jt);break}t&&I&&Kn.alternate===null&&e(N,I),j=u(Kn,j,nt),ct===null?J=Kn:ct.sibling=Kn,ct=Kn,I=Jt}if(pt.done)return n(N,I),gt&&aa(N,nt),J;if(I===null){for(;!pt.done;nt++,pt=R.next())pt=H(N,pt.value,B),pt!==null&&(j=u(pt,j,nt),ct===null?J=pt:ct.sibling=pt,ct=pt);return gt&&aa(N,nt),J}for(I=l(I);!pt.done;nt++,pt=R.next())pt=z(I,N,nt,pt.value,B),pt!==null&&(t&&pt.alternate!==null&&I.delete(pt.key===null?nt:pt.key),j=u(pt,j,nt),ct===null?J=pt:ct.sibling=pt,ct=pt);return t&&I.forEach(function(v1){return e(N,v1)}),gt&&aa(N,nt),J}function At(N,j,R,B){if(typeof R=="object"&&R!==null&&R.type===M&&R.key===null&&(R=R.props.children),typeof R=="object"&&R!==null){switch(R.$$typeof){case S:t:{for(var J=R.key;j!==null;){if(j.key===J){if(J=R.type,J===M){if(j.tag===7){n(N,j.sibling),B=o(j,R.props.children),B.return=N,N=B;break t}}else if(j.elementType===J||typeof J=="object"&&J!==null&&J.$$typeof===F&&Eh(J)===j.type){n(N,j.sibling),B=o(j,R.props),Pi(B,R),B.return=N,N=B;break t}n(N,j);break}else e(N,j);j=j.sibling}R.type===M?(B=ea(R.props.children,N.mode,B,R.key),B.return=N,N=B):(B=ss(R.type,R.key,R.props,null,N.mode,B),Pi(B,R),B.return=N,N=B)}return y(N);case E:t:{for(J=R.key;j!==null;){if(j.key===J)if(j.tag===4&&j.stateNode.containerInfo===R.containerInfo&&j.stateNode.implementation===R.implementation){n(N,j.sibling),B=o(j,R.children||[]),B.return=N,N=B;break t}else{n(N,j);break}else e(N,j);j=j.sibling}B=co(R,N.mode,B),B.return=N,N=B}return y(N);case F:return J=R._init,R=J(R._payload),At(N,j,R,B)}if($t(R))return it(N,j,R,B);if(Gt(R)){if(J=Gt(R),typeof J!="function")throw Error(r(150));return R=J.call(R),et(N,j,R,B)}if(typeof R.then=="function")return At(N,j,As(R),B);if(R.$$typeof===L)return At(N,j,cs(N,R),B);js(N,R)}return typeof R=="string"&&R!==""||typeof R=="number"||typeof R=="bigint"?(R=""+R,j!==null&&j.tag===6?(n(N,j.sibling),B=o(j,R),B.return=N,N=B):(n(N,j),B=uo(R,N.mode,B),B.return=N,N=B),y(N)):n(N,j)}return function(N,j,R,B){try{Qi=0;var J=At(N,j,R,B);return Xa=null,J}catch(I){if(I===Bi||I===ds)throw I;var ct=ve(29,I,null,N.mode);return ct.lanes=B,ct.return=N,ct}finally{}}}var Za=wh(!0),Nh=wh(!1),De=G(null),Ge=null;function Rn(t){var e=t.alternate;X(Xt,Xt.current&1),X(De,t),Ge===null&&(e===null||qa.current!==null||e.memoizedState!==null)&&(Ge=t)}function Mh(t){if(t.tag===22){if(X(Xt,Xt.current),X(De,t),Ge===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(Ge=t)}}else Dn()}function Dn(){X(Xt,Xt.current),X(De,De.current)}function on(t){Q(De),Ge===t&&(Ge=null),Q(Xt)}var Xt=G(0);function Es(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||zu(n)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function Ko(t,e,n,l){e=t.memoizedState,n=n(l,e),n=n==null?e:v({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var Xo={enqueueSetState:function(t,e,n){t=t._reactInternals;var l=Te(),o=wn(l);o.payload=e,n!=null&&(o.callback=n),e=Nn(t,o,l),e!==null&&(Ae(e,t,l),Hi(e,t,l))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var l=Te(),o=wn(l);o.tag=1,o.payload=e,n!=null&&(o.callback=n),e=Nn(t,o,l),e!==null&&(Ae(e,t,l),Hi(e,t,l))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=Te(),l=wn(n);l.tag=2,e!=null&&(l.callback=e),e=Nn(t,l,n),e!==null&&(Ae(e,t,n),Hi(e,t,n))}};function Rh(t,e,n,l,o,u,y){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(l,u,y):e.prototype&&e.prototype.isPureReactComponent?!Di(n,l)||!Di(o,u):!0}function Dh(t,e,n,l){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,l),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,l),e.state!==t&&Xo.enqueueReplaceState(e,e.state,null)}function ca(t,e){var n=e;if("ref"in e){n={};for(var l in e)l!=="ref"&&(n[l]=e[l])}if(t=t.defaultProps){n===e&&(n=v({},n));for(var o in t)n[o]===void 0&&(n[o]=t[o])}return n}var ws=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Ch(t){ws(t)}function Oh(t){console.error(t)}function zh(t){ws(t)}function Ns(t,e){try{var n=t.onUncaughtError;n(e.value,{componentStack:e.stack})}catch(l){setTimeout(function(){throw l})}}function Vh(t,e,n){try{var l=t.onCaughtError;l(n.value,{componentStack:n.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(o){setTimeout(function(){throw o})}}function Zo(t,e,n){return n=wn(n),n.tag=3,n.payload={element:null},n.callback=function(){Ns(t,e)},n}function _h(t){return t=wn(t),t.tag=3,t}function Uh(t,e,n,l){var o=n.type.getDerivedStateFromError;if(typeof o=="function"){var u=l.value;t.payload=function(){return o(u)},t.callback=function(){Vh(e,n,l)}}var y=n.stateNode;y!==null&&typeof y.componentDidCatch=="function"&&(t.callback=function(){Vh(e,n,l),typeof o!="function"&&(Un===null?Un=new Set([this]):Un.add(this));var x=l.stack;this.componentDidCatch(l.value,{componentStack:x!==null?x:""})})}function vx(t,e,n,l,o){if(n.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(e=n.alternate,e!==null&&_i(e,n,o,!0),n=De.current,n!==null){switch(n.tag){case 13:return Ge===null?yu():n.alternate===null&&Vt===0&&(Vt=3),n.flags&=-257,n.flags|=65536,n.lanes=o,l===So?n.flags|=16384:(e=n.updateQueue,e===null?n.updateQueue=new Set([l]):e.add(l),vu(t,l,o)),!1;case 22:return n.flags|=65536,l===So?n.flags|=16384:(e=n.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([l])},n.updateQueue=e):(n=e.retryQueue,n===null?e.retryQueue=new Set([l]):n.add(l)),vu(t,l,o)),!1}throw Error(r(435,n.tag))}return vu(t,l,o),yu(),!1}if(gt)return e=De.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=o,l!==mo&&(t=Error(r(422),{cause:l}),Vi(we(t,n)))):(l!==mo&&(e=Error(r(423),{cause:l}),Vi(we(e,n))),t=t.current.alternate,t.flags|=65536,o&=-o,t.lanes|=o,l=we(l,n),o=Zo(t.stateNode,l,o),jo(t,o),Vt!==4&&(Vt=2)),!1;var u=Error(r(520),{cause:l});if(u=we(u,n),el===null?el=[u]:el.push(u),Vt!==4&&(Vt=2),e===null)return!0;l=we(l,n),n=e;do{switch(n.tag){case 3:return n.flags|=65536,t=o&-o,n.lanes|=t,t=Zo(n.stateNode,l,t),jo(n,t),!1;case 1:if(e=n.type,u=n.stateNode,(n.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&(Un===null||!Un.has(u))))return n.flags|=65536,o&=-o,n.lanes|=o,o=_h(o),Uh(o,t,n,l),jo(n,o),!1}n=n.return}while(n!==null);return!1}var kh=Error(r(461)),Qt=!1;function Ft(t,e,n,l){e.child=t===null?Nh(e,null,n,l):Za(e,t.child,n,l)}function Bh(t,e,n,l,o){n=n.render;var u=e.ref;if("ref"in l){var y={};for(var x in l)x!=="ref"&&(y[x]=l[x])}else y=l;return ra(e),l=Ro(t,e,n,y,u,o),x=Do(),t!==null&&!Qt?(Co(t,e,o),un(t,e,o)):(gt&&x&&fo(e),e.flags|=1,Ft(t,e,l,o),e.child)}function Lh(t,e,n,l,o){if(t===null){var u=n.type;return typeof u=="function"&&!oo(u)&&u.defaultProps===void 0&&n.compare===null?(e.tag=15,e.type=u,Hh(t,e,u,l,o)):(t=ss(n.type,null,l,e,e.mode,o),t.ref=e.ref,t.return=e,e.child=t)}if(u=t.child,!tu(t,o)){var y=u.memoizedProps;if(n=n.compare,n=n!==null?n:Di,n(y,l)&&t.ref===e.ref)return un(t,e,o)}return e.flags|=1,t=en(u,l),t.ref=e.ref,t.return=e,e.child=t}function Hh(t,e,n,l,o){if(t!==null){var u=t.memoizedProps;if(Di(u,l)&&t.ref===e.ref)if(Qt=!1,e.pendingProps=l=u,tu(t,o))(t.flags&131072)!==0&&(Qt=!0);else return e.lanes=t.lanes,un(t,e,o)}return Qo(t,e,n,l,o)}function qh(t,e,n){var l=e.pendingProps,o=l.children,u=t!==null?t.memoizedState:null;if(l.mode==="hidden"){if((e.flags&128)!==0){if(l=u!==null?u.baseLanes|n:n,t!==null){for(o=e.child=t.child,u=0;o!==null;)u=u|o.lanes|o.childLanes,o=o.sibling;e.childLanes=u&~l}else e.childLanes=0,e.child=null;return Yh(t,e,l,n)}if((n&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&fs(e,u!==null?u.cachePool:null),u!==null?Hd(e,u):wo(),Mh(e);else return e.lanes=e.childLanes=536870912,Yh(t,e,u!==null?u.baseLanes|n:n,n)}else u!==null?(fs(e,u.cachePool),Hd(e,u),Dn(),e.memoizedState=null):(t!==null&&fs(e,null),wo(),Dn());return Ft(t,e,o,n),e.child}function Yh(t,e,n,l){var o=bo();return o=o===null?null:{parent:Kt._currentValue,pool:o},e.memoizedState={baseLanes:n,cachePool:o},t!==null&&fs(e,null),wo(),Mh(e),t!==null&&_i(t,e,l,!0),null}function Ms(t,e){var n=e.ref;if(n===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(r(284));(t===null||t.ref!==n)&&(e.flags|=4194816)}}function Qo(t,e,n,l,o){return ra(e),n=Ro(t,e,n,l,void 0,o),l=Do(),t!==null&&!Qt?(Co(t,e,o),un(t,e,o)):(gt&&l&&fo(e),e.flags|=1,Ft(t,e,n,o),e.child)}function Gh(t,e,n,l,o,u){return ra(e),e.updateQueue=null,n=Yd(e,l,n,o),qd(t),l=Do(),t!==null&&!Qt?(Co(t,e,u),un(t,e,u)):(gt&&l&&fo(e),e.flags|=1,Ft(t,e,n,u),e.child)}function Kh(t,e,n,l,o){if(ra(e),e.stateNode===null){var u=Ua,y=n.contextType;typeof y=="object"&&y!==null&&(u=ne(y)),u=new n(l,u),e.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,u.updater=Xo,e.stateNode=u,u._reactInternals=e,u=e.stateNode,u.props=l,u.state=e.memoizedState,u.refs={},To(e),y=n.contextType,u.context=typeof y=="object"&&y!==null?ne(y):Ua,u.state=e.memoizedState,y=n.getDerivedStateFromProps,typeof y=="function"&&(Ko(e,n,y,l),u.state=e.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(y=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),y!==u.state&&Xo.enqueueReplaceState(u,u.state,null),Yi(e,l,u,o),qi(),u.state=e.memoizedState),typeof u.componentDidMount=="function"&&(e.flags|=4194308),l=!0}else if(t===null){u=e.stateNode;var x=e.memoizedProps,T=ca(n,x);u.props=T;var D=u.context,U=n.contextType;y=Ua,typeof U=="object"&&U!==null&&(y=ne(U));var H=n.getDerivedStateFromProps;U=typeof H=="function"||typeof u.getSnapshotBeforeUpdate=="function",x=e.pendingProps!==x,U||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(x||D!==y)&&Dh(e,u,l,y),En=!1;var O=e.memoizedState;u.state=O,Yi(e,l,u,o),qi(),D=e.memoizedState,x||O!==D||En?(typeof H=="function"&&(Ko(e,n,H,l),D=e.memoizedState),(T=En||Rh(e,n,T,l,O,D,y))?(U||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount()),typeof u.componentDidMount=="function"&&(e.flags|=4194308)):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=l,e.memoizedState=D),u.props=l,u.state=D,u.context=y,l=T):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),l=!1)}else{u=e.stateNode,Ao(t,e),y=e.memoizedProps,U=ca(n,y),u.props=U,H=e.pendingProps,O=u.context,D=n.contextType,T=Ua,typeof D=="object"&&D!==null&&(T=ne(D)),x=n.getDerivedStateFromProps,(D=typeof x=="function"||typeof u.getSnapshotBeforeUpdate=="function")||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(y!==H||O!==T)&&Dh(e,u,l,T),En=!1,O=e.memoizedState,u.state=O,Yi(e,l,u,o),qi();var z=e.memoizedState;y!==H||O!==z||En||t!==null&&t.dependencies!==null&&us(t.dependencies)?(typeof x=="function"&&(Ko(e,n,x,l),z=e.memoizedState),(U=En||Rh(e,n,U,l,O,z,T)||t!==null&&t.dependencies!==null&&us(t.dependencies))?(D||typeof u.UNSAFE_componentWillUpdate!="function"&&typeof u.componentWillUpdate!="function"||(typeof u.componentWillUpdate=="function"&&u.componentWillUpdate(l,z,T),typeof u.UNSAFE_componentWillUpdate=="function"&&u.UNSAFE_componentWillUpdate(l,z,T)),typeof u.componentDidUpdate=="function"&&(e.flags|=4),typeof u.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof u.componentDidUpdate!="function"||y===t.memoizedProps&&O===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||y===t.memoizedProps&&O===t.memoizedState||(e.flags|=1024),e.memoizedProps=l,e.memoizedState=z),u.props=l,u.state=z,u.context=T,l=U):(typeof u.componentDidUpdate!="function"||y===t.memoizedProps&&O===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||y===t.memoizedProps&&O===t.memoizedState||(e.flags|=1024),l=!1)}return u=l,Ms(t,e),l=(e.flags&128)!==0,u||l?(u=e.stateNode,n=l&&typeof n.getDerivedStateFromError!="function"?null:u.render(),e.flags|=1,t!==null&&l?(e.child=Za(e,t.child,null,o),e.child=Za(e,null,n,o)):Ft(t,e,n,o),e.memoizedState=u.state,t=e.child):t=un(t,e,o),t}function Xh(t,e,n,l){return zi(),e.flags|=256,Ft(t,e,n,l),e.child}var Po={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Jo(t){return{baseLanes:t,cachePool:Od()}}function $o(t,e,n){return t=t!==null?t.childLanes&~n:0,e&&(t|=Ce),t}function Zh(t,e,n){var l=e.pendingProps,o=!1,u=(e.flags&128)!==0,y;if((y=u)||(y=t!==null&&t.memoizedState===null?!1:(Xt.current&2)!==0),y&&(o=!0,e.flags&=-129),y=(e.flags&32)!==0,e.flags&=-33,t===null){if(gt){if(o?Rn(e):Dn(),gt){var x=zt,T;if(T=x){t:{for(T=x,x=Ye;T.nodeType!==8;){if(!x){x=null;break t}if(T=Be(T.nextSibling),T===null){x=null;break t}}x=T}x!==null?(e.memoizedState={dehydrated:x,treeContext:na!==null?{id:nn,overflow:an}:null,retryLane:536870912,hydrationErrors:null},T=ve(18,null,null,0),T.stateNode=x,T.return=e,e.child=T,le=e,zt=null,T=!0):T=!1}T||la(e)}if(x=e.memoizedState,x!==null&&(x=x.dehydrated,x!==null))return zu(x)?e.lanes=32:e.lanes=536870912,null;on(e)}return x=l.children,l=l.fallback,o?(Dn(),o=e.mode,x=Rs({mode:"hidden",children:x},o),l=ea(l,o,n,null),x.return=e,l.return=e,x.sibling=l,e.child=x,o=e.child,o.memoizedState=Jo(n),o.childLanes=$o(t,y,n),e.memoizedState=Po,l):(Rn(e),Fo(e,x))}if(T=t.memoizedState,T!==null&&(x=T.dehydrated,x!==null)){if(u)e.flags&256?(Rn(e),e.flags&=-257,e=Wo(t,e,n)):e.memoizedState!==null?(Dn(),e.child=t.child,e.flags|=128,e=null):(Dn(),o=l.fallback,x=e.mode,l=Rs({mode:"visible",children:l.children},x),o=ea(o,x,n,null),o.flags|=2,l.return=e,o.return=e,l.sibling=o,e.child=l,Za(e,t.child,null,n),l=e.child,l.memoizedState=Jo(n),l.childLanes=$o(t,y,n),e.memoizedState=Po,e=o);else if(Rn(e),zu(x)){if(y=x.nextSibling&&x.nextSibling.dataset,y)var D=y.dgst;y=D,l=Error(r(419)),l.stack="",l.digest=y,Vi({value:l,source:null,stack:null}),e=Wo(t,e,n)}else if(Qt||_i(t,e,n,!1),y=(n&t.childLanes)!==0,Qt||y){if(y=Et,y!==null&&(l=n&-n,l=(l&42)!==0?1:zr(l),l=(l&(y.suspendedLanes|n))!==0?0:l,l!==0&&l!==T.retryLane))throw T.retryLane=l,_a(t,l),Ae(y,t,l),kh;x.data==="$?"||yu(),e=Wo(t,e,n)}else x.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=T.treeContext,zt=Be(x.nextSibling),le=e,gt=!0,ia=null,Ye=!1,t!==null&&(Me[Re++]=nn,Me[Re++]=an,Me[Re++]=na,nn=t.id,an=t.overflow,na=e),e=Fo(e,l.children),e.flags|=4096);return e}return o?(Dn(),o=l.fallback,x=e.mode,T=t.child,D=T.sibling,l=en(T,{mode:"hidden",children:l.children}),l.subtreeFlags=T.subtreeFlags&65011712,D!==null?o=en(D,o):(o=ea(o,x,n,null),o.flags|=2),o.return=e,l.return=e,l.sibling=o,e.child=l,l=o,o=e.child,x=t.child.memoizedState,x===null?x=Jo(n):(T=x.cachePool,T!==null?(D=Kt._currentValue,T=T.parent!==D?{parent:D,pool:D}:T):T=Od(),x={baseLanes:x.baseLanes|n,cachePool:T}),o.memoizedState=x,o.childLanes=$o(t,y,n),e.memoizedState=Po,l):(Rn(e),n=t.child,t=n.sibling,n=en(n,{mode:"visible",children:l.children}),n.return=e,n.sibling=null,t!==null&&(y=e.deletions,y===null?(e.deletions=[t],e.flags|=16):y.push(t)),e.child=n,e.memoizedState=null,n)}function Fo(t,e){return e=Rs({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function Rs(t,e){return t=ve(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function Wo(t,e,n){return Za(e,t.child,null,n),t=Fo(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function Qh(t,e,n){t.lanes|=e;var l=t.alternate;l!==null&&(l.lanes|=e),yo(t.return,e,n)}function Io(t,e,n,l,o){var u=t.memoizedState;u===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:l,tail:n,tailMode:o}:(u.isBackwards=e,u.rendering=null,u.renderingStartTime=0,u.last=l,u.tail=n,u.tailMode=o)}function Ph(t,e,n){var l=e.pendingProps,o=l.revealOrder,u=l.tail;if(Ft(t,e,l.children,n),l=Xt.current,(l&2)!==0)l=l&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&Qh(t,n,e);else if(t.tag===19)Qh(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}l&=1}switch(X(Xt,l),o){case"forwards":for(n=e.child,o=null;n!==null;)t=n.alternate,t!==null&&Es(t)===null&&(o=n),n=n.sibling;n=o,n===null?(o=e.child,e.child=null):(o=n.sibling,n.sibling=null),Io(e,!1,o,n,u);break;case"backwards":for(n=null,o=e.child,e.child=null;o!==null;){if(t=o.alternate,t!==null&&Es(t)===null){e.child=o;break}t=o.sibling,o.sibling=n,n=o,o=t}Io(e,!0,n,null,u);break;case"together":Io(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function un(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),_n|=e.lanes,(n&e.childLanes)===0)if(t!==null){if(_i(t,e,n,!1),(n&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(r(153));if(e.child!==null){for(t=e.child,n=en(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=en(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function tu(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&us(t)))}function xx(t,e,n){switch(e.tag){case 3:wt(e,e.stateNode.containerInfo),jn(e,Kt,t.memoizedState.cache),zi();break;case 27:case 5:Mr(e);break;case 4:wt(e,e.stateNode.containerInfo);break;case 10:jn(e,e.type,e.memoizedProps.value);break;case 13:var l=e.memoizedState;if(l!==null)return l.dehydrated!==null?(Rn(e),e.flags|=128,null):(n&e.child.childLanes)!==0?Zh(t,e,n):(Rn(e),t=un(t,e,n),t!==null?t.sibling:null);Rn(e);break;case 19:var o=(t.flags&128)!==0;if(l=(n&e.childLanes)!==0,l||(_i(t,e,n,!1),l=(n&e.childLanes)!==0),o){if(l)return Ph(t,e,n);e.flags|=128}if(o=e.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),X(Xt,Xt.current),l)break;return null;case 22:case 23:return e.lanes=0,qh(t,e,n);case 24:jn(e,Kt,t.memoizedState.cache)}return un(t,e,n)}function Jh(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps)Qt=!0;else{if(!tu(t,n)&&(e.flags&128)===0)return Qt=!1,xx(t,e,n);Qt=(t.flags&131072)!==0}else Qt=!1,gt&&(e.flags&1048576)!==0&&Ed(e,os,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var l=e.elementType,o=l._init;if(l=o(l._payload),e.type=l,typeof l=="function")oo(l)?(t=ca(l,t),e.tag=1,e=Kh(null,e,l,t,n)):(e.tag=0,e=Qo(null,e,l,t,n));else{if(l!=null){if(o=l.$$typeof,o===P){e.tag=11,e=Bh(null,e,l,t,n);break t}else if(o===at){e.tag=14,e=Lh(null,e,l,t,n);break t}}throw e=He(l)||l,Error(r(306,e,""))}}return e;case 0:return Qo(t,e,e.type,e.pendingProps,n);case 1:return l=e.type,o=ca(l,e.pendingProps),Kh(t,e,l,o,n);case 3:t:{if(wt(e,e.stateNode.containerInfo),t===null)throw Error(r(387));l=e.pendingProps;var u=e.memoizedState;o=u.element,Ao(t,e),Yi(e,l,null,n);var y=e.memoizedState;if(l=y.cache,jn(e,Kt,l),l!==u.cache&&go(e,[Kt],n,!0),qi(),l=y.element,u.isDehydrated)if(u={element:l,isDehydrated:!1,cache:y.cache},e.updateQueue.baseState=u,e.memoizedState=u,e.flags&256){e=Xh(t,e,l,n);break t}else if(l!==o){o=we(Error(r(424)),e),Vi(o),e=Xh(t,e,l,n);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(zt=Be(t.firstChild),le=e,gt=!0,ia=null,Ye=!0,n=Nh(e,null,l,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(zi(),l===o){e=un(t,e,n);break t}Ft(t,e,l,n)}e=e.child}return e;case 26:return Ms(t,e),t===null?(n=Im(e.type,null,e.pendingProps,null))?e.memoizedState=n:gt||(n=e.type,t=e.pendingProps,l=Gs(st.current).createElement(n),l[ee]=e,l[oe]=t,It(l,n,t),Zt(l),e.stateNode=l):e.memoizedState=Im(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return Mr(e),t===null&&gt&&(l=e.stateNode=$m(e.type,e.pendingProps,st.current),le=e,Ye=!0,o=zt,Ln(e.type)?(Vu=o,zt=Be(l.firstChild)):zt=o),Ft(t,e,e.pendingProps.children,n),Ms(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&gt&&((o=l=zt)&&(l=Qx(l,e.type,e.pendingProps,Ye),l!==null?(e.stateNode=l,le=e,zt=Be(l.firstChild),Ye=!1,o=!0):o=!1),o||la(e)),Mr(e),o=e.type,u=e.pendingProps,y=t!==null?t.memoizedProps:null,l=u.children,Du(o,u)?l=null:y!==null&&Du(o,y)&&(e.flags|=32),e.memoizedState!==null&&(o=Ro(t,e,fx,null,null,n),cl._currentValue=o),Ms(t,e),Ft(t,e,l,n),e.child;case 6:return t===null&&gt&&((t=n=zt)&&(n=Px(n,e.pendingProps,Ye),n!==null?(e.stateNode=n,le=e,zt=null,t=!0):t=!1),t||la(e)),null;case 13:return Zh(t,e,n);case 4:return wt(e,e.stateNode.containerInfo),l=e.pendingProps,t===null?e.child=Za(e,null,l,n):Ft(t,e,l,n),e.child;case 11:return Bh(t,e,e.type,e.pendingProps,n);case 7:return Ft(t,e,e.pendingProps,n),e.child;case 8:return Ft(t,e,e.pendingProps.children,n),e.child;case 12:return Ft(t,e,e.pendingProps.children,n),e.child;case 10:return l=e.pendingProps,jn(e,e.type,l.value),Ft(t,e,l.children,n),e.child;case 9:return o=e.type._context,l=e.pendingProps.children,ra(e),o=ne(o),l=l(o),e.flags|=1,Ft(t,e,l,n),e.child;case 14:return Lh(t,e,e.type,e.pendingProps,n);case 15:return Hh(t,e,e.type,e.pendingProps,n);case 19:return Ph(t,e,n);case 31:return l=e.pendingProps,n=e.mode,l={mode:l.mode,children:l.children},t===null?(n=Rs(l,n),n.ref=e.ref,e.child=n,n.return=e,e=n):(n=en(t.child,l),n.ref=e.ref,e.child=n,n.return=e,e=n),e;case 22:return qh(t,e,n);case 24:return ra(e),l=ne(Kt),t===null?(o=bo(),o===null&&(o=Et,u=vo(),o.pooledCache=u,u.refCount++,u!==null&&(o.pooledCacheLanes|=n),o=u),e.memoizedState={parent:l,cache:o},To(e),jn(e,Kt,o)):((t.lanes&n)!==0&&(Ao(t,e),Yi(e,null,null,n),qi()),o=t.memoizedState,u=e.memoizedState,o.parent!==l?(o={parent:l,cache:l},e.memoizedState=o,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=o),jn(e,Kt,l)):(l=u.cache,jn(e,Kt,l),l!==o.cache&&go(e,[Kt],n,!0))),Ft(t,e,e.pendingProps.children,n),e.child;case 29:throw e.pendingProps}throw Error(r(156,e.tag))}function cn(t){t.flags|=4}function $h(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!ip(e)){if(e=De.current,e!==null&&((ht&4194048)===ht?Ge!==null:(ht&62914560)!==ht&&(ht&536870912)===0||e!==Ge))throw Li=So,zd;t.flags|=8192}}function Ds(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?Mf():536870912,t.lanes|=e,$a|=e)}function Ji(t,e){if(!gt)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var l=null;n!==null;)n.alternate!==null&&(l=n),n=n.sibling;l===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:l.sibling=null}}function Mt(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,l=0;if(e)for(var o=t.child;o!==null;)n|=o.lanes|o.childLanes,l|=o.subtreeFlags&65011712,l|=o.flags&65011712,o.return=t,o=o.sibling;else for(o=t.child;o!==null;)n|=o.lanes|o.childLanes,l|=o.subtreeFlags,l|=o.flags,o.return=t,o=o.sibling;return t.subtreeFlags|=l,t.childLanes=n,e}function bx(t,e,n){var l=e.pendingProps;switch(ho(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Mt(e),null;case 1:return Mt(e),null;case 3:return n=e.stateNode,l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),sn(Kt),bn(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(t===null||t.child===null)&&(Oi(e)?cn(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,Md())),Mt(e),null;case 26:return n=e.memoizedState,t===null?(cn(e),n!==null?(Mt(e),$h(e,n)):(Mt(e),e.flags&=-16777217)):n?n!==t.memoizedState?(cn(e),Mt(e),$h(e,n)):(Mt(e),e.flags&=-16777217):(t.memoizedProps!==l&&cn(e),Mt(e),e.flags&=-16777217),null;case 27:ql(e),n=st.current;var o=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==l&&cn(e);else{if(!l){if(e.stateNode===null)throw Error(r(166));return Mt(e),null}t=tt.current,Oi(e)?wd(e):(t=$m(o,l,n),e.stateNode=t,cn(e))}return Mt(e),null;case 5:if(ql(e),n=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==l&&cn(e);else{if(!l){if(e.stateNode===null)throw Error(r(166));return Mt(e),null}if(t=tt.current,Oi(e))wd(e);else{switch(o=Gs(st.current),t){case 1:t=o.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:t=o.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":t=o.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":t=o.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":t=o.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof l.is=="string"?o.createElement("select",{is:l.is}):o.createElement("select"),l.multiple?t.multiple=!0:l.size&&(t.size=l.size);break;default:t=typeof l.is=="string"?o.createElement(n,{is:l.is}):o.createElement(n)}}t[ee]=e,t[oe]=l;t:for(o=e.child;o!==null;){if(o.tag===5||o.tag===6)t.appendChild(o.stateNode);else if(o.tag!==4&&o.tag!==27&&o.child!==null){o.child.return=o,o=o.child;continue}if(o===e)break t;for(;o.sibling===null;){if(o.return===null||o.return===e)break t;o=o.return}o.sibling.return=o.return,o=o.sibling}e.stateNode=t;t:switch(It(t,n,l),n){case"button":case"input":case"select":case"textarea":t=!!l.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&cn(e)}}return Mt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==l&&cn(e);else{if(typeof l!="string"&&e.stateNode===null)throw Error(r(166));if(t=st.current,Oi(e)){if(t=e.stateNode,n=e.memoizedProps,l=null,o=le,o!==null)switch(o.tag){case 27:case 5:l=o.memoizedProps}t[ee]=e,t=!!(t.nodeValue===n||l!==null&&l.suppressHydrationWarning===!0||Gm(t.nodeValue,n)),t||la(e)}else t=Gs(t).createTextNode(l),t[ee]=e,e.stateNode=t}return Mt(e),null;case 13:if(l=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(o=Oi(e),l!==null&&l.dehydrated!==null){if(t===null){if(!o)throw Error(r(318));if(o=e.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(r(317));o[ee]=e}else zi(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Mt(e),o=!1}else o=Md(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=o),o=!0;if(!o)return e.flags&256?(on(e),e):(on(e),null)}if(on(e),(e.flags&128)!==0)return e.lanes=n,e;if(n=l!==null,t=t!==null&&t.memoizedState!==null,n){l=e.child,o=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(o=l.alternate.memoizedState.cachePool.pool);var u=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(u=l.memoizedState.cachePool.pool),u!==o&&(l.flags|=2048)}return n!==t&&n&&(e.child.flags|=8192),Ds(e,e.updateQueue),Mt(e),null;case 4:return bn(),t===null&&Eu(e.stateNode.containerInfo),Mt(e),null;case 10:return sn(e.type),Mt(e),null;case 19:if(Q(Xt),o=e.memoizedState,o===null)return Mt(e),null;if(l=(e.flags&128)!==0,u=o.rendering,u===null)if(l)Ji(o,!1);else{if(Vt!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(u=Es(t),u!==null){for(e.flags|=128,Ji(o,!1),t=u.updateQueue,e.updateQueue=t,Ds(e,t),e.subtreeFlags=0,t=n,n=e.child;n!==null;)jd(n,t),n=n.sibling;return X(Xt,Xt.current&1|2),e.child}t=t.sibling}o.tail!==null&&qe()>zs&&(e.flags|=128,l=!0,Ji(o,!1),e.lanes=4194304)}else{if(!l)if(t=Es(u),t!==null){if(e.flags|=128,l=!0,t=t.updateQueue,e.updateQueue=t,Ds(e,t),Ji(o,!0),o.tail===null&&o.tailMode==="hidden"&&!u.alternate&&!gt)return Mt(e),null}else 2*qe()-o.renderingStartTime>zs&&n!==536870912&&(e.flags|=128,l=!0,Ji(o,!1),e.lanes=4194304);o.isBackwards?(u.sibling=e.child,e.child=u):(t=o.last,t!==null?t.sibling=u:e.child=u,o.last=u)}return o.tail!==null?(e=o.tail,o.rendering=e,o.tail=e.sibling,o.renderingStartTime=qe(),e.sibling=null,t=Xt.current,X(Xt,l?t&1|2:t&1),e):(Mt(e),null);case 22:case 23:return on(e),No(),l=e.memoizedState!==null,t!==null?t.memoizedState!==null!==l&&(e.flags|=8192):l&&(e.flags|=8192),l?(n&536870912)!==0&&(e.flags&128)===0&&(Mt(e),e.subtreeFlags&6&&(e.flags|=8192)):Mt(e),n=e.updateQueue,n!==null&&Ds(e,n.retryQueue),n=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),l=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),l!==n&&(e.flags|=2048),t!==null&&Q(oa),null;case 24:return n=null,t!==null&&(n=t.memoizedState.cache),e.memoizedState.cache!==n&&(e.flags|=2048),sn(Kt),Mt(e),null;case 25:return null;case 30:return null}throw Error(r(156,e.tag))}function Sx(t,e){switch(ho(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return sn(Kt),bn(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return ql(e),null;case 13:if(on(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(r(340));zi()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return Q(Xt),null;case 4:return bn(),null;case 10:return sn(e.type),null;case 22:case 23:return on(e),No(),t!==null&&Q(oa),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return sn(Kt),null;case 25:return null;default:return null}}function Fh(t,e){switch(ho(e),e.tag){case 3:sn(Kt),bn();break;case 26:case 27:case 5:ql(e);break;case 4:bn();break;case 13:on(e);break;case 19:Q(Xt);break;case 10:sn(e.type);break;case 22:case 23:on(e),No(),t!==null&&Q(oa);break;case 24:sn(Kt)}}function $i(t,e){try{var n=e.updateQueue,l=n!==null?n.lastEffect:null;if(l!==null){var o=l.next;n=o;do{if((n.tag&t)===t){l=void 0;var u=n.create,y=n.inst;l=u(),y.destroy=l}n=n.next}while(n!==o)}}catch(x){jt(e,e.return,x)}}function Cn(t,e,n){try{var l=e.updateQueue,o=l!==null?l.lastEffect:null;if(o!==null){var u=o.next;l=u;do{if((l.tag&t)===t){var y=l.inst,x=y.destroy;if(x!==void 0){y.destroy=void 0,o=e;var T=n,D=x;try{D()}catch(U){jt(o,T,U)}}}l=l.next}while(l!==u)}}catch(U){jt(e,e.return,U)}}function Wh(t){var e=t.updateQueue;if(e!==null){var n=t.stateNode;try{Ld(e,n)}catch(l){jt(t,t.return,l)}}}function Ih(t,e,n){n.props=ca(t.type,t.memoizedProps),n.state=t.memoizedState;try{n.componentWillUnmount()}catch(l){jt(t,e,l)}}function Fi(t,e){try{var n=t.ref;if(n!==null){switch(t.tag){case 26:case 27:case 5:var l=t.stateNode;break;case 30:l=t.stateNode;break;default:l=t.stateNode}typeof n=="function"?t.refCleanup=n(l):n.current=l}}catch(o){jt(t,e,o)}}function Ke(t,e){var n=t.ref,l=t.refCleanup;if(n!==null)if(typeof l=="function")try{l()}catch(o){jt(t,e,o)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(o){jt(t,e,o)}else n.current=null}function tm(t){var e=t.type,n=t.memoizedProps,l=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":n.autoFocus&&l.focus();break t;case"img":n.src?l.src=n.src:n.srcSet&&(l.srcset=n.srcSet)}}catch(o){jt(t,t.return,o)}}function eu(t,e,n){try{var l=t.stateNode;Yx(l,t.type,n,e),l[oe]=e}catch(o){jt(t,t.return,o)}}function em(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&Ln(t.type)||t.tag===4}function nu(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||em(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&Ln(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function au(t,e,n){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(t,e):(e=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,e.appendChild(t),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=Ys));else if(l!==4&&(l===27&&Ln(t.type)&&(n=t.stateNode,e=null),t=t.child,t!==null))for(au(t,e,n),t=t.sibling;t!==null;)au(t,e,n),t=t.sibling}function Cs(t,e,n){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(l!==4&&(l===27&&Ln(t.type)&&(n=t.stateNode),t=t.child,t!==null))for(Cs(t,e,n),t=t.sibling;t!==null;)Cs(t,e,n),t=t.sibling}function nm(t){var e=t.stateNode,n=t.memoizedProps;try{for(var l=t.type,o=e.attributes;o.length;)e.removeAttributeNode(o[0]);It(e,l,n),e[ee]=t,e[oe]=n}catch(u){jt(t,t.return,u)}}var fn=!1,kt=!1,iu=!1,am=typeof WeakSet=="function"?WeakSet:Set,Pt=null;function Tx(t,e){if(t=t.containerInfo,Mu=Js,t=md(t),eo(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else t:{n=(n=t.ownerDocument)&&n.defaultView||window;var l=n.getSelection&&n.getSelection();if(l&&l.rangeCount!==0){n=l.anchorNode;var o=l.anchorOffset,u=l.focusNode;l=l.focusOffset;try{n.nodeType,u.nodeType}catch{n=null;break t}var y=0,x=-1,T=-1,D=0,U=0,H=t,O=null;e:for(;;){for(var z;H!==n||o!==0&&H.nodeType!==3||(x=y+o),H!==u||l!==0&&H.nodeType!==3||(T=y+l),H.nodeType===3&&(y+=H.nodeValue.length),(z=H.firstChild)!==null;)O=H,H=z;for(;;){if(H===t)break e;if(O===n&&++D===o&&(x=y),O===u&&++U===l&&(T=y),(z=H.nextSibling)!==null)break;H=O,O=H.parentNode}H=z}n=x===-1||T===-1?null:{start:x,end:T}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ru={focusedElem:t,selectionRange:n},Js=!1,Pt=e;Pt!==null;)if(e=Pt,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,Pt=t;else for(;Pt!==null;){switch(e=Pt,u=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&u!==null){t=void 0,n=e,o=u.memoizedProps,u=u.memoizedState,l=n.stateNode;try{var it=ca(n.type,o,n.elementType===n.type);t=l.getSnapshotBeforeUpdate(it,u),l.__reactInternalSnapshotBeforeUpdate=t}catch(et){jt(n,n.return,et)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,n=t.nodeType,n===9)Ou(t);else if(n===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":Ou(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(r(163))}if(t=e.sibling,t!==null){t.return=e.return,Pt=t;break}Pt=e.return}}function im(t,e,n){var l=n.flags;switch(n.tag){case 0:case 11:case 15:On(t,n),l&4&&$i(5,n);break;case 1:if(On(t,n),l&4)if(t=n.stateNode,e===null)try{t.componentDidMount()}catch(y){jt(n,n.return,y)}else{var o=ca(n.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(o,e,t.__reactInternalSnapshotBeforeUpdate)}catch(y){jt(n,n.return,y)}}l&64&&Wh(n),l&512&&Fi(n,n.return);break;case 3:if(On(t,n),l&64&&(t=n.updateQueue,t!==null)){if(e=null,n.child!==null)switch(n.child.tag){case 27:case 5:e=n.child.stateNode;break;case 1:e=n.child.stateNode}try{Ld(t,e)}catch(y){jt(n,n.return,y)}}break;case 27:e===null&&l&4&&nm(n);case 26:case 5:On(t,n),e===null&&l&4&&tm(n),l&512&&Fi(n,n.return);break;case 12:On(t,n);break;case 13:On(t,n),l&4&&rm(t,n),l&64&&(t=n.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(n=Cx.bind(null,n),Jx(t,n))));break;case 22:if(l=n.memoizedState!==null||fn,!l){e=e!==null&&e.memoizedState!==null||kt,o=fn;var u=kt;fn=l,(kt=e)&&!u?zn(t,n,(n.subtreeFlags&8772)!==0):On(t,n),fn=o,kt=u}break;case 30:break;default:On(t,n)}}function lm(t){var e=t.alternate;e!==null&&(t.alternate=null,lm(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&Ur(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Nt=null,fe=!1;function dn(t,e,n){for(n=n.child;n!==null;)sm(t,e,n),n=n.sibling}function sm(t,e,n){if(pe&&typeof pe.onCommitFiberUnmount=="function")try{pe.onCommitFiberUnmount(vi,n)}catch{}switch(n.tag){case 26:kt||Ke(n,e),dn(t,e,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:kt||Ke(n,e);var l=Nt,o=fe;Ln(n.type)&&(Nt=n.stateNode,fe=!1),dn(t,e,n),sl(n.stateNode),Nt=l,fe=o;break;case 5:kt||Ke(n,e);case 6:if(l=Nt,o=fe,Nt=null,dn(t,e,n),Nt=l,fe=o,Nt!==null)if(fe)try{(Nt.nodeType===9?Nt.body:Nt.nodeName==="HTML"?Nt.ownerDocument.body:Nt).removeChild(n.stateNode)}catch(u){jt(n,e,u)}else try{Nt.removeChild(n.stateNode)}catch(u){jt(n,e,u)}break;case 18:Nt!==null&&(fe?(t=Nt,Pm(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,n.stateNode),ml(t)):Pm(Nt,n.stateNode));break;case 4:l=Nt,o=fe,Nt=n.stateNode.containerInfo,fe=!0,dn(t,e,n),Nt=l,fe=o;break;case 0:case 11:case 14:case 15:kt||Cn(2,n,e),kt||Cn(4,n,e),dn(t,e,n);break;case 1:kt||(Ke(n,e),l=n.stateNode,typeof l.componentWillUnmount=="function"&&Ih(n,e,l)),dn(t,e,n);break;case 21:dn(t,e,n);break;case 22:kt=(l=kt)||n.memoizedState!==null,dn(t,e,n),kt=l;break;default:dn(t,e,n)}}function rm(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{ml(t)}catch(n){jt(e,e.return,n)}}function Ax(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new am),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new am),e;default:throw Error(r(435,t.tag))}}function lu(t,e){var n=Ax(t);e.forEach(function(l){var o=Ox.bind(null,t,l);n.has(l)||(n.add(l),l.then(o,o))})}function xe(t,e){var n=e.deletions;if(n!==null)for(var l=0;l<n.length;l++){var o=n[l],u=t,y=e,x=y;t:for(;x!==null;){switch(x.tag){case 27:if(Ln(x.type)){Nt=x.stateNode,fe=!1;break t}break;case 5:Nt=x.stateNode,fe=!1;break t;case 3:case 4:Nt=x.stateNode.containerInfo,fe=!0;break t}x=x.return}if(Nt===null)throw Error(r(160));sm(u,y,o),Nt=null,fe=!1,u=o.alternate,u!==null&&(u.return=null),o.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)om(e,t),e=e.sibling}var ke=null;function om(t,e){var n=t.alternate,l=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:xe(e,t),be(t),l&4&&(Cn(3,t,t.return),$i(3,t),Cn(5,t,t.return));break;case 1:xe(e,t),be(t),l&512&&(kt||n===null||Ke(n,n.return)),l&64&&fn&&(t=t.updateQueue,t!==null&&(l=t.callbacks,l!==null&&(n=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=n===null?l:n.concat(l))));break;case 26:var o=ke;if(xe(e,t),be(t),l&512&&(kt||n===null||Ke(n,n.return)),l&4){var u=n!==null?n.memoizedState:null;if(l=t.memoizedState,n===null)if(l===null)if(t.stateNode===null){t:{l=t.type,n=t.memoizedProps,o=o.ownerDocument||o;e:switch(l){case"title":u=o.getElementsByTagName("title")[0],(!u||u[Si]||u[ee]||u.namespaceURI==="http://www.w3.org/2000/svg"||u.hasAttribute("itemprop"))&&(u=o.createElement(l),o.head.insertBefore(u,o.querySelector("head > title"))),It(u,l,n),u[ee]=t,Zt(u),l=u;break t;case"link":var y=np("link","href",o).get(l+(n.href||""));if(y){for(var x=0;x<y.length;x++)if(u=y[x],u.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&u.getAttribute("rel")===(n.rel==null?null:n.rel)&&u.getAttribute("title")===(n.title==null?null:n.title)&&u.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){y.splice(x,1);break e}}u=o.createElement(l),It(u,l,n),o.head.appendChild(u);break;case"meta":if(y=np("meta","content",o).get(l+(n.content||""))){for(x=0;x<y.length;x++)if(u=y[x],u.getAttribute("content")===(n.content==null?null:""+n.content)&&u.getAttribute("name")===(n.name==null?null:n.name)&&u.getAttribute("property")===(n.property==null?null:n.property)&&u.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&u.getAttribute("charset")===(n.charSet==null?null:n.charSet)){y.splice(x,1);break e}}u=o.createElement(l),It(u,l,n),o.head.appendChild(u);break;default:throw Error(r(468,l))}u[ee]=t,Zt(u),l=u}t.stateNode=l}else ap(o,t.type,t.stateNode);else t.stateNode=ep(o,l,t.memoizedProps);else u!==l?(u===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):u.count--,l===null?ap(o,t.type,t.stateNode):ep(o,l,t.memoizedProps)):l===null&&t.stateNode!==null&&eu(t,t.memoizedProps,n.memoizedProps)}break;case 27:xe(e,t),be(t),l&512&&(kt||n===null||Ke(n,n.return)),n!==null&&l&4&&eu(t,t.memoizedProps,n.memoizedProps);break;case 5:if(xe(e,t),be(t),l&512&&(kt||n===null||Ke(n,n.return)),t.flags&32){o=t.stateNode;try{Ma(o,"")}catch(z){jt(t,t.return,z)}}l&4&&t.stateNode!=null&&(o=t.memoizedProps,eu(t,o,n!==null?n.memoizedProps:o)),l&1024&&(iu=!0);break;case 6:if(xe(e,t),be(t),l&4){if(t.stateNode===null)throw Error(r(162));l=t.memoizedProps,n=t.stateNode;try{n.nodeValue=l}catch(z){jt(t,t.return,z)}}break;case 3:if(Zs=null,o=ke,ke=Ks(e.containerInfo),xe(e,t),ke=o,be(t),l&4&&n!==null&&n.memoizedState.isDehydrated)try{ml(e.containerInfo)}catch(z){jt(t,t.return,z)}iu&&(iu=!1,um(t));break;case 4:l=ke,ke=Ks(t.stateNode.containerInfo),xe(e,t),be(t),ke=l;break;case 12:xe(e,t),be(t);break;case 13:xe(e,t),be(t),t.child.flags&8192&&t.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(fu=qe()),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,lu(t,l)));break;case 22:o=t.memoizedState!==null;var T=n!==null&&n.memoizedState!==null,D=fn,U=kt;if(fn=D||o,kt=U||T,xe(e,t),kt=U,fn=D,be(t),l&8192)t:for(e=t.stateNode,e._visibility=o?e._visibility&-2:e._visibility|1,o&&(n===null||T||fn||kt||fa(t)),n=null,e=t;;){if(e.tag===5||e.tag===26){if(n===null){T=n=e;try{if(u=T.stateNode,o)y=u.style,typeof y.setProperty=="function"?y.setProperty("display","none","important"):y.display="none";else{x=T.stateNode;var H=T.memoizedProps.style,O=H!=null&&H.hasOwnProperty("display")?H.display:null;x.style.display=O==null||typeof O=="boolean"?"":(""+O).trim()}}catch(z){jt(T,T.return,z)}}}else if(e.tag===6){if(n===null){T=e;try{T.stateNode.nodeValue=o?"":T.memoizedProps}catch(z){jt(T,T.return,z)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;n===e&&(n=null),e=e.return}n===e&&(n=null),e.sibling.return=e.return,e=e.sibling}l&4&&(l=t.updateQueue,l!==null&&(n=l.retryQueue,n!==null&&(l.retryQueue=null,lu(t,n))));break;case 19:xe(e,t),be(t),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,lu(t,l)));break;case 30:break;case 21:break;default:xe(e,t),be(t)}}function be(t){var e=t.flags;if(e&2){try{for(var n,l=t.return;l!==null;){if(em(l)){n=l;break}l=l.return}if(n==null)throw Error(r(160));switch(n.tag){case 27:var o=n.stateNode,u=nu(t);Cs(t,u,o);break;case 5:var y=n.stateNode;n.flags&32&&(Ma(y,""),n.flags&=-33);var x=nu(t);Cs(t,x,y);break;case 3:case 4:var T=n.stateNode.containerInfo,D=nu(t);au(t,D,T);break;default:throw Error(r(161))}}catch(U){jt(t,t.return,U)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function um(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;um(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function On(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)im(t,e.alternate,e),e=e.sibling}function fa(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:Cn(4,e,e.return),fa(e);break;case 1:Ke(e,e.return);var n=e.stateNode;typeof n.componentWillUnmount=="function"&&Ih(e,e.return,n),fa(e);break;case 27:sl(e.stateNode);case 26:case 5:Ke(e,e.return),fa(e);break;case 22:e.memoizedState===null&&fa(e);break;case 30:fa(e);break;default:fa(e)}t=t.sibling}}function zn(t,e,n){for(n=n&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var l=e.alternate,o=t,u=e,y=u.flags;switch(u.tag){case 0:case 11:case 15:zn(o,u,n),$i(4,u);break;case 1:if(zn(o,u,n),l=u,o=l.stateNode,typeof o.componentDidMount=="function")try{o.componentDidMount()}catch(D){jt(l,l.return,D)}if(l=u,o=l.updateQueue,o!==null){var x=l.stateNode;try{var T=o.shared.hiddenCallbacks;if(T!==null)for(o.shared.hiddenCallbacks=null,o=0;o<T.length;o++)Bd(T[o],x)}catch(D){jt(l,l.return,D)}}n&&y&64&&Wh(u),Fi(u,u.return);break;case 27:nm(u);case 26:case 5:zn(o,u,n),n&&l===null&&y&4&&tm(u),Fi(u,u.return);break;case 12:zn(o,u,n);break;case 13:zn(o,u,n),n&&y&4&&rm(o,u);break;case 22:u.memoizedState===null&&zn(o,u,n),Fi(u,u.return);break;case 30:break;default:zn(o,u,n)}e=e.sibling}}function su(t,e){var n=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==n&&(t!=null&&t.refCount++,n!=null&&Ui(n))}function ru(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Ui(t))}function Xe(t,e,n,l){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)cm(t,e,n,l),e=e.sibling}function cm(t,e,n,l){var o=e.flags;switch(e.tag){case 0:case 11:case 15:Xe(t,e,n,l),o&2048&&$i(9,e);break;case 1:Xe(t,e,n,l);break;case 3:Xe(t,e,n,l),o&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Ui(t)));break;case 12:if(o&2048){Xe(t,e,n,l),t=e.stateNode;try{var u=e.memoizedProps,y=u.id,x=u.onPostCommit;typeof x=="function"&&x(y,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(T){jt(e,e.return,T)}}else Xe(t,e,n,l);break;case 13:Xe(t,e,n,l);break;case 23:break;case 22:u=e.stateNode,y=e.alternate,e.memoizedState!==null?u._visibility&2?Xe(t,e,n,l):Wi(t,e):u._visibility&2?Xe(t,e,n,l):(u._visibility|=2,Qa(t,e,n,l,(e.subtreeFlags&10256)!==0)),o&2048&&su(y,e);break;case 24:Xe(t,e,n,l),o&2048&&ru(e.alternate,e);break;default:Xe(t,e,n,l)}}function Qa(t,e,n,l,o){for(o=o&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var u=t,y=e,x=n,T=l,D=y.flags;switch(y.tag){case 0:case 11:case 15:Qa(u,y,x,T,o),$i(8,y);break;case 23:break;case 22:var U=y.stateNode;y.memoizedState!==null?U._visibility&2?Qa(u,y,x,T,o):Wi(u,y):(U._visibility|=2,Qa(u,y,x,T,o)),o&&D&2048&&su(y.alternate,y);break;case 24:Qa(u,y,x,T,o),o&&D&2048&&ru(y.alternate,y);break;default:Qa(u,y,x,T,o)}e=e.sibling}}function Wi(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var n=t,l=e,o=l.flags;switch(l.tag){case 22:Wi(n,l),o&2048&&su(l.alternate,l);break;case 24:Wi(n,l),o&2048&&ru(l.alternate,l);break;default:Wi(n,l)}e=e.sibling}}var Ii=8192;function Pa(t){if(t.subtreeFlags&Ii)for(t=t.child;t!==null;)fm(t),t=t.sibling}function fm(t){switch(t.tag){case 26:Pa(t),t.flags&Ii&&t.memoizedState!==null&&o1(ke,t.memoizedState,t.memoizedProps);break;case 5:Pa(t);break;case 3:case 4:var e=ke;ke=Ks(t.stateNode.containerInfo),Pa(t),ke=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=Ii,Ii=16777216,Pa(t),Ii=e):Pa(t));break;default:Pa(t)}}function dm(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function tl(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var l=e[n];Pt=l,mm(l,t)}dm(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)hm(t),t=t.sibling}function hm(t){switch(t.tag){case 0:case 11:case 15:tl(t),t.flags&2048&&Cn(9,t,t.return);break;case 3:tl(t);break;case 12:tl(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,Os(t)):tl(t);break;default:tl(t)}}function Os(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var l=e[n];Pt=l,mm(l,t)}dm(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:Cn(8,e,e.return),Os(e);break;case 22:n=e.stateNode,n._visibility&2&&(n._visibility&=-3,Os(e));break;default:Os(e)}t=t.sibling}}function mm(t,e){for(;Pt!==null;){var n=Pt;switch(n.tag){case 0:case 11:case 15:Cn(8,n,e);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var l=n.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:Ui(n.memoizedState.cache)}if(l=n.child,l!==null)l.return=n,Pt=l;else t:for(n=t;Pt!==null;){l=Pt;var o=l.sibling,u=l.return;if(lm(l),l===n){Pt=null;break t}if(o!==null){o.return=u,Pt=o;break t}Pt=u}}}var jx={getCacheForType:function(t){var e=ne(Kt),n=e.data.get(t);return n===void 0&&(n=t(),e.data.set(t,n)),n}},Ex=typeof WeakMap=="function"?WeakMap:Map,xt=0,Et=null,ft=null,ht=0,bt=0,Se=null,Vn=!1,Ja=!1,ou=!1,hn=0,Vt=0,_n=0,da=0,uu=0,Ce=0,$a=0,el=null,de=null,cu=!1,fu=0,zs=1/0,Vs=null,Un=null,Wt=0,kn=null,Fa=null,Wa=0,du=0,hu=null,pm=null,nl=0,mu=null;function Te(){if((xt&2)!==0&&ht!==0)return ht&-ht;if(k.T!==null){var t=La;return t!==0?t:Su()}return Cf()}function ym(){Ce===0&&(Ce=(ht&536870912)===0||gt?Nf():536870912);var t=De.current;return t!==null&&(t.flags|=32),Ce}function Ae(t,e,n){(t===Et&&(bt===2||bt===9)||t.cancelPendingCommit!==null)&&(Ia(t,0),Bn(t,ht,Ce,!1)),bi(t,n),((xt&2)===0||t!==Et)&&(t===Et&&((xt&2)===0&&(da|=n),Vt===4&&Bn(t,ht,Ce,!1)),Ze(t))}function gm(t,e,n){if((xt&6)!==0)throw Error(r(327));var l=!n&&(e&124)===0&&(e&t.expiredLanes)===0||xi(t,e),o=l?Mx(t,e):gu(t,e,!0),u=l;do{if(o===0){Ja&&!l&&Bn(t,e,0,!1);break}else{if(n=t.current.alternate,u&&!wx(n)){o=gu(t,e,!1),u=!1;continue}if(o===2){if(u=e,t.errorRecoveryDisabledLanes&u)var y=0;else y=t.pendingLanes&-536870913,y=y!==0?y:y&536870912?536870912:0;if(y!==0){e=y;t:{var x=t;o=el;var T=x.current.memoizedState.isDehydrated;if(T&&(Ia(x,y).flags|=256),y=gu(x,y,!1),y!==2){if(ou&&!T){x.errorRecoveryDisabledLanes|=u,da|=u,o=4;break t}u=de,de=o,u!==null&&(de===null?de=u:de.push.apply(de,u))}o=y}if(u=!1,o!==2)continue}}if(o===1){Ia(t,0),Bn(t,e,0,!0);break}t:{switch(l=t,u=o,u){case 0:case 1:throw Error(r(345));case 4:if((e&4194048)!==e)break;case 6:Bn(l,e,Ce,!Vn);break t;case 2:de=null;break;case 3:case 5:break;default:throw Error(r(329))}if((e&62914560)===e&&(o=fu+300-qe(),10<o)){if(Bn(l,e,Ce,!Vn),Xl(l,0,!0)!==0)break t;l.timeoutHandle=Zm(vm.bind(null,l,n,de,Vs,cu,e,Ce,da,$a,Vn,u,2,-0,0),o);break t}vm(l,n,de,Vs,cu,e,Ce,da,$a,Vn,u,0,-0,0)}}break}while(!0);Ze(t)}function vm(t,e,n,l,o,u,y,x,T,D,U,H,O,z){if(t.timeoutHandle=-1,H=e.subtreeFlags,(H&8192||(H&16785408)===16785408)&&(ul={stylesheets:null,count:0,unsuspend:r1},fm(e),H=u1(),H!==null)){t.cancelPendingCommit=H(Em.bind(null,t,e,u,n,l,o,y,x,T,U,1,O,z)),Bn(t,u,y,!D);return}Em(t,e,u,n,l,o,y,x,T)}function wx(t){for(var e=t;;){var n=e.tag;if((n===0||n===11||n===15)&&e.flags&16384&&(n=e.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var l=0;l<n.length;l++){var o=n[l],u=o.getSnapshot;o=o.value;try{if(!ge(u(),o))return!1}catch{return!1}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Bn(t,e,n,l){e&=~uu,e&=~da,t.suspendedLanes|=e,t.pingedLanes&=~e,l&&(t.warmLanes|=e),l=t.expirationTimes;for(var o=e;0<o;){var u=31-ye(o),y=1<<u;l[u]=-1,o&=~y}n!==0&&Rf(t,n,e)}function _s(){return(xt&6)===0?(al(0),!1):!0}function pu(){if(ft!==null){if(bt===0)var t=ft.return;else t=ft,ln=sa=null,Oo(t),Xa=null,Qi=0,t=ft;for(;t!==null;)Fh(t.alternate,t),t=t.return;ft=null}}function Ia(t,e){var n=t.timeoutHandle;n!==-1&&(t.timeoutHandle=-1,Kx(n)),n=t.cancelPendingCommit,n!==null&&(t.cancelPendingCommit=null,n()),pu(),Et=t,ft=n=en(t.current,null),ht=e,bt=0,Se=null,Vn=!1,Ja=xi(t,e),ou=!1,$a=Ce=uu=da=_n=Vt=0,de=el=null,cu=!1,(e&8)!==0&&(e|=e&32);var l=t.entangledLanes;if(l!==0)for(t=t.entanglements,l&=e;0<l;){var o=31-ye(l),u=1<<o;e|=t[o],l&=~u}return hn=e,as(),n}function xm(t,e){ut=null,k.H=Ts,e===Bi||e===ds?(e=Ud(),bt=3):e===zd?(e=Ud(),bt=4):bt=e===kh?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,Se=e,ft===null&&(Vt=1,Ns(t,we(e,t.current)))}function bm(){var t=k.H;return k.H=Ts,t===null?Ts:t}function Sm(){var t=k.A;return k.A=jx,t}function yu(){Vt=4,Vn||(ht&4194048)!==ht&&De.current!==null||(Ja=!0),(_n&134217727)===0&&(da&134217727)===0||Et===null||Bn(Et,ht,Ce,!1)}function gu(t,e,n){var l=xt;xt|=2;var o=bm(),u=Sm();(Et!==t||ht!==e)&&(Vs=null,Ia(t,e)),e=!1;var y=Vt;t:do try{if(bt!==0&&ft!==null){var x=ft,T=Se;switch(bt){case 8:pu(),y=6;break t;case 3:case 2:case 9:case 6:De.current===null&&(e=!0);var D=bt;if(bt=0,Se=null,ti(t,x,T,D),n&&Ja){y=0;break t}break;default:D=bt,bt=0,Se=null,ti(t,x,T,D)}}Nx(),y=Vt;break}catch(U){xm(t,U)}while(!0);return e&&t.shellSuspendCounter++,ln=sa=null,xt=l,k.H=o,k.A=u,ft===null&&(Et=null,ht=0,as()),y}function Nx(){for(;ft!==null;)Tm(ft)}function Mx(t,e){var n=xt;xt|=2;var l=bm(),o=Sm();Et!==t||ht!==e?(Vs=null,zs=qe()+500,Ia(t,e)):Ja=xi(t,e);t:do try{if(bt!==0&&ft!==null){e=ft;var u=Se;e:switch(bt){case 1:bt=0,Se=null,ti(t,e,u,1);break;case 2:case 9:if(Vd(u)){bt=0,Se=null,Am(e);break}e=function(){bt!==2&&bt!==9||Et!==t||(bt=7),Ze(t)},u.then(e,e);break t;case 3:bt=7;break t;case 4:bt=5;break t;case 7:Vd(u)?(bt=0,Se=null,Am(e)):(bt=0,Se=null,ti(t,e,u,7));break;case 5:var y=null;switch(ft.tag){case 26:y=ft.memoizedState;case 5:case 27:var x=ft;if(!y||ip(y)){bt=0,Se=null;var T=x.sibling;if(T!==null)ft=T;else{var D=x.return;D!==null?(ft=D,Us(D)):ft=null}break e}}bt=0,Se=null,ti(t,e,u,5);break;case 6:bt=0,Se=null,ti(t,e,u,6);break;case 8:pu(),Vt=6;break t;default:throw Error(r(462))}}Rx();break}catch(U){xm(t,U)}while(!0);return ln=sa=null,k.H=l,k.A=o,xt=n,ft!==null?0:(Et=null,ht=0,as(),Vt)}function Rx(){for(;ft!==null&&!Fg();)Tm(ft)}function Tm(t){var e=Jh(t.alternate,t,hn);t.memoizedProps=t.pendingProps,e===null?Us(t):ft=e}function Am(t){var e=t,n=e.alternate;switch(e.tag){case 15:case 0:e=Gh(n,e,e.pendingProps,e.type,void 0,ht);break;case 11:e=Gh(n,e,e.pendingProps,e.type.render,e.ref,ht);break;case 5:Oo(e);default:Fh(n,e),e=ft=jd(e,hn),e=Jh(n,e,hn)}t.memoizedProps=t.pendingProps,e===null?Us(t):ft=e}function ti(t,e,n,l){ln=sa=null,Oo(e),Xa=null,Qi=0;var o=e.return;try{if(vx(t,o,e,n,ht)){Vt=1,Ns(t,we(n,t.current)),ft=null;return}}catch(u){if(o!==null)throw ft=o,u;Vt=1,Ns(t,we(n,t.current)),ft=null;return}e.flags&32768?(gt||l===1?t=!0:Ja||(ht&536870912)!==0?t=!1:(Vn=t=!0,(l===2||l===9||l===3||l===6)&&(l=De.current,l!==null&&l.tag===13&&(l.flags|=16384))),jm(e,t)):Us(e)}function Us(t){var e=t;do{if((e.flags&32768)!==0){jm(e,Vn);return}t=e.return;var n=bx(e.alternate,e,hn);if(n!==null){ft=n;return}if(e=e.sibling,e!==null){ft=e;return}ft=e=t}while(e!==null);Vt===0&&(Vt=5)}function jm(t,e){do{var n=Sx(t.alternate,t);if(n!==null){n.flags&=32767,ft=n;return}if(n=t.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!e&&(t=t.sibling,t!==null)){ft=t;return}ft=t=n}while(t!==null);Vt=6,ft=null}function Em(t,e,n,l,o,u,y,x,T){t.cancelPendingCommit=null;do ks();while(Wt!==0);if((xt&6)!==0)throw Error(r(327));if(e!==null){if(e===t.current)throw Error(r(177));if(u=e.lanes|e.childLanes,u|=so,rv(t,n,u,y,x,T),t===Et&&(ft=Et=null,ht=0),Fa=e,kn=t,Wa=n,du=u,hu=o,pm=l,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,zx(Yl,function(){return Dm(),null})):(t.callbackNode=null,t.callbackPriority=0),l=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||l){l=k.T,k.T=null,o=K.p,K.p=2,y=xt,xt|=4;try{Tx(t,e,n)}finally{xt=y,K.p=o,k.T=l}}Wt=1,wm(),Nm(),Mm()}}function wm(){if(Wt===1){Wt=0;var t=kn,e=Fa,n=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||n){n=k.T,k.T=null;var l=K.p;K.p=2;var o=xt;xt|=4;try{om(e,t);var u=Ru,y=md(t.containerInfo),x=u.focusedElem,T=u.selectionRange;if(y!==x&&x&&x.ownerDocument&&hd(x.ownerDocument.documentElement,x)){if(T!==null&&eo(x)){var D=T.start,U=T.end;if(U===void 0&&(U=D),"selectionStart"in x)x.selectionStart=D,x.selectionEnd=Math.min(U,x.value.length);else{var H=x.ownerDocument||document,O=H&&H.defaultView||window;if(O.getSelection){var z=O.getSelection(),it=x.textContent.length,et=Math.min(T.start,it),At=T.end===void 0?et:Math.min(T.end,it);!z.extend&&et>At&&(y=At,At=et,et=y);var N=dd(x,et),j=dd(x,At);if(N&&j&&(z.rangeCount!==1||z.anchorNode!==N.node||z.anchorOffset!==N.offset||z.focusNode!==j.node||z.focusOffset!==j.offset)){var R=H.createRange();R.setStart(N.node,N.offset),z.removeAllRanges(),et>At?(z.addRange(R),z.extend(j.node,j.offset)):(R.setEnd(j.node,j.offset),z.addRange(R))}}}}for(H=[],z=x;z=z.parentNode;)z.nodeType===1&&H.push({element:z,left:z.scrollLeft,top:z.scrollTop});for(typeof x.focus=="function"&&x.focus(),x=0;x<H.length;x++){var B=H[x];B.element.scrollLeft=B.left,B.element.scrollTop=B.top}}Js=!!Mu,Ru=Mu=null}finally{xt=o,K.p=l,k.T=n}}t.current=e,Wt=2}}function Nm(){if(Wt===2){Wt=0;var t=kn,e=Fa,n=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||n){n=k.T,k.T=null;var l=K.p;K.p=2;var o=xt;xt|=4;try{im(t,e.alternate,e)}finally{xt=o,K.p=l,k.T=n}}Wt=3}}function Mm(){if(Wt===4||Wt===3){Wt=0,Wg();var t=kn,e=Fa,n=Wa,l=pm;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?Wt=5:(Wt=0,Fa=kn=null,Rm(t,t.pendingLanes));var o=t.pendingLanes;if(o===0&&(Un=null),Vr(n),e=e.stateNode,pe&&typeof pe.onCommitFiberRoot=="function")try{pe.onCommitFiberRoot(vi,e,void 0,(e.current.flags&128)===128)}catch{}if(l!==null){e=k.T,o=K.p,K.p=2,k.T=null;try{for(var u=t.onRecoverableError,y=0;y<l.length;y++){var x=l[y];u(x.value,{componentStack:x.stack})}}finally{k.T=e,K.p=o}}(Wa&3)!==0&&ks(),Ze(t),o=t.pendingLanes,(n&4194090)!==0&&(o&42)!==0?t===mu?nl++:(nl=0,mu=t):nl=0,al(0)}}function Rm(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,Ui(e)))}function ks(t){return wm(),Nm(),Mm(),Dm()}function Dm(){if(Wt!==5)return!1;var t=kn,e=du;du=0;var n=Vr(Wa),l=k.T,o=K.p;try{K.p=32>n?32:n,k.T=null,n=hu,hu=null;var u=kn,y=Wa;if(Wt=0,Fa=kn=null,Wa=0,(xt&6)!==0)throw Error(r(331));var x=xt;if(xt|=4,hm(u.current),cm(u,u.current,y,n),xt=x,al(0,!1),pe&&typeof pe.onPostCommitFiberRoot=="function")try{pe.onPostCommitFiberRoot(vi,u)}catch{}return!0}finally{K.p=o,k.T=l,Rm(t,e)}}function Cm(t,e,n){e=we(n,e),e=Zo(t.stateNode,e,2),t=Nn(t,e,2),t!==null&&(bi(t,2),Ze(t))}function jt(t,e,n){if(t.tag===3)Cm(t,t,n);else for(;e!==null;){if(e.tag===3){Cm(e,t,n);break}else if(e.tag===1){var l=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(Un===null||!Un.has(l))){t=we(n,t),n=_h(2),l=Nn(e,n,2),l!==null&&(Uh(n,l,e,t),bi(l,2),Ze(l));break}}e=e.return}}function vu(t,e,n){var l=t.pingCache;if(l===null){l=t.pingCache=new Ex;var o=new Set;l.set(e,o)}else o=l.get(e),o===void 0&&(o=new Set,l.set(e,o));o.has(n)||(ou=!0,o.add(n),t=Dx.bind(null,t,e,n),e.then(t,t))}function Dx(t,e,n){var l=t.pingCache;l!==null&&l.delete(e),t.pingedLanes|=t.suspendedLanes&n,t.warmLanes&=~n,Et===t&&(ht&n)===n&&(Vt===4||Vt===3&&(ht&62914560)===ht&&300>qe()-fu?(xt&2)===0&&Ia(t,0):uu|=n,$a===ht&&($a=0)),Ze(t)}function Om(t,e){e===0&&(e=Mf()),t=_a(t,e),t!==null&&(bi(t,e),Ze(t))}function Cx(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),Om(t,n)}function Ox(t,e){var n=0;switch(t.tag){case 13:var l=t.stateNode,o=t.memoizedState;o!==null&&(n=o.retryLane);break;case 19:l=t.stateNode;break;case 22:l=t.stateNode._retryCache;break;default:throw Error(r(314))}l!==null&&l.delete(e),Om(t,n)}function zx(t,e){return Dr(t,e)}var Bs=null,ei=null,xu=!1,Ls=!1,bu=!1,ha=0;function Ze(t){t!==ei&&t.next===null&&(ei===null?Bs=ei=t:ei=ei.next=t),Ls=!0,xu||(xu=!0,_x())}function al(t,e){if(!bu&&Ls){bu=!0;do for(var n=!1,l=Bs;l!==null;){if(t!==0){var o=l.pendingLanes;if(o===0)var u=0;else{var y=l.suspendedLanes,x=l.pingedLanes;u=(1<<31-ye(42|t)+1)-1,u&=o&~(y&~x),u=u&201326741?u&201326741|1:u?u|2:0}u!==0&&(n=!0,Um(l,u))}else u=ht,u=Xl(l,l===Et?u:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(u&3)===0||xi(l,u)||(n=!0,Um(l,u));l=l.next}while(n);bu=!1}}function Vx(){zm()}function zm(){Ls=xu=!1;var t=0;ha!==0&&(Gx()&&(t=ha),ha=0);for(var e=qe(),n=null,l=Bs;l!==null;){var o=l.next,u=Vm(l,e);u===0?(l.next=null,n===null?Bs=o:n.next=o,o===null&&(ei=n)):(n=l,(t!==0||(u&3)!==0)&&(Ls=!0)),l=o}al(t)}function Vm(t,e){for(var n=t.suspendedLanes,l=t.pingedLanes,o=t.expirationTimes,u=t.pendingLanes&-62914561;0<u;){var y=31-ye(u),x=1<<y,T=o[y];T===-1?((x&n)===0||(x&l)!==0)&&(o[y]=sv(x,e)):T<=e&&(t.expiredLanes|=x),u&=~x}if(e=Et,n=ht,n=Xl(t,t===e?n:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),l=t.callbackNode,n===0||t===e&&(bt===2||bt===9)||t.cancelPendingCommit!==null)return l!==null&&l!==null&&Cr(l),t.callbackNode=null,t.callbackPriority=0;if((n&3)===0||xi(t,n)){if(e=n&-n,e===t.callbackPriority)return e;switch(l!==null&&Cr(l),Vr(n)){case 2:case 8:n=Ef;break;case 32:n=Yl;break;case 268435456:n=wf;break;default:n=Yl}return l=_m.bind(null,t),n=Dr(n,l),t.callbackPriority=e,t.callbackNode=n,e}return l!==null&&l!==null&&Cr(l),t.callbackPriority=2,t.callbackNode=null,2}function _m(t,e){if(Wt!==0&&Wt!==5)return t.callbackNode=null,t.callbackPriority=0,null;var n=t.callbackNode;if(ks()&&t.callbackNode!==n)return null;var l=ht;return l=Xl(t,t===Et?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),l===0?null:(gm(t,l,e),Vm(t,qe()),t.callbackNode!=null&&t.callbackNode===n?_m.bind(null,t):null)}function Um(t,e){if(ks())return null;gm(t,e,!0)}function _x(){Xx(function(){(xt&6)!==0?Dr(jf,Vx):zm()})}function Su(){return ha===0&&(ha=Nf()),ha}function km(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:$l(""+t)}function Bm(t,e){var n=e.ownerDocument.createElement("input");return n.name=e.name,n.value=e.value,t.id&&n.setAttribute("form",t.id),e.parentNode.insertBefore(n,e),t=new FormData(t),n.parentNode.removeChild(n),t}function Ux(t,e,n,l,o){if(e==="submit"&&n&&n.stateNode===o){var u=km((o[oe]||null).action),y=l.submitter;y&&(e=(e=y[oe]||null)?km(e.formAction):y.getAttribute("formAction"),e!==null&&(u=e,y=null));var x=new ts("action","action",null,l,o);t.push({event:x,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(ha!==0){var T=y?Bm(o,y):new FormData(o);qo(n,{pending:!0,data:T,method:o.method,action:u},null,T)}}else typeof u=="function"&&(x.preventDefault(),T=y?Bm(o,y):new FormData(o),qo(n,{pending:!0,data:T,method:o.method,action:u},u,T))},currentTarget:o}]})}}for(var Tu=0;Tu<lo.length;Tu++){var Au=lo[Tu],kx=Au.toLowerCase(),Bx=Au[0].toUpperCase()+Au.slice(1);Ue(kx,"on"+Bx)}Ue(gd,"onAnimationEnd"),Ue(vd,"onAnimationIteration"),Ue(xd,"onAnimationStart"),Ue("dblclick","onDoubleClick"),Ue("focusin","onFocus"),Ue("focusout","onBlur"),Ue(ex,"onTransitionRun"),Ue(nx,"onTransitionStart"),Ue(ax,"onTransitionCancel"),Ue(bd,"onTransitionEnd"),Ea("onMouseEnter",["mouseout","mouseover"]),Ea("onMouseLeave",["mouseout","mouseover"]),Ea("onPointerEnter",["pointerout","pointerover"]),Ea("onPointerLeave",["pointerout","pointerover"]),Fn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Fn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Fn("onBeforeInput",["compositionend","keypress","textInput","paste"]),Fn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Fn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Fn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var il="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Lx=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(il));function Lm(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var l=t[n],o=l.event;l=l.listeners;t:{var u=void 0;if(e)for(var y=l.length-1;0<=y;y--){var x=l[y],T=x.instance,D=x.currentTarget;if(x=x.listener,T!==u&&o.isPropagationStopped())break t;u=x,o.currentTarget=D;try{u(o)}catch(U){ws(U)}o.currentTarget=null,u=T}else for(y=0;y<l.length;y++){if(x=l[y],T=x.instance,D=x.currentTarget,x=x.listener,T!==u&&o.isPropagationStopped())break t;u=x,o.currentTarget=D;try{u(o)}catch(U){ws(U)}o.currentTarget=null,u=T}}}}function dt(t,e){var n=e[_r];n===void 0&&(n=e[_r]=new Set);var l=t+"__bubble";n.has(l)||(Hm(e,t,2,!1),n.add(l))}function ju(t,e,n){var l=0;e&&(l|=4),Hm(n,t,l,e)}var Hs="_reactListening"+Math.random().toString(36).slice(2);function Eu(t){if(!t[Hs]){t[Hs]=!0,zf.forEach(function(n){n!=="selectionchange"&&(Lx.has(n)||ju(n,!1,t),ju(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Hs]||(e[Hs]=!0,ju("selectionchange",!1,e))}}function Hm(t,e,n,l){switch(cp(e)){case 2:var o=d1;break;case 8:o=h1;break;default:o=Lu}n=o.bind(null,e,n,t),o=void 0,!Zr||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(o=!0),l?o!==void 0?t.addEventListener(e,n,{capture:!0,passive:o}):t.addEventListener(e,n,!0):o!==void 0?t.addEventListener(e,n,{passive:o}):t.addEventListener(e,n,!1)}function wu(t,e,n,l,o){var u=l;if((e&1)===0&&(e&2)===0&&l!==null)t:for(;;){if(l===null)return;var y=l.tag;if(y===3||y===4){var x=l.stateNode.containerInfo;if(x===o)break;if(y===4)for(y=l.return;y!==null;){var T=y.tag;if((T===3||T===4)&&y.stateNode.containerInfo===o)return;y=y.return}for(;x!==null;){if(y=Ta(x),y===null)return;if(T=y.tag,T===5||T===6||T===26||T===27){l=u=y;continue t}x=x.parentNode}}l=l.return}Qf(function(){var D=u,U=Kr(n),H=[];t:{var O=Sd.get(t);if(O!==void 0){var z=ts,it=t;switch(t){case"keypress":if(Wl(n)===0)break t;case"keydown":case"keyup":z=zv;break;case"focusin":it="focus",z=$r;break;case"focusout":it="blur",z=$r;break;case"beforeblur":case"afterblur":z=$r;break;case"click":if(n.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":z=$f;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":z=Sv;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":z=Uv;break;case gd:case vd:case xd:z=jv;break;case bd:z=Bv;break;case"scroll":case"scrollend":z=xv;break;case"wheel":z=Hv;break;case"copy":case"cut":case"paste":z=wv;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":z=Wf;break;case"toggle":case"beforetoggle":z=Yv}var et=(e&4)!==0,At=!et&&(t==="scroll"||t==="scrollend"),N=et?O!==null?O+"Capture":null:O;et=[];for(var j=D,R;j!==null;){var B=j;if(R=B.stateNode,B=B.tag,B!==5&&B!==26&&B!==27||R===null||N===null||(B=Ai(j,N),B!=null&&et.push(ll(j,B,R))),At)break;j=j.return}0<et.length&&(O=new z(O,it,null,n,U),H.push({event:O,listeners:et}))}}if((e&7)===0){t:{if(O=t==="mouseover"||t==="pointerover",z=t==="mouseout"||t==="pointerout",O&&n!==Gr&&(it=n.relatedTarget||n.fromElement)&&(Ta(it)||it[Sa]))break t;if((z||O)&&(O=U.window===U?U:(O=U.ownerDocument)?O.defaultView||O.parentWindow:window,z?(it=n.relatedTarget||n.toElement,z=D,it=it?Ta(it):null,it!==null&&(At=d(it),et=it.tag,it!==At||et!==5&&et!==27&&et!==6)&&(it=null)):(z=null,it=D),z!==it)){if(et=$f,B="onMouseLeave",N="onMouseEnter",j="mouse",(t==="pointerout"||t==="pointerover")&&(et=Wf,B="onPointerLeave",N="onPointerEnter",j="pointer"),At=z==null?O:Ti(z),R=it==null?O:Ti(it),O=new et(B,j+"leave",z,n,U),O.target=At,O.relatedTarget=R,B=null,Ta(U)===D&&(et=new et(N,j+"enter",it,n,U),et.target=R,et.relatedTarget=At,B=et),At=B,z&&it)e:{for(et=z,N=it,j=0,R=et;R;R=ni(R))j++;for(R=0,B=N;B;B=ni(B))R++;for(;0<j-R;)et=ni(et),j--;for(;0<R-j;)N=ni(N),R--;for(;j--;){if(et===N||N!==null&&et===N.alternate)break e;et=ni(et),N=ni(N)}et=null}else et=null;z!==null&&qm(H,O,z,et,!1),it!==null&&At!==null&&qm(H,At,it,et,!0)}}t:{if(O=D?Ti(D):window,z=O.nodeName&&O.nodeName.toLowerCase(),z==="select"||z==="input"&&O.type==="file")var J=sd;else if(id(O))if(rd)J=Wv;else{J=$v;var ct=Jv}else z=O.nodeName,!z||z.toLowerCase()!=="input"||O.type!=="checkbox"&&O.type!=="radio"?D&&Yr(D.elementType)&&(J=sd):J=Fv;if(J&&(J=J(t,D))){ld(H,J,n,U);break t}ct&&ct(t,O,D),t==="focusout"&&D&&O.type==="number"&&D.memoizedProps.value!=null&&qr(O,"number",O.value)}switch(ct=D?Ti(D):window,t){case"focusin":(id(ct)||ct.contentEditable==="true")&&(Oa=ct,no=D,Ci=null);break;case"focusout":Ci=no=Oa=null;break;case"mousedown":ao=!0;break;case"contextmenu":case"mouseup":case"dragend":ao=!1,pd(H,n,U);break;case"selectionchange":if(tx)break;case"keydown":case"keyup":pd(H,n,U)}var I;if(Wr)t:{switch(t){case"compositionstart":var nt="onCompositionStart";break t;case"compositionend":nt="onCompositionEnd";break t;case"compositionupdate":nt="onCompositionUpdate";break t}nt=void 0}else Ca?nd(t,n)&&(nt="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&(nt="onCompositionStart");nt&&(If&&n.locale!=="ko"&&(Ca||nt!=="onCompositionStart"?nt==="onCompositionEnd"&&Ca&&(I=Pf()):(An=U,Qr="value"in An?An.value:An.textContent,Ca=!0)),ct=qs(D,nt),0<ct.length&&(nt=new Ff(nt,t,null,n,U),H.push({event:nt,listeners:ct}),I?nt.data=I:(I=ad(n),I!==null&&(nt.data=I)))),(I=Kv?Xv(t,n):Zv(t,n))&&(nt=qs(D,"onBeforeInput"),0<nt.length&&(ct=new Ff("onBeforeInput","beforeinput",null,n,U),H.push({event:ct,listeners:nt}),ct.data=I)),Ux(H,t,D,n,U)}Lm(H,e)})}function ll(t,e,n){return{instance:t,listener:e,currentTarget:n}}function qs(t,e){for(var n=e+"Capture",l=[];t!==null;){var o=t,u=o.stateNode;if(o=o.tag,o!==5&&o!==26&&o!==27||u===null||(o=Ai(t,n),o!=null&&l.unshift(ll(t,o,u)),o=Ai(t,e),o!=null&&l.push(ll(t,o,u))),t.tag===3)return l;t=t.return}return[]}function ni(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function qm(t,e,n,l,o){for(var u=e._reactName,y=[];n!==null&&n!==l;){var x=n,T=x.alternate,D=x.stateNode;if(x=x.tag,T!==null&&T===l)break;x!==5&&x!==26&&x!==27||D===null||(T=D,o?(D=Ai(n,u),D!=null&&y.unshift(ll(n,D,T))):o||(D=Ai(n,u),D!=null&&y.push(ll(n,D,T)))),n=n.return}y.length!==0&&t.push({event:e,listeners:y})}var Hx=/\r\n?/g,qx=/\u0000|\uFFFD/g;function Ym(t){return(typeof t=="string"?t:""+t).replace(Hx,`
`).replace(qx,"")}function Gm(t,e){return e=Ym(e),Ym(t)===e}function Ys(){}function Tt(t,e,n,l,o,u){switch(n){case"children":typeof l=="string"?e==="body"||e==="textarea"&&l===""||Ma(t,l):(typeof l=="number"||typeof l=="bigint")&&e!=="body"&&Ma(t,""+l);break;case"className":Ql(t,"class",l);break;case"tabIndex":Ql(t,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":Ql(t,n,l);break;case"style":Xf(t,l,u);break;case"data":if(e!=="object"){Ql(t,"data",l);break}case"src":case"href":if(l===""&&(e!=="a"||n!=="href")){t.removeAttribute(n);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(n);break}l=$l(""+l),t.setAttribute(n,l);break;case"action":case"formAction":if(typeof l=="function"){t.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof u=="function"&&(n==="formAction"?(e!=="input"&&Tt(t,e,"name",o.name,o,null),Tt(t,e,"formEncType",o.formEncType,o,null),Tt(t,e,"formMethod",o.formMethod,o,null),Tt(t,e,"formTarget",o.formTarget,o,null)):(Tt(t,e,"encType",o.encType,o,null),Tt(t,e,"method",o.method,o,null),Tt(t,e,"target",o.target,o,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(n);break}l=$l(""+l),t.setAttribute(n,l);break;case"onClick":l!=null&&(t.onclick=Ys);break;case"onScroll":l!=null&&dt("scroll",t);break;case"onScrollEnd":l!=null&&dt("scrollend",t);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(r(61));if(n=l.__html,n!=null){if(o.children!=null)throw Error(r(60));t.innerHTML=n}}break;case"multiple":t.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":t.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){t.removeAttribute("xlink:href");break}n=$l(""+l),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,""+l):t.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,""):t.removeAttribute(n);break;case"capture":case"download":l===!0?t.setAttribute(n,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,l):t.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?t.setAttribute(n,l):t.removeAttribute(n);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?t.removeAttribute(n):t.setAttribute(n,l);break;case"popover":dt("beforetoggle",t),dt("toggle",t),Zl(t,"popover",l);break;case"xlinkActuate":Ie(t,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":Ie(t,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":Ie(t,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":Ie(t,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":Ie(t,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":Ie(t,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":Ie(t,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":Ie(t,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":Ie(t,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":Zl(t,"is",l);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=gv.get(n)||n,Zl(t,n,l))}}function Nu(t,e,n,l,o,u){switch(n){case"style":Xf(t,l,u);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(r(61));if(n=l.__html,n!=null){if(o.children!=null)throw Error(r(60));t.innerHTML=n}}break;case"children":typeof l=="string"?Ma(t,l):(typeof l=="number"||typeof l=="bigint")&&Ma(t,""+l);break;case"onScroll":l!=null&&dt("scroll",t);break;case"onScrollEnd":l!=null&&dt("scrollend",t);break;case"onClick":l!=null&&(t.onclick=Ys);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Vf.hasOwnProperty(n))t:{if(n[0]==="o"&&n[1]==="n"&&(o=n.endsWith("Capture"),e=n.slice(2,o?n.length-7:void 0),u=t[oe]||null,u=u!=null?u[n]:null,typeof u=="function"&&t.removeEventListener(e,u,o),typeof l=="function")){typeof u!="function"&&u!==null&&(n in t?t[n]=null:t.hasAttribute(n)&&t.removeAttribute(n)),t.addEventListener(e,l,o);break t}n in t?t[n]=l:l===!0?t.setAttribute(n,""):Zl(t,n,l)}}}function It(t,e,n){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":dt("error",t),dt("load",t);var l=!1,o=!1,u;for(u in n)if(n.hasOwnProperty(u)){var y=n[u];if(y!=null)switch(u){case"src":l=!0;break;case"srcSet":o=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(r(137,e));default:Tt(t,e,u,y,n,null)}}o&&Tt(t,e,"srcSet",n.srcSet,n,null),l&&Tt(t,e,"src",n.src,n,null);return;case"input":dt("invalid",t);var x=u=y=o=null,T=null,D=null;for(l in n)if(n.hasOwnProperty(l)){var U=n[l];if(U!=null)switch(l){case"name":o=U;break;case"type":y=U;break;case"checked":T=U;break;case"defaultChecked":D=U;break;case"value":u=U;break;case"defaultValue":x=U;break;case"children":case"dangerouslySetInnerHTML":if(U!=null)throw Error(r(137,e));break;default:Tt(t,e,l,U,n,null)}}qf(t,u,x,T,D,y,o,!1),Pl(t);return;case"select":dt("invalid",t),l=y=u=null;for(o in n)if(n.hasOwnProperty(o)&&(x=n[o],x!=null))switch(o){case"value":u=x;break;case"defaultValue":y=x;break;case"multiple":l=x;default:Tt(t,e,o,x,n,null)}e=u,n=y,t.multiple=!!l,e!=null?Na(t,!!l,e,!1):n!=null&&Na(t,!!l,n,!0);return;case"textarea":dt("invalid",t),u=o=l=null;for(y in n)if(n.hasOwnProperty(y)&&(x=n[y],x!=null))switch(y){case"value":l=x;break;case"defaultValue":o=x;break;case"children":u=x;break;case"dangerouslySetInnerHTML":if(x!=null)throw Error(r(91));break;default:Tt(t,e,y,x,n,null)}Gf(t,l,o,u),Pl(t);return;case"option":for(T in n)if(n.hasOwnProperty(T)&&(l=n[T],l!=null))switch(T){case"selected":t.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:Tt(t,e,T,l,n,null)}return;case"dialog":dt("beforetoggle",t),dt("toggle",t),dt("cancel",t),dt("close",t);break;case"iframe":case"object":dt("load",t);break;case"video":case"audio":for(l=0;l<il.length;l++)dt(il[l],t);break;case"image":dt("error",t),dt("load",t);break;case"details":dt("toggle",t);break;case"embed":case"source":case"link":dt("error",t),dt("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(D in n)if(n.hasOwnProperty(D)&&(l=n[D],l!=null))switch(D){case"children":case"dangerouslySetInnerHTML":throw Error(r(137,e));default:Tt(t,e,D,l,n,null)}return;default:if(Yr(e)){for(U in n)n.hasOwnProperty(U)&&(l=n[U],l!==void 0&&Nu(t,e,U,l,n,void 0));return}}for(x in n)n.hasOwnProperty(x)&&(l=n[x],l!=null&&Tt(t,e,x,l,n,null))}function Yx(t,e,n,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var o=null,u=null,y=null,x=null,T=null,D=null,U=null;for(z in n){var H=n[z];if(n.hasOwnProperty(z)&&H!=null)switch(z){case"checked":break;case"value":break;case"defaultValue":T=H;default:l.hasOwnProperty(z)||Tt(t,e,z,null,l,H)}}for(var O in l){var z=l[O];if(H=n[O],l.hasOwnProperty(O)&&(z!=null||H!=null))switch(O){case"type":u=z;break;case"name":o=z;break;case"checked":D=z;break;case"defaultChecked":U=z;break;case"value":y=z;break;case"defaultValue":x=z;break;case"children":case"dangerouslySetInnerHTML":if(z!=null)throw Error(r(137,e));break;default:z!==H&&Tt(t,e,O,z,l,H)}}Hr(t,y,x,T,D,U,u,o);return;case"select":z=y=x=O=null;for(u in n)if(T=n[u],n.hasOwnProperty(u)&&T!=null)switch(u){case"value":break;case"multiple":z=T;default:l.hasOwnProperty(u)||Tt(t,e,u,null,l,T)}for(o in l)if(u=l[o],T=n[o],l.hasOwnProperty(o)&&(u!=null||T!=null))switch(o){case"value":O=u;break;case"defaultValue":x=u;break;case"multiple":y=u;default:u!==T&&Tt(t,e,o,u,l,T)}e=x,n=y,l=z,O!=null?Na(t,!!n,O,!1):!!l!=!!n&&(e!=null?Na(t,!!n,e,!0):Na(t,!!n,n?[]:"",!1));return;case"textarea":z=O=null;for(x in n)if(o=n[x],n.hasOwnProperty(x)&&o!=null&&!l.hasOwnProperty(x))switch(x){case"value":break;case"children":break;default:Tt(t,e,x,null,l,o)}for(y in l)if(o=l[y],u=n[y],l.hasOwnProperty(y)&&(o!=null||u!=null))switch(y){case"value":O=o;break;case"defaultValue":z=o;break;case"children":break;case"dangerouslySetInnerHTML":if(o!=null)throw Error(r(91));break;default:o!==u&&Tt(t,e,y,o,l,u)}Yf(t,O,z);return;case"option":for(var it in n)if(O=n[it],n.hasOwnProperty(it)&&O!=null&&!l.hasOwnProperty(it))switch(it){case"selected":t.selected=!1;break;default:Tt(t,e,it,null,l,O)}for(T in l)if(O=l[T],z=n[T],l.hasOwnProperty(T)&&O!==z&&(O!=null||z!=null))switch(T){case"selected":t.selected=O&&typeof O!="function"&&typeof O!="symbol";break;default:Tt(t,e,T,O,l,z)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var et in n)O=n[et],n.hasOwnProperty(et)&&O!=null&&!l.hasOwnProperty(et)&&Tt(t,e,et,null,l,O);for(D in l)if(O=l[D],z=n[D],l.hasOwnProperty(D)&&O!==z&&(O!=null||z!=null))switch(D){case"children":case"dangerouslySetInnerHTML":if(O!=null)throw Error(r(137,e));break;default:Tt(t,e,D,O,l,z)}return;default:if(Yr(e)){for(var At in n)O=n[At],n.hasOwnProperty(At)&&O!==void 0&&!l.hasOwnProperty(At)&&Nu(t,e,At,void 0,l,O);for(U in l)O=l[U],z=n[U],!l.hasOwnProperty(U)||O===z||O===void 0&&z===void 0||Nu(t,e,U,O,l,z);return}}for(var N in n)O=n[N],n.hasOwnProperty(N)&&O!=null&&!l.hasOwnProperty(N)&&Tt(t,e,N,null,l,O);for(H in l)O=l[H],z=n[H],!l.hasOwnProperty(H)||O===z||O==null&&z==null||Tt(t,e,H,O,l,z)}var Mu=null,Ru=null;function Gs(t){return t.nodeType===9?t:t.ownerDocument}function Km(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Xm(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function Du(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var Cu=null;function Gx(){var t=window.event;return t&&t.type==="popstate"?t===Cu?!1:(Cu=t,!0):(Cu=null,!1)}var Zm=typeof setTimeout=="function"?setTimeout:void 0,Kx=typeof clearTimeout=="function"?clearTimeout:void 0,Qm=typeof Promise=="function"?Promise:void 0,Xx=typeof queueMicrotask=="function"?queueMicrotask:typeof Qm<"u"?function(t){return Qm.resolve(null).then(t).catch(Zx)}:Zm;function Zx(t){setTimeout(function(){throw t})}function Ln(t){return t==="head"}function Pm(t,e){var n=e,l=0,o=0;do{var u=n.nextSibling;if(t.removeChild(n),u&&u.nodeType===8)if(n=u.data,n==="/$"){if(0<l&&8>l){n=l;var y=t.ownerDocument;if(n&1&&sl(y.documentElement),n&2&&sl(y.body),n&4)for(n=y.head,sl(n),y=n.firstChild;y;){var x=y.nextSibling,T=y.nodeName;y[Si]||T==="SCRIPT"||T==="STYLE"||T==="LINK"&&y.rel.toLowerCase()==="stylesheet"||n.removeChild(y),y=x}}if(o===0){t.removeChild(u),ml(e);return}o--}else n==="$"||n==="$?"||n==="$!"?o++:l=n.charCodeAt(0)-48;else l=0;n=u}while(n);ml(e)}function Ou(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var n=e;switch(e=e.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":Ou(n),Ur(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}t.removeChild(n)}}function Qx(t,e,n,l){for(;t.nodeType===1;){var o=n;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!l&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(l){if(!t[Si])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(u=t.getAttribute("rel"),u==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(u!==o.rel||t.getAttribute("href")!==(o.href==null||o.href===""?null:o.href)||t.getAttribute("crossorigin")!==(o.crossOrigin==null?null:o.crossOrigin)||t.getAttribute("title")!==(o.title==null?null:o.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(u=t.getAttribute("src"),(u!==(o.src==null?null:o.src)||t.getAttribute("type")!==(o.type==null?null:o.type)||t.getAttribute("crossorigin")!==(o.crossOrigin==null?null:o.crossOrigin))&&u&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var u=o.name==null?null:""+o.name;if(o.type==="hidden"&&t.getAttribute("name")===u)return t}else return t;if(t=Be(t.nextSibling),t===null)break}return null}function Px(t,e,n){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!n||(t=Be(t.nextSibling),t===null))return null;return t}function zu(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function Jx(t,e){var n=t.ownerDocument;if(t.data!=="$?"||n.readyState==="complete")e();else{var l=function(){e(),n.removeEventListener("DOMContentLoaded",l)};n.addEventListener("DOMContentLoaded",l),t._reactRetry=l}}function Be(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var Vu=null;function Jm(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}function $m(t,e,n){switch(e=Gs(n),t){case"html":if(t=e.documentElement,!t)throw Error(r(452));return t;case"head":if(t=e.head,!t)throw Error(r(453));return t;case"body":if(t=e.body,!t)throw Error(r(454));return t;default:throw Error(r(451))}}function sl(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);Ur(t)}var Oe=new Map,Fm=new Set;function Ks(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var mn=K.d;K.d={f:$x,r:Fx,D:Wx,C:Ix,L:t1,m:e1,X:a1,S:n1,M:i1};function $x(){var t=mn.f(),e=_s();return t||e}function Fx(t){var e=Aa(t);e!==null&&e.tag===5&&e.type==="form"?gh(e):mn.r(t)}var ai=typeof document>"u"?null:document;function Wm(t,e,n){var l=ai;if(l&&typeof e=="string"&&e){var o=Ee(e);o='link[rel="'+t+'"][href="'+o+'"]',typeof n=="string"&&(o+='[crossorigin="'+n+'"]'),Fm.has(o)||(Fm.add(o),t={rel:t,crossOrigin:n,href:e},l.querySelector(o)===null&&(e=l.createElement("link"),It(e,"link",t),Zt(e),l.head.appendChild(e)))}}function Wx(t){mn.D(t),Wm("dns-prefetch",t,null)}function Ix(t,e){mn.C(t,e),Wm("preconnect",t,e)}function t1(t,e,n){mn.L(t,e,n);var l=ai;if(l&&t&&e){var o='link[rel="preload"][as="'+Ee(e)+'"]';e==="image"&&n&&n.imageSrcSet?(o+='[imagesrcset="'+Ee(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(o+='[imagesizes="'+Ee(n.imageSizes)+'"]')):o+='[href="'+Ee(t)+'"]';var u=o;switch(e){case"style":u=ii(t);break;case"script":u=li(t)}Oe.has(u)||(t=v({rel:"preload",href:e==="image"&&n&&n.imageSrcSet?void 0:t,as:e},n),Oe.set(u,t),l.querySelector(o)!==null||e==="style"&&l.querySelector(rl(u))||e==="script"&&l.querySelector(ol(u))||(e=l.createElement("link"),It(e,"link",t),Zt(e),l.head.appendChild(e)))}}function e1(t,e){mn.m(t,e);var n=ai;if(n&&t){var l=e&&typeof e.as=="string"?e.as:"script",o='link[rel="modulepreload"][as="'+Ee(l)+'"][href="'+Ee(t)+'"]',u=o;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":u=li(t)}if(!Oe.has(u)&&(t=v({rel:"modulepreload",href:t},e),Oe.set(u,t),n.querySelector(o)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(ol(u)))return}l=n.createElement("link"),It(l,"link",t),Zt(l),n.head.appendChild(l)}}}function n1(t,e,n){mn.S(t,e,n);var l=ai;if(l&&t){var o=ja(l).hoistableStyles,u=ii(t);e=e||"default";var y=o.get(u);if(!y){var x={loading:0,preload:null};if(y=l.querySelector(rl(u)))x.loading=5;else{t=v({rel:"stylesheet",href:t,"data-precedence":e},n),(n=Oe.get(u))&&_u(t,n);var T=y=l.createElement("link");Zt(T),It(T,"link",t),T._p=new Promise(function(D,U){T.onload=D,T.onerror=U}),T.addEventListener("load",function(){x.loading|=1}),T.addEventListener("error",function(){x.loading|=2}),x.loading|=4,Xs(y,e,l)}y={type:"stylesheet",instance:y,count:1,state:x},o.set(u,y)}}}function a1(t,e){mn.X(t,e);var n=ai;if(n&&t){var l=ja(n).hoistableScripts,o=li(t),u=l.get(o);u||(u=n.querySelector(ol(o)),u||(t=v({src:t,async:!0},e),(e=Oe.get(o))&&Uu(t,e),u=n.createElement("script"),Zt(u),It(u,"link",t),n.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},l.set(o,u))}}function i1(t,e){mn.M(t,e);var n=ai;if(n&&t){var l=ja(n).hoistableScripts,o=li(t),u=l.get(o);u||(u=n.querySelector(ol(o)),u||(t=v({src:t,async:!0,type:"module"},e),(e=Oe.get(o))&&Uu(t,e),u=n.createElement("script"),Zt(u),It(u,"link",t),n.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},l.set(o,u))}}function Im(t,e,n,l){var o=(o=st.current)?Ks(o):null;if(!o)throw Error(r(446));switch(t){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(e=ii(n.href),n=ja(o).hoistableStyles,l=n.get(e),l||(l={type:"style",instance:null,count:0,state:null},n.set(e,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){t=ii(n.href);var u=ja(o).hoistableStyles,y=u.get(t);if(y||(o=o.ownerDocument||o,y={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(t,y),(u=o.querySelector(rl(t)))&&!u._p&&(y.instance=u,y.state.loading=5),Oe.has(t)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Oe.set(t,n),u||l1(o,t,n,y.state))),e&&l===null)throw Error(r(528,""));return y}if(e&&l!==null)throw Error(r(529,""));return null;case"script":return e=n.async,n=n.src,typeof n=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=li(n),n=ja(o).hoistableScripts,l=n.get(e),l||(l={type:"script",instance:null,count:0,state:null},n.set(e,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(r(444,t))}}function ii(t){return'href="'+Ee(t)+'"'}function rl(t){return'link[rel="stylesheet"]['+t+"]"}function tp(t){return v({},t,{"data-precedence":t.precedence,precedence:null})}function l1(t,e,n,l){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?l.loading=1:(e=t.createElement("link"),l.preload=e,e.addEventListener("load",function(){return l.loading|=1}),e.addEventListener("error",function(){return l.loading|=2}),It(e,"link",n),Zt(e),t.head.appendChild(e))}function li(t){return'[src="'+Ee(t)+'"]'}function ol(t){return"script[async]"+t}function ep(t,e,n){if(e.count++,e.instance===null)switch(e.type){case"style":var l=t.querySelector('style[data-href~="'+Ee(n.href)+'"]');if(l)return e.instance=l,Zt(l),l;var o=v({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return l=(t.ownerDocument||t).createElement("style"),Zt(l),It(l,"style",o),Xs(l,n.precedence,t),e.instance=l;case"stylesheet":o=ii(n.href);var u=t.querySelector(rl(o));if(u)return e.state.loading|=4,e.instance=u,Zt(u),u;l=tp(n),(o=Oe.get(o))&&_u(l,o),u=(t.ownerDocument||t).createElement("link"),Zt(u);var y=u;return y._p=new Promise(function(x,T){y.onload=x,y.onerror=T}),It(u,"link",l),e.state.loading|=4,Xs(u,n.precedence,t),e.instance=u;case"script":return u=li(n.src),(o=t.querySelector(ol(u)))?(e.instance=o,Zt(o),o):(l=n,(o=Oe.get(u))&&(l=v({},n),Uu(l,o)),t=t.ownerDocument||t,o=t.createElement("script"),Zt(o),It(o,"link",l),t.head.appendChild(o),e.instance=o);case"void":return null;default:throw Error(r(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(l=e.instance,e.state.loading|=4,Xs(l,n.precedence,t));return e.instance}function Xs(t,e,n){for(var l=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),o=l.length?l[l.length-1]:null,u=o,y=0;y<l.length;y++){var x=l[y];if(x.dataset.precedence===e)u=x;else if(u!==o)break}u?u.parentNode.insertBefore(t,u.nextSibling):(e=n.nodeType===9?n.head:n,e.insertBefore(t,e.firstChild))}function _u(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function Uu(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var Zs=null;function np(t,e,n){if(Zs===null){var l=new Map,o=Zs=new Map;o.set(n,l)}else o=Zs,l=o.get(n),l||(l=new Map,o.set(n,l));if(l.has(t))return l;for(l.set(t,null),n=n.getElementsByTagName(t),o=0;o<n.length;o++){var u=n[o];if(!(u[Si]||u[ee]||t==="link"&&u.getAttribute("rel")==="stylesheet")&&u.namespaceURI!=="http://www.w3.org/2000/svg"){var y=u.getAttribute(e)||"";y=t+y;var x=l.get(y);x?x.push(u):l.set(y,[u])}}return l}function ap(t,e,n){t=t.ownerDocument||t,t.head.insertBefore(n,e==="title"?t.querySelector("head > title"):null)}function s1(t,e,n){if(n===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function ip(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var ul=null;function r1(){}function o1(t,e,n){if(ul===null)throw Error(r(475));var l=ul;if(e.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var o=ii(n.href),u=t.querySelector(rl(o));if(u){t=u._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(l.count++,l=Qs.bind(l),t.then(l,l)),e.state.loading|=4,e.instance=u,Zt(u);return}u=t.ownerDocument||t,n=tp(n),(o=Oe.get(o))&&_u(n,o),u=u.createElement("link"),Zt(u);var y=u;y._p=new Promise(function(x,T){y.onload=x,y.onerror=T}),It(u,"link",n),e.instance=u}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(l.count++,e=Qs.bind(l),t.addEventListener("load",e),t.addEventListener("error",e))}}function u1(){if(ul===null)throw Error(r(475));var t=ul;return t.stylesheets&&t.count===0&&ku(t,t.stylesheets),0<t.count?function(e){var n=setTimeout(function(){if(t.stylesheets&&ku(t,t.stylesheets),t.unsuspend){var l=t.unsuspend;t.unsuspend=null,l()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(n)}}:null}function Qs(){if(this.count--,this.count===0){if(this.stylesheets)ku(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var Ps=null;function ku(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,Ps=new Map,e.forEach(c1,t),Ps=null,Qs.call(t))}function c1(t,e){if(!(e.state.loading&4)){var n=Ps.get(t);if(n)var l=n.get(null);else{n=new Map,Ps.set(t,n);for(var o=t.querySelectorAll("link[data-precedence],style[data-precedence]"),u=0;u<o.length;u++){var y=o[u];(y.nodeName==="LINK"||y.getAttribute("media")!=="not all")&&(n.set(y.dataset.precedence,y),l=y)}l&&n.set(null,l)}o=e.instance,y=o.getAttribute("data-precedence"),u=n.get(y)||l,u===l&&n.set(null,o),n.set(y,o),this.count++,l=Qs.bind(this),o.addEventListener("load",l),o.addEventListener("error",l),u?u.parentNode.insertBefore(o,u.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(o,t.firstChild)),e.state.loading|=4}}var cl={$$typeof:L,Provider:null,Consumer:null,_currentValue:W,_currentValue2:W,_threadCount:0};function f1(t,e,n,l,o,u,y,x){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Or(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Or(0),this.hiddenUpdates=Or(null),this.identifierPrefix=l,this.onUncaughtError=o,this.onCaughtError=u,this.onRecoverableError=y,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=x,this.incompleteTransitions=new Map}function lp(t,e,n,l,o,u,y,x,T,D,U,H){return t=new f1(t,e,n,y,x,T,D,H),e=1,u===!0&&(e|=24),u=ve(3,null,null,e),t.current=u,u.stateNode=t,e=vo(),e.refCount++,t.pooledCache=e,e.refCount++,u.memoizedState={element:l,isDehydrated:n,cache:e},To(u),t}function sp(t){return t?(t=Ua,t):Ua}function rp(t,e,n,l,o,u){o=sp(o),l.context===null?l.context=o:l.pendingContext=o,l=wn(e),l.payload={element:n},u=u===void 0?null:u,u!==null&&(l.callback=u),n=Nn(t,l,e),n!==null&&(Ae(n,t,e),Hi(n,t,e))}function op(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function Bu(t,e){op(t,e),(t=t.alternate)&&op(t,e)}function up(t){if(t.tag===13){var e=_a(t,67108864);e!==null&&Ae(e,t,67108864),Bu(t,67108864)}}var Js=!0;function d1(t,e,n,l){var o=k.T;k.T=null;var u=K.p;try{K.p=2,Lu(t,e,n,l)}finally{K.p=u,k.T=o}}function h1(t,e,n,l){var o=k.T;k.T=null;var u=K.p;try{K.p=8,Lu(t,e,n,l)}finally{K.p=u,k.T=o}}function Lu(t,e,n,l){if(Js){var o=Hu(l);if(o===null)wu(t,e,l,$s,n),fp(t,l);else if(p1(o,t,e,n,l))l.stopPropagation();else if(fp(t,l),e&4&&-1<m1.indexOf(t)){for(;o!==null;){var u=Aa(o);if(u!==null)switch(u.tag){case 3:if(u=u.stateNode,u.current.memoizedState.isDehydrated){var y=$n(u.pendingLanes);if(y!==0){var x=u;for(x.pendingLanes|=2,x.entangledLanes|=2;y;){var T=1<<31-ye(y);x.entanglements[1]|=T,y&=~T}Ze(u),(xt&6)===0&&(zs=qe()+500,al(0))}}break;case 13:x=_a(u,2),x!==null&&Ae(x,u,2),_s(),Bu(u,2)}if(u=Hu(l),u===null&&wu(t,e,l,$s,n),u===o)break;o=u}o!==null&&l.stopPropagation()}else wu(t,e,l,null,n)}}function Hu(t){return t=Kr(t),qu(t)}var $s=null;function qu(t){if($s=null,t=Ta(t),t!==null){var e=d(t);if(e===null)t=null;else{var n=e.tag;if(n===13){if(t=f(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return $s=t,null}function cp(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Ig()){case jf:return 2;case Ef:return 8;case Yl:case tv:return 32;case wf:return 268435456;default:return 32}default:return 32}}var Yu=!1,Hn=null,qn=null,Yn=null,fl=new Map,dl=new Map,Gn=[],m1="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function fp(t,e){switch(t){case"focusin":case"focusout":Hn=null;break;case"dragenter":case"dragleave":qn=null;break;case"mouseover":case"mouseout":Yn=null;break;case"pointerover":case"pointerout":fl.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":dl.delete(e.pointerId)}}function hl(t,e,n,l,o,u){return t===null||t.nativeEvent!==u?(t={blockedOn:e,domEventName:n,eventSystemFlags:l,nativeEvent:u,targetContainers:[o]},e!==null&&(e=Aa(e),e!==null&&up(e)),t):(t.eventSystemFlags|=l,e=t.targetContainers,o!==null&&e.indexOf(o)===-1&&e.push(o),t)}function p1(t,e,n,l,o){switch(e){case"focusin":return Hn=hl(Hn,t,e,n,l,o),!0;case"dragenter":return qn=hl(qn,t,e,n,l,o),!0;case"mouseover":return Yn=hl(Yn,t,e,n,l,o),!0;case"pointerover":var u=o.pointerId;return fl.set(u,hl(fl.get(u)||null,t,e,n,l,o)),!0;case"gotpointercapture":return u=o.pointerId,dl.set(u,hl(dl.get(u)||null,t,e,n,l,o)),!0}return!1}function dp(t){var e=Ta(t.target);if(e!==null){var n=d(e);if(n!==null){if(e=n.tag,e===13){if(e=f(n),e!==null){t.blockedOn=e,ov(t.priority,function(){if(n.tag===13){var l=Te();l=zr(l);var o=_a(n,l);o!==null&&Ae(o,n,l),Bu(n,l)}});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function Fs(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=Hu(t.nativeEvent);if(n===null){n=t.nativeEvent;var l=new n.constructor(n.type,n);Gr=l,n.target.dispatchEvent(l),Gr=null}else return e=Aa(n),e!==null&&up(e),t.blockedOn=n,!1;e.shift()}return!0}function hp(t,e,n){Fs(t)&&n.delete(e)}function y1(){Yu=!1,Hn!==null&&Fs(Hn)&&(Hn=null),qn!==null&&Fs(qn)&&(qn=null),Yn!==null&&Fs(Yn)&&(Yn=null),fl.forEach(hp),dl.forEach(hp)}function Ws(t,e){t.blockedOn===e&&(t.blockedOn=null,Yu||(Yu=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,y1)))}var Is=null;function mp(t){Is!==t&&(Is=t,a.unstable_scheduleCallback(a.unstable_NormalPriority,function(){Is===t&&(Is=null);for(var e=0;e<t.length;e+=3){var n=t[e],l=t[e+1],o=t[e+2];if(typeof l!="function"){if(qu(l||n)===null)continue;break}var u=Aa(n);u!==null&&(t.splice(e,3),e-=3,qo(u,{pending:!0,data:o,method:n.method,action:l},l,o))}}))}function ml(t){function e(T){return Ws(T,t)}Hn!==null&&Ws(Hn,t),qn!==null&&Ws(qn,t),Yn!==null&&Ws(Yn,t),fl.forEach(e),dl.forEach(e);for(var n=0;n<Gn.length;n++){var l=Gn[n];l.blockedOn===t&&(l.blockedOn=null)}for(;0<Gn.length&&(n=Gn[0],n.blockedOn===null);)dp(n),n.blockedOn===null&&Gn.shift();if(n=(t.ownerDocument||t).$$reactFormReplay,n!=null)for(l=0;l<n.length;l+=3){var o=n[l],u=n[l+1],y=o[oe]||null;if(typeof u=="function")y||mp(n);else if(y){var x=null;if(u&&u.hasAttribute("formAction")){if(o=u,y=u[oe]||null)x=y.formAction;else if(qu(o)!==null)continue}else x=y.action;typeof x=="function"?n[l+1]=x:(n.splice(l,3),l-=3),mp(n)}}}function Gu(t){this._internalRoot=t}tr.prototype.render=Gu.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(r(409));var n=e.current,l=Te();rp(n,l,t,e,null,null)},tr.prototype.unmount=Gu.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;rp(t.current,2,null,t,null,null),_s(),e[Sa]=null}};function tr(t){this._internalRoot=t}tr.prototype.unstable_scheduleHydration=function(t){if(t){var e=Cf();t={blockedOn:null,target:t,priority:e};for(var n=0;n<Gn.length&&e!==0&&e<Gn[n].priority;n++);Gn.splice(n,0,t),n===0&&dp(t)}};var pp=i.version;if(pp!=="19.1.0")throw Error(r(527,pp,"19.1.0"));K.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(r(188)):(t=Object.keys(t).join(","),Error(r(268,t)));return t=m(e),t=t!==null?h(t):null,t=t===null?null:t.stateNode,t};var g1={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:k,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var er=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!er.isDisabled&&er.supportsFiber)try{vi=er.inject(g1),pe=er}catch{}}return yl.createRoot=function(t,e){if(!c(t))throw Error(r(299));var n=!1,l="",o=Ch,u=Oh,y=zh,x=null;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(l=e.identifierPrefix),e.onUncaughtError!==void 0&&(o=e.onUncaughtError),e.onCaughtError!==void 0&&(u=e.onCaughtError),e.onRecoverableError!==void 0&&(y=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(x=e.unstable_transitionCallbacks)),e=lp(t,1,!1,null,null,n,l,o,u,y,x,null),t[Sa]=e.current,Eu(t),new Gu(e)},yl.hydrateRoot=function(t,e,n){if(!c(t))throw Error(r(299));var l=!1,o="",u=Ch,y=Oh,x=zh,T=null,D=null;return n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onUncaughtError!==void 0&&(u=n.onUncaughtError),n.onCaughtError!==void 0&&(y=n.onCaughtError),n.onRecoverableError!==void 0&&(x=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(T=n.unstable_transitionCallbacks),n.formState!==void 0&&(D=n.formState)),e=lp(t,1,!0,e,n??null,l,o,u,y,x,T,D),e.context=sp(null),n=e.current,l=Te(),l=zr(l),o=wn(l),o.callback=null,Nn(n,o,l),n=l,e.current.lanes=n,bi(e,n),Ze(e),t[Sa]=e.current,Eu(t),new tr(e)},yl.version="19.1.0",yl}var Ep;function N1(){if(Ep)return Zu.exports;Ep=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(i){console.error(i)}}return a(),Zu.exports=w1(),Zu.exports}var M1=N1(),gl={},wp;function R1(){if(wp)return gl;wp=1,Object.defineProperty(gl,"__esModule",{value:!0}),gl.parse=f,gl.serialize=h;const a=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,i=/^[\u0021-\u003A\u003C-\u007E]*$/,s=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,r=/^[\u0020-\u003A\u003D-\u007E]*$/,c=Object.prototype.toString,d=(()=>{const S=function(){};return S.prototype=Object.create(null),S})();function f(S,E){const M=new d,V=S.length;if(V<2)return M;const _=(E==null?void 0:E.decode)||v;let C=0;do{const q=S.indexOf("=",C);if(q===-1)break;const L=S.indexOf(";",C),P=L===-1?V:L;if(q>P){C=S.lastIndexOf(";",q-1)+1;continue}const Y=p(S,C,q),$=m(S,q,Y),at=S.slice(Y,$);if(M[at]===void 0){let F=p(S,q+1,P),rt=m(S,P,F);const vt=_(S.slice(F,rt));M[at]=vt}C=P+1}while(C<V);return M}function p(S,E,M){do{const V=S.charCodeAt(E);if(V!==32&&V!==9)return E}while(++E<M);return M}function m(S,E,M){for(;E>M;){const V=S.charCodeAt(--E);if(V!==32&&V!==9)return E+1}return M}function h(S,E,M){const V=(M==null?void 0:M.encode)||encodeURIComponent;if(!a.test(S))throw new TypeError(`argument name is invalid: ${S}`);const _=V(E);if(!i.test(_))throw new TypeError(`argument val is invalid: ${E}`);let C=S+"="+_;if(!M)return C;if(M.maxAge!==void 0){if(!Number.isInteger(M.maxAge))throw new TypeError(`option maxAge is invalid: ${M.maxAge}`);C+="; Max-Age="+M.maxAge}if(M.domain){if(!s.test(M.domain))throw new TypeError(`option domain is invalid: ${M.domain}`);C+="; Domain="+M.domain}if(M.path){if(!r.test(M.path))throw new TypeError(`option path is invalid: ${M.path}`);C+="; Path="+M.path}if(M.expires){if(!b(M.expires)||!Number.isFinite(M.expires.valueOf()))throw new TypeError(`option expires is invalid: ${M.expires}`);C+="; Expires="+M.expires.toUTCString()}if(M.httpOnly&&(C+="; HttpOnly"),M.secure&&(C+="; Secure"),M.partitioned&&(C+="; Partitioned"),M.priority)switch(typeof M.priority=="string"?M.priority.toLowerCase():void 0){case"low":C+="; Priority=Low";break;case"medium":C+="; Priority=Medium";break;case"high":C+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${M.priority}`)}if(M.sameSite)switch(typeof M.sameSite=="string"?M.sameSite.toLowerCase():M.sameSite){case!0:case"strict":C+="; SameSite=Strict";break;case"lax":C+="; SameSite=Lax";break;case"none":C+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${M.sameSite}`)}return C}function v(S){if(S.indexOf("%")===-1)return S;try{return decodeURIComponent(S)}catch{return S}}function b(S){return c.call(S)==="[object Date]"}return gl}R1();var Np="popstate";function D1(a={}){function i(r,c){let{pathname:d,search:f,hash:p}=r.location;return fc("",{pathname:d,search:f,hash:p},c.state&&c.state.usr||null,c.state&&c.state.key||"default")}function s(r,c){return typeof c=="string"?c:wl(c)}return O1(i,s,null,a)}function Dt(a,i){if(a===!1||a===null||typeof a>"u")throw new Error(i)}function $e(a,i){if(!a){typeof console<"u"&&console.warn(i);try{throw new Error(i)}catch{}}}function C1(){return Math.random().toString(36).substring(2,10)}function Mp(a,i){return{usr:a.state,key:a.key,idx:i}}function fc(a,i,s=null,r){return{pathname:typeof a=="string"?a:a.pathname,search:"",hash:"",...typeof i=="string"?hi(i):i,state:s,key:i&&i.key||r||C1()}}function wl({pathname:a="/",search:i="",hash:s=""}){return i&&i!=="?"&&(a+=i.charAt(0)==="?"?i:"?"+i),s&&s!=="#"&&(a+=s.charAt(0)==="#"?s:"#"+s),a}function hi(a){let i={};if(a){let s=a.indexOf("#");s>=0&&(i.hash=a.substring(s),a=a.substring(0,s));let r=a.indexOf("?");r>=0&&(i.search=a.substring(r),a=a.substring(0,r)),a&&(i.pathname=a)}return i}function O1(a,i,s,r={}){let{window:c=document.defaultView,v5Compat:d=!1}=r,f=c.history,p="POP",m=null,h=v();h==null&&(h=0,f.replaceState({...f.state,idx:h},""));function v(){return(f.state||{idx:null}).idx}function b(){p="POP";let _=v(),C=_==null?null:_-h;h=_,m&&m({action:p,location:V.location,delta:C})}function S(_,C){p="PUSH";let q=fc(V.location,_,C);h=v()+1;let L=Mp(q,h),P=V.createHref(q);try{f.pushState(L,"",P)}catch(Y){if(Y instanceof DOMException&&Y.name==="DataCloneError")throw Y;c.location.assign(P)}d&&m&&m({action:p,location:V.location,delta:1})}function E(_,C){p="REPLACE";let q=fc(V.location,_,C);h=v();let L=Mp(q,h),P=V.createHref(q);f.replaceState(L,"",P),d&&m&&m({action:p,location:V.location,delta:0})}function M(_){return z1(_)}let V={get action(){return p},get location(){return a(c,f)},listen(_){if(m)throw new Error("A history only accepts one active listener");return c.addEventListener(Np,b),m=_,()=>{c.removeEventListener(Np,b),m=null}},createHref(_){return i(c,_)},createURL:M,encodeLocation(_){let C=M(_);return{pathname:C.pathname,search:C.search,hash:C.hash}},push:S,replace:E,go(_){return f.go(_)}};return V}function z1(a,i=!1){let s="http://localhost";typeof window<"u"&&(s=window.location.origin!=="null"?window.location.origin:window.location.href),Dt(s,"No window.location.(origin|href) available to create URL");let r=typeof a=="string"?a:wl(a);return r=r.replace(/ $/,"%20"),!i&&r.startsWith("//")&&(r=s+r),new URL(r,s)}function Z0(a,i,s="/"){return V1(a,i,s,!1)}function V1(a,i,s,r){let c=typeof i=="string"?hi(i):i,d=yn(c.pathname||"/",s);if(d==null)return null;let f=Q0(a);_1(f);let p=null;for(let m=0;p==null&&m<f.length;++m){let h=Z1(d);p=K1(f[m],h,r)}return p}function Q0(a,i=[],s=[],r=""){let c=(d,f,p)=>{let m={relativePath:p===void 0?d.path||"":p,caseSensitive:d.caseSensitive===!0,childrenIndex:f,route:d};m.relativePath.startsWith("/")&&(Dt(m.relativePath.startsWith(r),`Absolute route path "${m.relativePath}" nested under path "${r}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),m.relativePath=m.relativePath.slice(r.length));let h=pn([r,m.relativePath]),v=s.concat(m);d.children&&d.children.length>0&&(Dt(d.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${h}".`),Q0(d.children,i,v,h)),!(d.path==null&&!d.index)&&i.push({path:h,score:Y1(h,d.index),routesMeta:v})};return a.forEach((d,f)=>{var p;if(d.path===""||!((p=d.path)!=null&&p.includes("?")))c(d,f);else for(let m of P0(d.path))c(d,f,m)}),i}function P0(a){let i=a.split("/");if(i.length===0)return[];let[s,...r]=i,c=s.endsWith("?"),d=s.replace(/\?$/,"");if(r.length===0)return c?[d,""]:[d];let f=P0(r.join("/")),p=[];return p.push(...f.map(m=>m===""?d:[d,m].join("/"))),c&&p.push(...f),p.map(m=>a.startsWith("/")&&m===""?"/":m)}function _1(a){a.sort((i,s)=>i.score!==s.score?s.score-i.score:G1(i.routesMeta.map(r=>r.childrenIndex),s.routesMeta.map(r=>r.childrenIndex)))}var U1=/^:[\w-]+$/,k1=3,B1=2,L1=1,H1=10,q1=-2,Rp=a=>a==="*";function Y1(a,i){let s=a.split("/"),r=s.length;return s.some(Rp)&&(r+=q1),i&&(r+=B1),s.filter(c=>!Rp(c)).reduce((c,d)=>c+(U1.test(d)?k1:d===""?L1:H1),r)}function G1(a,i){return a.length===i.length&&a.slice(0,-1).every((r,c)=>r===i[c])?a[a.length-1]-i[i.length-1]:0}function K1(a,i,s=!1){let{routesMeta:r}=a,c={},d="/",f=[];for(let p=0;p<r.length;++p){let m=r[p],h=p===r.length-1,v=d==="/"?i:i.slice(d.length)||"/",b=hr({path:m.relativePath,caseSensitive:m.caseSensitive,end:h},v),S=m.route;if(!b&&h&&s&&!r[r.length-1].route.index&&(b=hr({path:m.relativePath,caseSensitive:m.caseSensitive,end:!1},v)),!b)return null;Object.assign(c,b.params),f.push({params:c,pathname:pn([d,b.pathname]),pathnameBase:$1(pn([d,b.pathnameBase])),route:S}),b.pathnameBase!=="/"&&(d=pn([d,b.pathnameBase]))}return f}function hr(a,i){typeof a=="string"&&(a={path:a,caseSensitive:!1,end:!0});let[s,r]=X1(a.path,a.caseSensitive,a.end),c=i.match(s);if(!c)return null;let d=c[0],f=d.replace(/(.)\/+$/,"$1"),p=c.slice(1);return{params:r.reduce((h,{paramName:v,isOptional:b},S)=>{if(v==="*"){let M=p[S]||"";f=d.slice(0,d.length-M.length).replace(/(.)\/+$/,"$1")}const E=p[S];return b&&!E?h[v]=void 0:h[v]=(E||"").replace(/%2F/g,"/"),h},{}),pathname:d,pathnameBase:f,pattern:a}}function X1(a,i=!1,s=!0){$e(a==="*"||!a.endsWith("*")||a.endsWith("/*"),`Route path "${a}" will be treated as if it were "${a.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${a.replace(/\*$/,"/*")}".`);let r=[],c="^"+a.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(f,p,m)=>(r.push({paramName:p,isOptional:m!=null}),m?"/?([^\\/]+)?":"/([^\\/]+)"));return a.endsWith("*")?(r.push({paramName:"*"}),c+=a==="*"||a==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):s?c+="\\/*$":a!==""&&a!=="/"&&(c+="(?:(?=\\/|$))"),[new RegExp(c,i?void 0:"i"),r]}function Z1(a){try{return a.split("/").map(i=>decodeURIComponent(i).replace(/\//g,"%2F")).join("/")}catch(i){return $e(!1,`The URL path "${a}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${i}).`),a}}function yn(a,i){if(i==="/")return a;if(!a.toLowerCase().startsWith(i.toLowerCase()))return null;let s=i.endsWith("/")?i.length-1:i.length,r=a.charAt(s);return r&&r!=="/"?null:a.slice(s)||"/"}function Q1(a,i="/"){let{pathname:s,search:r="",hash:c=""}=typeof a=="string"?hi(a):a;return{pathname:s?s.startsWith("/")?s:P1(s,i):i,search:F1(r),hash:W1(c)}}function P1(a,i){let s=i.replace(/\/+$/,"").split("/");return a.split("/").forEach(c=>{c===".."?s.length>1&&s.pop():c!=="."&&s.push(c)}),s.length>1?s.join("/"):"/"}function $u(a,i,s,r){return`Cannot include a '${a}' character in a manually specified \`to.${i}\` field [${JSON.stringify(r)}].  Please separate it out to the \`to.${s}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function J1(a){return a.filter((i,s)=>s===0||i.route.path&&i.route.path.length>0)}function J0(a){let i=J1(a);return i.map((s,r)=>r===i.length-1?s.pathname:s.pathnameBase)}function $0(a,i,s,r=!1){let c;typeof a=="string"?c=hi(a):(c={...a},Dt(!c.pathname||!c.pathname.includes("?"),$u("?","pathname","search",c)),Dt(!c.pathname||!c.pathname.includes("#"),$u("#","pathname","hash",c)),Dt(!c.search||!c.search.includes("#"),$u("#","search","hash",c)));let d=a===""||c.pathname==="",f=d?"/":c.pathname,p;if(f==null)p=s;else{let b=i.length-1;if(!r&&f.startsWith("..")){let S=f.split("/");for(;S[0]==="..";)S.shift(),b-=1;c.pathname=S.join("/")}p=b>=0?i[b]:"/"}let m=Q1(c,p),h=f&&f!=="/"&&f.endsWith("/"),v=(d||f===".")&&s.endsWith("/");return!m.pathname.endsWith("/")&&(h||v)&&(m.pathname+="/"),m}var pn=a=>a.join("/").replace(/\/\/+/g,"/"),$1=a=>a.replace(/\/+$/,"").replace(/^\/*/,"/"),F1=a=>!a||a==="?"?"":a.startsWith("?")?a:"?"+a,W1=a=>!a||a==="#"?"":a.startsWith("#")?a:"#"+a;function I1(a){return a!=null&&typeof a.status=="number"&&typeof a.statusText=="string"&&typeof a.internal=="boolean"&&"data"in a}var F0=["POST","PUT","PATCH","DELETE"];new Set(F0);var tb=["GET",...F0];new Set(tb);var mi=w.createContext(null);mi.displayName="DataRouter";var br=w.createContext(null);br.displayName="DataRouterState";var W0=w.createContext({isTransitioning:!1});W0.displayName="ViewTransition";var eb=w.createContext(new Map);eb.displayName="Fetchers";var nb=w.createContext(null);nb.displayName="Await";var Fe=w.createContext(null);Fe.displayName="Navigation";var Vl=w.createContext(null);Vl.displayName="Location";var xn=w.createContext({outlet:null,matches:[],isDataRoute:!1});xn.displayName="Route";var Vc=w.createContext(null);Vc.displayName="RouteError";function ab(a,{relative:i}={}){Dt(_l(),"useHref() may be used only in the context of a <Router> component.");let{basename:s,navigator:r}=w.useContext(Fe),{hash:c,pathname:d,search:f}=Ul(a,{relative:i}),p=d;return s!=="/"&&(p=d==="/"?s:pn([s,d])),r.createHref({pathname:p,search:f,hash:c})}function _l(){return w.useContext(Vl)!=null}function Pn(){return Dt(_l(),"useLocation() may be used only in the context of a <Router> component."),w.useContext(Vl).location}var I0="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function ty(a){w.useContext(Fe).static||w.useLayoutEffect(a)}function ib(){let{isDataRoute:a}=w.useContext(xn);return a?gb():lb()}function lb(){Dt(_l(),"useNavigate() may be used only in the context of a <Router> component.");let a=w.useContext(mi),{basename:i,navigator:s}=w.useContext(Fe),{matches:r}=w.useContext(xn),{pathname:c}=Pn(),d=JSON.stringify(J0(r)),f=w.useRef(!1);return ty(()=>{f.current=!0}),w.useCallback((m,h={})=>{if($e(f.current,I0),!f.current)return;if(typeof m=="number"){s.go(m);return}let v=$0(m,JSON.parse(d),c,h.relative==="path");a==null&&i!=="/"&&(v.pathname=v.pathname==="/"?i:pn([i,v.pathname])),(h.replace?s.replace:s.push)(v,h.state,h)},[i,s,d,c,a])}w.createContext(null);function Ul(a,{relative:i}={}){let{matches:s}=w.useContext(xn),{pathname:r}=Pn(),c=JSON.stringify(J0(s));return w.useMemo(()=>$0(a,JSON.parse(c),r,i==="path"),[a,c,r,i])}function sb(a,i){return ey(a,i)}function ey(a,i,s,r){var C;Dt(_l(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:c}=w.useContext(Fe),{matches:d}=w.useContext(xn),f=d[d.length-1],p=f?f.params:{},m=f?f.pathname:"/",h=f?f.pathnameBase:"/",v=f&&f.route;{let q=v&&v.path||"";ny(m,!v||q.endsWith("*")||q.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${m}" (under <Route path="${q}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${q}"> to <Route path="${q==="/"?"*":`${q}/*`}">.`)}let b=Pn(),S;if(i){let q=typeof i=="string"?hi(i):i;Dt(h==="/"||((C=q.pathname)==null?void 0:C.startsWith(h)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${h}" but pathname "${q.pathname}" was given in the \`location\` prop.`),S=q}else S=b;let E=S.pathname||"/",M=E;if(h!=="/"){let q=h.replace(/^\//,"").split("/");M="/"+E.replace(/^\//,"").split("/").slice(q.length).join("/")}let V=Z0(a,{pathname:M});$e(v||V!=null,`No routes matched location "${S.pathname}${S.search}${S.hash}" `),$e(V==null||V[V.length-1].route.element!==void 0||V[V.length-1].route.Component!==void 0||V[V.length-1].route.lazy!==void 0,`Matched leaf route at location "${S.pathname}${S.search}${S.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let _=fb(V&&V.map(q=>Object.assign({},q,{params:Object.assign({},p,q.params),pathname:pn([h,c.encodeLocation?c.encodeLocation(q.pathname).pathname:q.pathname]),pathnameBase:q.pathnameBase==="/"?h:pn([h,c.encodeLocation?c.encodeLocation(q.pathnameBase).pathname:q.pathnameBase])})),d,s,r);return i&&_?w.createElement(Vl.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...S},navigationType:"POP"}},_):_}function rb(){let a=yb(),i=I1(a)?`${a.status} ${a.statusText}`:a instanceof Error?a.message:JSON.stringify(a),s=a instanceof Error?a.stack:null,r="rgba(200,200,200, 0.5)",c={padding:"0.5rem",backgroundColor:r},d={padding:"2px 4px",backgroundColor:r},f=null;return console.error("Error handled by React Router default ErrorBoundary:",a),f=w.createElement(w.Fragment,null,w.createElement("p",null,"💿 Hey developer 👋"),w.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",w.createElement("code",{style:d},"ErrorBoundary")," or"," ",w.createElement("code",{style:d},"errorElement")," prop on your route.")),w.createElement(w.Fragment,null,w.createElement("h2",null,"Unexpected Application Error!"),w.createElement("h3",{style:{fontStyle:"italic"}},i),s?w.createElement("pre",{style:c},s):null,f)}var ob=w.createElement(rb,null),ub=class extends w.Component{constructor(a){super(a),this.state={location:a.location,revalidation:a.revalidation,error:a.error}}static getDerivedStateFromError(a){return{error:a}}static getDerivedStateFromProps(a,i){return i.location!==a.location||i.revalidation!=="idle"&&a.revalidation==="idle"?{error:a.error,location:a.location,revalidation:a.revalidation}:{error:a.error!==void 0?a.error:i.error,location:i.location,revalidation:a.revalidation||i.revalidation}}componentDidCatch(a,i){console.error("React Router caught the following error during render",a,i)}render(){return this.state.error!==void 0?w.createElement(xn.Provider,{value:this.props.routeContext},w.createElement(Vc.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function cb({routeContext:a,match:i,children:s}){let r=w.useContext(mi);return r&&r.static&&r.staticContext&&(i.route.errorElement||i.route.ErrorBoundary)&&(r.staticContext._deepestRenderedBoundaryId=i.route.id),w.createElement(xn.Provider,{value:a},s)}function fb(a,i=[],s=null,r=null){if(a==null){if(!s)return null;if(s.errors)a=s.matches;else if(i.length===0&&!s.initialized&&s.matches.length>0)a=s.matches;else return null}let c=a,d=s==null?void 0:s.errors;if(d!=null){let m=c.findIndex(h=>h.route.id&&(d==null?void 0:d[h.route.id])!==void 0);Dt(m>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(d).join(",")}`),c=c.slice(0,Math.min(c.length,m+1))}let f=!1,p=-1;if(s)for(let m=0;m<c.length;m++){let h=c[m];if((h.route.HydrateFallback||h.route.hydrateFallbackElement)&&(p=m),h.route.id){let{loaderData:v,errors:b}=s,S=h.route.loader&&!v.hasOwnProperty(h.route.id)&&(!b||b[h.route.id]===void 0);if(h.route.lazy||S){f=!0,p>=0?c=c.slice(0,p+1):c=[c[0]];break}}}return c.reduceRight((m,h,v)=>{let b,S=!1,E=null,M=null;s&&(b=d&&h.route.id?d[h.route.id]:void 0,E=h.route.errorElement||ob,f&&(p<0&&v===0?(ny("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),S=!0,M=null):p===v&&(S=!0,M=h.route.hydrateFallbackElement||null)));let V=i.concat(c.slice(0,v+1)),_=()=>{let C;return b?C=E:S?C=M:h.route.Component?C=w.createElement(h.route.Component,null):h.route.element?C=h.route.element:C=m,w.createElement(cb,{match:h,routeContext:{outlet:m,matches:V,isDataRoute:s!=null},children:C})};return s&&(h.route.ErrorBoundary||h.route.errorElement||v===0)?w.createElement(ub,{location:s.location,revalidation:s.revalidation,component:E,error:b,children:_(),routeContext:{outlet:null,matches:V,isDataRoute:!0}}):_()},null)}function _c(a){return`${a} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function db(a){let i=w.useContext(mi);return Dt(i,_c(a)),i}function hb(a){let i=w.useContext(br);return Dt(i,_c(a)),i}function mb(a){let i=w.useContext(xn);return Dt(i,_c(a)),i}function Uc(a){let i=mb(a),s=i.matches[i.matches.length-1];return Dt(s.route.id,`${a} can only be used on routes that contain a unique "id"`),s.route.id}function pb(){return Uc("useRouteId")}function yb(){var r;let a=w.useContext(Vc),i=hb("useRouteError"),s=Uc("useRouteError");return a!==void 0?a:(r=i.errors)==null?void 0:r[s]}function gb(){let{router:a}=db("useNavigate"),i=Uc("useNavigate"),s=w.useRef(!1);return ty(()=>{s.current=!0}),w.useCallback(async(c,d={})=>{$e(s.current,I0),s.current&&(typeof c=="number"?a.navigate(c):await a.navigate(c,{fromRouteId:i,...d}))},[a,i])}var Dp={};function ny(a,i,s){!i&&!Dp[a]&&(Dp[a]=!0,$e(!1,s))}w.memo(vb);function vb({routes:a,future:i,state:s}){return ey(a,void 0,s,i)}function pa(a){Dt(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function xb({basename:a="/",children:i=null,location:s,navigationType:r="POP",navigator:c,static:d=!1}){Dt(!_l(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let f=a.replace(/^\/*/,"/"),p=w.useMemo(()=>({basename:f,navigator:c,static:d,future:{}}),[f,c,d]);typeof s=="string"&&(s=hi(s));let{pathname:m="/",search:h="",hash:v="",state:b=null,key:S="default"}=s,E=w.useMemo(()=>{let M=yn(m,f);return M==null?null:{location:{pathname:M,search:h,hash:v,state:b,key:S},navigationType:r}},[f,m,h,v,b,S,r]);return $e(E!=null,`<Router basename="${f}"> is not able to match the URL "${m}${h}${v}" because it does not start with the basename, so the <Router> won't render anything.`),E==null?null:w.createElement(Fe.Provider,{value:p},w.createElement(Vl.Provider,{children:i,value:E}))}function bb({children:a,location:i}){return sb(dc(a),i)}function dc(a,i=[]){let s=[];return w.Children.forEach(a,(r,c)=>{if(!w.isValidElement(r))return;let d=[...i,c];if(r.type===w.Fragment){s.push.apply(s,dc(r.props.children,d));return}Dt(r.type===pa,`[${typeof r.type=="string"?r.type:r.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),Dt(!r.props.index||!r.props.children,"An index route cannot have child routes.");let f={id:r.props.id||d.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,hydrateFallbackElement:r.props.hydrateFallbackElement,HydrateFallback:r.props.HydrateFallback,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.hasErrorBoundary===!0||r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(f.children=dc(r.props.children,d)),s.push(f)}),s}var rr="get",or="application/x-www-form-urlencoded";function Sr(a){return a!=null&&typeof a.tagName=="string"}function Sb(a){return Sr(a)&&a.tagName.toLowerCase()==="button"}function Tb(a){return Sr(a)&&a.tagName.toLowerCase()==="form"}function Ab(a){return Sr(a)&&a.tagName.toLowerCase()==="input"}function jb(a){return!!(a.metaKey||a.altKey||a.ctrlKey||a.shiftKey)}function Eb(a,i){return a.button===0&&(!i||i==="_self")&&!jb(a)}var nr=null;function wb(){if(nr===null)try{new FormData(document.createElement("form"),0),nr=!1}catch{nr=!0}return nr}var Nb=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Fu(a){return a!=null&&!Nb.has(a)?($e(!1,`"${a}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${or}"`),null):a}function Mb(a,i){let s,r,c,d,f;if(Tb(a)){let p=a.getAttribute("action");r=p?yn(p,i):null,s=a.getAttribute("method")||rr,c=Fu(a.getAttribute("enctype"))||or,d=new FormData(a)}else if(Sb(a)||Ab(a)&&(a.type==="submit"||a.type==="image")){let p=a.form;if(p==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let m=a.getAttribute("formaction")||p.getAttribute("action");if(r=m?yn(m,i):null,s=a.getAttribute("formmethod")||p.getAttribute("method")||rr,c=Fu(a.getAttribute("formenctype"))||Fu(p.getAttribute("enctype"))||or,d=new FormData(p,a),!wb()){let{name:h,type:v,value:b}=a;if(v==="image"){let S=h?`${h}.`:"";d.append(`${S}x`,"0"),d.append(`${S}y`,"0")}else h&&d.append(h,b)}}else{if(Sr(a))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');s=rr,r=null,c=or,f=a}return d&&c==="text/plain"&&(f=d,d=void 0),{action:r,method:s.toLowerCase(),encType:c,formData:d,body:f}}function kc(a,i){if(a===!1||a===null||typeof a>"u")throw new Error(i)}async function Rb(a,i){if(a.id in i)return i[a.id];try{let s=await import(a.module);return i[a.id]=s,s}catch(s){return console.error(`Error loading route module \`${a.module}\`, reloading page...`),console.error(s),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function Db(a){return a==null?!1:a.href==null?a.rel==="preload"&&typeof a.imageSrcSet=="string"&&typeof a.imageSizes=="string":typeof a.rel=="string"&&typeof a.href=="string"}async function Cb(a,i,s){let r=await Promise.all(a.map(async c=>{let d=i.routes[c.route.id];if(d){let f=await Rb(d,s);return f.links?f.links():[]}return[]}));return _b(r.flat(1).filter(Db).filter(c=>c.rel==="stylesheet"||c.rel==="preload").map(c=>c.rel==="stylesheet"?{...c,rel:"prefetch",as:"style"}:{...c,rel:"prefetch"}))}function Cp(a,i,s,r,c,d){let f=(m,h)=>s[h]?m.route.id!==s[h].route.id:!0,p=(m,h)=>{var v;return s[h].pathname!==m.pathname||((v=s[h].route.path)==null?void 0:v.endsWith("*"))&&s[h].params["*"]!==m.params["*"]};return d==="assets"?i.filter((m,h)=>f(m,h)||p(m,h)):d==="data"?i.filter((m,h)=>{var b;let v=r.routes[m.route.id];if(!v||!v.hasLoader)return!1;if(f(m,h)||p(m,h))return!0;if(m.route.shouldRevalidate){let S=m.route.shouldRevalidate({currentUrl:new URL(c.pathname+c.search+c.hash,window.origin),currentParams:((b=s[0])==null?void 0:b.params)||{},nextUrl:new URL(a,window.origin),nextParams:m.params,defaultShouldRevalidate:!0});if(typeof S=="boolean")return S}return!0}):[]}function Ob(a,i,{includeHydrateFallback:s}={}){return zb(a.map(r=>{let c=i.routes[r.route.id];if(!c)return[];let d=[c.module];return c.clientActionModule&&(d=d.concat(c.clientActionModule)),c.clientLoaderModule&&(d=d.concat(c.clientLoaderModule)),s&&c.hydrateFallbackModule&&(d=d.concat(c.hydrateFallbackModule)),c.imports&&(d=d.concat(c.imports)),d}).flat(1))}function zb(a){return[...new Set(a)]}function Vb(a){let i={},s=Object.keys(a).sort();for(let r of s)i[r]=a[r];return i}function _b(a,i){let s=new Set;return new Set(i),a.reduce((r,c)=>{let d=JSON.stringify(Vb(c));return s.has(d)||(s.add(d),r.push({key:d,link:c})),r},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var Ub=new Set([100,101,204,205]);function kb(a,i){let s=typeof a=="string"?new URL(a,typeof window>"u"?"server://singlefetch/":window.location.origin):a;return s.pathname==="/"?s.pathname="_root.data":i&&yn(s.pathname,i)==="/"?s.pathname=`${i.replace(/\/$/,"")}/_root.data`:s.pathname=`${s.pathname.replace(/\/$/,"")}.data`,s}function ay(){let a=w.useContext(mi);return kc(a,"You must render this element inside a <DataRouterContext.Provider> element"),a}function Bb(){let a=w.useContext(br);return kc(a,"You must render this element inside a <DataRouterStateContext.Provider> element"),a}var Bc=w.createContext(void 0);Bc.displayName="FrameworkContext";function iy(){let a=w.useContext(Bc);return kc(a,"You must render this element inside a <HydratedRouter> element"),a}function Lb(a,i){let s=w.useContext(Bc),[r,c]=w.useState(!1),[d,f]=w.useState(!1),{onFocus:p,onBlur:m,onMouseEnter:h,onMouseLeave:v,onTouchStart:b}=i,S=w.useRef(null);w.useEffect(()=>{if(a==="render"&&f(!0),a==="viewport"){let V=C=>{C.forEach(q=>{f(q.isIntersecting)})},_=new IntersectionObserver(V,{threshold:.5});return S.current&&_.observe(S.current),()=>{_.disconnect()}}},[a]),w.useEffect(()=>{if(r){let V=setTimeout(()=>{f(!0)},100);return()=>{clearTimeout(V)}}},[r]);let E=()=>{c(!0)},M=()=>{c(!1),f(!1)};return s?a!=="intent"?[d,S,{}]:[d,S,{onFocus:vl(p,E),onBlur:vl(m,M),onMouseEnter:vl(h,E),onMouseLeave:vl(v,M),onTouchStart:vl(b,E)}]:[!1,S,{}]}function vl(a,i){return s=>{a&&a(s),s.defaultPrevented||i(s)}}function Hb({page:a,...i}){let{router:s}=ay(),r=w.useMemo(()=>Z0(s.routes,a,s.basename),[s.routes,a,s.basename]);return r?w.createElement(Yb,{page:a,matches:r,...i}):null}function qb(a){let{manifest:i,routeModules:s}=iy(),[r,c]=w.useState([]);return w.useEffect(()=>{let d=!1;return Cb(a,i,s).then(f=>{d||c(f)}),()=>{d=!0}},[a,i,s]),r}function Yb({page:a,matches:i,...s}){let r=Pn(),{manifest:c,routeModules:d}=iy(),{basename:f}=ay(),{loaderData:p,matches:m}=Bb(),h=w.useMemo(()=>Cp(a,i,m,c,r,"data"),[a,i,m,c,r]),v=w.useMemo(()=>Cp(a,i,m,c,r,"assets"),[a,i,m,c,r]),b=w.useMemo(()=>{if(a===r.pathname+r.search+r.hash)return[];let M=new Set,V=!1;if(i.forEach(C=>{var L;let q=c.routes[C.route.id];!q||!q.hasLoader||(!h.some(P=>P.route.id===C.route.id)&&C.route.id in p&&((L=d[C.route.id])!=null&&L.shouldRevalidate)||q.hasClientLoader?V=!0:M.add(C.route.id))}),M.size===0)return[];let _=kb(a,f);return V&&M.size>0&&_.searchParams.set("_routes",i.filter(C=>M.has(C.route.id)).map(C=>C.route.id).join(",")),[_.pathname+_.search]},[f,p,r,c,h,i,a,d]),S=w.useMemo(()=>Ob(v,c),[v,c]),E=qb(v);return w.createElement(w.Fragment,null,b.map(M=>w.createElement("link",{key:M,rel:"prefetch",as:"fetch",href:M,...s})),S.map(M=>w.createElement("link",{key:M,rel:"modulepreload",href:M,...s})),E.map(({key:M,link:V})=>w.createElement("link",{key:M,...V})))}function Gb(...a){return i=>{a.forEach(s=>{typeof s=="function"?s(i):s!=null&&(s.current=i)})}}var ly=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{ly&&(window.__reactRouterVersion="7.6.2")}catch{}function Kb({basename:a,children:i,window:s}){let r=w.useRef();r.current==null&&(r.current=D1({window:s,v5Compat:!0}));let c=r.current,[d,f]=w.useState({action:c.action,location:c.location}),p=w.useCallback(m=>{w.startTransition(()=>f(m))},[f]);return w.useLayoutEffect(()=>c.listen(p),[c,p]),w.createElement(xb,{basename:a,children:i,location:d.location,navigationType:d.action,navigator:c})}var sy=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,se=w.forwardRef(function({onClick:i,discover:s="render",prefetch:r="none",relative:c,reloadDocument:d,replace:f,state:p,target:m,to:h,preventScrollReset:v,viewTransition:b,...S},E){let{basename:M}=w.useContext(Fe),V=typeof h=="string"&&sy.test(h),_,C=!1;if(typeof h=="string"&&V&&(_=h,ly))try{let rt=new URL(window.location.href),vt=h.startsWith("//")?new URL(rt.protocol+h):new URL(h),Yt=yn(vt.pathname,M);vt.origin===rt.origin&&Yt!=null?h=Yt+vt.search+vt.hash:C=!0}catch{$e(!1,`<Link to="${h}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let q=ab(h,{relative:c}),[L,P,Y]=Lb(r,S),$=Pb(h,{replace:f,state:p,target:m,preventScrollReset:v,relative:c,viewTransition:b});function at(rt){i&&i(rt),rt.defaultPrevented||$(rt)}let F=w.createElement("a",{...S,...Y,href:_||q,onClick:C||d?i:at,ref:Gb(E,P),target:m,"data-discover":!V&&s==="render"?"true":void 0});return L&&!V?w.createElement(w.Fragment,null,F,w.createElement(Hb,{page:q})):F});se.displayName="Link";var Xb=w.forwardRef(function({"aria-current":i="page",caseSensitive:s=!1,className:r="",end:c=!1,style:d,to:f,viewTransition:p,children:m,...h},v){let b=Ul(f,{relative:h.relative}),S=Pn(),E=w.useContext(br),{navigator:M,basename:V}=w.useContext(Fe),_=E!=null&&Ib(b)&&p===!0,C=M.encodeLocation?M.encodeLocation(b).pathname:b.pathname,q=S.pathname,L=E&&E.navigation&&E.navigation.location?E.navigation.location.pathname:null;s||(q=q.toLowerCase(),L=L?L.toLowerCase():null,C=C.toLowerCase()),L&&V&&(L=yn(L,V)||L);const P=C!=="/"&&C.endsWith("/")?C.length-1:C.length;let Y=q===C||!c&&q.startsWith(C)&&q.charAt(P)==="/",$=L!=null&&(L===C||!c&&L.startsWith(C)&&L.charAt(C.length)==="/"),at={isActive:Y,isPending:$,isTransitioning:_},F=Y?i:void 0,rt;typeof r=="function"?rt=r(at):rt=[r,Y?"active":null,$?"pending":null,_?"transitioning":null].filter(Boolean).join(" ");let vt=typeof d=="function"?d(at):d;return w.createElement(se,{...h,"aria-current":F,className:rt,ref:v,style:vt,to:f,viewTransition:p},typeof m=="function"?m(at):m)});Xb.displayName="NavLink";var Zb=w.forwardRef(({discover:a="render",fetcherKey:i,navigate:s,reloadDocument:r,replace:c,state:d,method:f=rr,action:p,onSubmit:m,relative:h,preventScrollReset:v,viewTransition:b,...S},E)=>{let M=Fb(),V=Wb(p,{relative:h}),_=f.toLowerCase()==="get"?"get":"post",C=typeof p=="string"&&sy.test(p),q=L=>{if(m&&m(L),L.defaultPrevented)return;L.preventDefault();let P=L.nativeEvent.submitter,Y=(P==null?void 0:P.getAttribute("formmethod"))||f;M(P||L.currentTarget,{fetcherKey:i,method:Y,navigate:s,replace:c,state:d,relative:h,preventScrollReset:v,viewTransition:b})};return w.createElement("form",{ref:E,method:_,action:V,onSubmit:r?m:q,...S,"data-discover":!C&&a==="render"?"true":void 0})});Zb.displayName="Form";function Qb(a){return`${a} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function ry(a){let i=w.useContext(mi);return Dt(i,Qb(a)),i}function Pb(a,{target:i,replace:s,state:r,preventScrollReset:c,relative:d,viewTransition:f}={}){let p=ib(),m=Pn(),h=Ul(a,{relative:d});return w.useCallback(v=>{if(Eb(v,i)){v.preventDefault();let b=s!==void 0?s:wl(m)===wl(h);p(a,{replace:b,state:r,preventScrollReset:c,relative:d,viewTransition:f})}},[m,p,h,s,r,i,a,c,d,f])}var Jb=0,$b=()=>`__${String(++Jb)}__`;function Fb(){let{router:a}=ry("useSubmit"),{basename:i}=w.useContext(Fe),s=pb();return w.useCallback(async(r,c={})=>{let{action:d,method:f,encType:p,formData:m,body:h}=Mb(r,i);if(c.navigate===!1){let v=c.fetcherKey||$b();await a.fetch(v,s,c.action||d,{preventScrollReset:c.preventScrollReset,formData:m,body:h,formMethod:c.method||f,formEncType:c.encType||p,flushSync:c.flushSync})}else await a.navigate(c.action||d,{preventScrollReset:c.preventScrollReset,formData:m,body:h,formMethod:c.method||f,formEncType:c.encType||p,replace:c.replace,state:c.state,fromRouteId:s,flushSync:c.flushSync,viewTransition:c.viewTransition})},[a,i,s])}function Wb(a,{relative:i}={}){let{basename:s}=w.useContext(Fe),r=w.useContext(xn);Dt(r,"useFormAction must be used inside a RouteContext");let[c]=r.matches.slice(-1),d={...Ul(a||".",{relative:i})},f=Pn();if(a==null){d.search=f.search;let p=new URLSearchParams(d.search),m=p.getAll("index");if(m.some(v=>v==="")){p.delete("index"),m.filter(b=>b).forEach(b=>p.append("index",b));let v=p.toString();d.search=v?`?${v}`:""}}return(!a||a===".")&&c.route.index&&(d.search=d.search?d.search.replace(/^\?/,"?index&"):"?index"),s!=="/"&&(d.pathname=d.pathname==="/"?s:pn([s,d.pathname])),wl(d)}function Ib(a,i={}){let s=w.useContext(W0);Dt(s!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:r}=ry("useViewTransitionState"),c=Ul(a,{relative:i.relative});if(!s.isTransitioning)return!1;let d=yn(s.currentLocation.pathname,r)||s.currentLocation.pathname,f=yn(s.nextLocation.pathname,r)||s.nextLocation.pathname;return hr(c.pathname,f)!=null||hr(c.pathname,d)!=null}[...Ub];const Lc=w.createContext({});function Hc(a){const i=w.useRef(null);return i.current===null&&(i.current=a()),i.current}const qc=typeof window<"u",oy=qc?w.useLayoutEffect:w.useEffect,Tr=w.createContext(null);function Yc(a,i){a.indexOf(i)===-1&&a.push(i)}function Gc(a,i){const s=a.indexOf(i);s>-1&&a.splice(s,1)}const gn=(a,i,s)=>s>i?i:s<a?a:s;let Kc=()=>{};const vn={},uy=a=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(a);function cy(a){return typeof a=="object"&&a!==null}const fy=a=>/^0[^.\s]+$/u.test(a);function Xc(a){let i;return()=>(i===void 0&&(i=a()),i)}const _e=a=>a,t2=(a,i)=>s=>i(a(s)),kl=(...a)=>a.reduce(t2),Nl=(a,i,s)=>{const r=i-a;return r===0?1:(s-a)/r};class Zc{constructor(){this.subscriptions=[]}add(i){return Yc(this.subscriptions,i),()=>Gc(this.subscriptions,i)}notify(i,s,r){const c=this.subscriptions.length;if(c)if(c===1)this.subscriptions[0](i,s,r);else for(let d=0;d<c;d++){const f=this.subscriptions[d];f&&f(i,s,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Qe=a=>a*1e3,Pe=a=>a/1e3;function dy(a,i){return i?a*(1e3/i):0}const hy=(a,i,s)=>(((1-3*s+3*i)*a+(3*s-6*i))*a+3*i)*a,e2=1e-7,n2=12;function a2(a,i,s,r,c){let d,f,p=0;do f=i+(s-i)/2,d=hy(f,r,c)-a,d>0?s=f:i=f;while(Math.abs(d)>e2&&++p<n2);return f}function Bl(a,i,s,r){if(a===i&&s===r)return _e;const c=d=>a2(d,0,1,a,s);return d=>d===0||d===1?d:hy(c(d),i,r)}const my=a=>i=>i<=.5?a(2*i)/2:(2-a(2*(1-i)))/2,py=a=>i=>1-a(1-i),yy=Bl(.33,1.53,.69,.99),Qc=py(yy),gy=my(Qc),vy=a=>(a*=2)<1?.5*Qc(a):.5*(2-Math.pow(2,-10*(a-1))),Pc=a=>1-Math.sin(Math.acos(a)),xy=py(Pc),by=my(Pc),i2=Bl(.42,0,1,1),l2=Bl(0,0,.58,1),Sy=Bl(.42,0,.58,1),s2=a=>Array.isArray(a)&&typeof a[0]!="number",Ty=a=>Array.isArray(a)&&typeof a[0]=="number",r2={linear:_e,easeIn:i2,easeInOut:Sy,easeOut:l2,circIn:Pc,circInOut:by,circOut:xy,backIn:Qc,backInOut:gy,backOut:yy,anticipate:vy},o2=a=>typeof a=="string",Op=a=>{if(Ty(a)){Kc(a.length===4);const[i,s,r,c]=a;return Bl(i,s,r,c)}else if(o2(a))return r2[a];return a},ar=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],zp={value:null};function u2(a,i){let s=new Set,r=new Set,c=!1,d=!1;const f=new WeakSet;let p={delta:0,timestamp:0,isProcessing:!1},m=0;function h(b){f.has(b)&&(v.schedule(b),a()),m++,b(p)}const v={schedule:(b,S=!1,E=!1)=>{const V=E&&c?s:r;return S&&f.add(b),V.has(b)||V.add(b),b},cancel:b=>{r.delete(b),f.delete(b)},process:b=>{if(p=b,c){d=!0;return}c=!0,[s,r]=[r,s],s.forEach(h),i&&zp.value&&zp.value.frameloop[i].push(m),m=0,s.clear(),c=!1,d&&(d=!1,v.process(b))}};return v}const c2=40;function Ay(a,i){let s=!1,r=!0;const c={delta:0,timestamp:0,isProcessing:!1},d=()=>s=!0,f=ar.reduce((L,P)=>(L[P]=u2(d,i?P:void 0),L),{}),{setup:p,read:m,resolveKeyframes:h,preUpdate:v,update:b,preRender:S,render:E,postRender:M}=f,V=()=>{const L=vn.useManualTiming?c.timestamp:performance.now();s=!1,vn.useManualTiming||(c.delta=r?1e3/60:Math.max(Math.min(L-c.timestamp,c2),1)),c.timestamp=L,c.isProcessing=!0,p.process(c),m.process(c),h.process(c),v.process(c),b.process(c),S.process(c),E.process(c),M.process(c),c.isProcessing=!1,s&&i&&(r=!1,a(V))},_=()=>{s=!0,r=!0,c.isProcessing||a(V)};return{schedule:ar.reduce((L,P)=>{const Y=f[P];return L[P]=($,at=!1,F=!1)=>(s||_(),Y.schedule($,at,F)),L},{}),cancel:L=>{for(let P=0;P<ar.length;P++)f[ar[P]].cancel(L)},state:c,steps:f}}const{schedule:Ct,cancel:Zn,state:te,steps:Wu}=Ay(typeof requestAnimationFrame<"u"?requestAnimationFrame:_e,!0);let ur;function f2(){ur=void 0}const he={now:()=>(ur===void 0&&he.set(te.isProcessing||vn.useManualTiming?te.timestamp:performance.now()),ur),set:a=>{ur=a,queueMicrotask(f2)}},jy=a=>i=>typeof i=="string"&&i.startsWith(a),Jc=jy("--"),d2=jy("var(--"),$c=a=>d2(a)?h2.test(a.split("/*")[0].trim()):!1,h2=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,pi={test:a=>typeof a=="number",parse:parseFloat,transform:a=>a},Ml={...pi,transform:a=>gn(0,1,a)},ir={...pi,default:1},Sl=a=>Math.round(a*1e5)/1e5,Fc=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function m2(a){return a==null}const p2=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Wc=(a,i)=>s=>!!(typeof s=="string"&&p2.test(s)&&s.startsWith(a)||i&&!m2(s)&&Object.prototype.hasOwnProperty.call(s,i)),Ey=(a,i,s)=>r=>{if(typeof r!="string")return r;const[c,d,f,p]=r.match(Fc);return{[a]:parseFloat(c),[i]:parseFloat(d),[s]:parseFloat(f),alpha:p!==void 0?parseFloat(p):1}},y2=a=>gn(0,255,a),Iu={...pi,transform:a=>Math.round(y2(a))},ga={test:Wc("rgb","red"),parse:Ey("red","green","blue"),transform:({red:a,green:i,blue:s,alpha:r=1})=>"rgba("+Iu.transform(a)+", "+Iu.transform(i)+", "+Iu.transform(s)+", "+Sl(Ml.transform(r))+")"};function g2(a){let i="",s="",r="",c="";return a.length>5?(i=a.substring(1,3),s=a.substring(3,5),r=a.substring(5,7),c=a.substring(7,9)):(i=a.substring(1,2),s=a.substring(2,3),r=a.substring(3,4),c=a.substring(4,5),i+=i,s+=s,r+=r,c+=c),{red:parseInt(i,16),green:parseInt(s,16),blue:parseInt(r,16),alpha:c?parseInt(c,16)/255:1}}const hc={test:Wc("#"),parse:g2,transform:ga.transform},Ll=a=>({test:i=>typeof i=="string"&&i.endsWith(a)&&i.split(" ").length===1,parse:parseFloat,transform:i=>`${i}${a}`}),Xn=Ll("deg"),Je=Ll("%"),lt=Ll("px"),v2=Ll("vh"),x2=Ll("vw"),Vp={...Je,parse:a=>Je.parse(a)/100,transform:a=>Je.transform(a*100)},si={test:Wc("hsl","hue"),parse:Ey("hue","saturation","lightness"),transform:({hue:a,saturation:i,lightness:s,alpha:r=1})=>"hsla("+Math.round(a)+", "+Je.transform(Sl(i))+", "+Je.transform(Sl(s))+", "+Sl(Ml.transform(r))+")"},qt={test:a=>ga.test(a)||hc.test(a)||si.test(a),parse:a=>ga.test(a)?ga.parse(a):si.test(a)?si.parse(a):hc.parse(a),transform:a=>typeof a=="string"?a:a.hasOwnProperty("red")?ga.transform(a):si.transform(a),getAnimatableNone:a=>{const i=qt.parse(a);return i.alpha=0,qt.transform(i)}},b2=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function S2(a){var i,s;return isNaN(a)&&typeof a=="string"&&(((i=a.match(Fc))==null?void 0:i.length)||0)+(((s=a.match(b2))==null?void 0:s.length)||0)>0}const wy="number",Ny="color",T2="var",A2="var(",_p="${}",j2=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Rl(a){const i=a.toString(),s=[],r={color:[],number:[],var:[]},c=[];let d=0;const p=i.replace(j2,m=>(qt.test(m)?(r.color.push(d),c.push(Ny),s.push(qt.parse(m))):m.startsWith(A2)?(r.var.push(d),c.push(T2),s.push(m)):(r.number.push(d),c.push(wy),s.push(parseFloat(m))),++d,_p)).split(_p);return{values:s,split:p,indexes:r,types:c}}function My(a){return Rl(a).values}function Ry(a){const{split:i,types:s}=Rl(a),r=i.length;return c=>{let d="";for(let f=0;f<r;f++)if(d+=i[f],c[f]!==void 0){const p=s[f];p===wy?d+=Sl(c[f]):p===Ny?d+=qt.transform(c[f]):d+=c[f]}return d}}const E2=a=>typeof a=="number"?0:qt.test(a)?qt.getAnimatableNone(a):a;function w2(a){const i=My(a);return Ry(a)(i.map(E2))}const Qn={test:S2,parse:My,createTransformer:Ry,getAnimatableNone:w2};function tc(a,i,s){return s<0&&(s+=1),s>1&&(s-=1),s<1/6?a+(i-a)*6*s:s<1/2?i:s<2/3?a+(i-a)*(2/3-s)*6:a}function N2({hue:a,saturation:i,lightness:s,alpha:r}){a/=360,i/=100,s/=100;let c=0,d=0,f=0;if(!i)c=d=f=s;else{const p=s<.5?s*(1+i):s+i-s*i,m=2*s-p;c=tc(m,p,a+1/3),d=tc(m,p,a),f=tc(m,p,a-1/3)}return{red:Math.round(c*255),green:Math.round(d*255),blue:Math.round(f*255),alpha:r}}function mr(a,i){return s=>s>0?i:a}const Rt=(a,i,s)=>a+(i-a)*s,ec=(a,i,s)=>{const r=a*a,c=s*(i*i-r)+r;return c<0?0:Math.sqrt(c)},M2=[hc,ga,si],R2=a=>M2.find(i=>i.test(a));function Up(a){const i=R2(a);if(!i)return!1;let s=i.parse(a);return i===si&&(s=N2(s)),s}const kp=(a,i)=>{const s=Up(a),r=Up(i);if(!s||!r)return mr(a,i);const c={...s};return d=>(c.red=ec(s.red,r.red,d),c.green=ec(s.green,r.green,d),c.blue=ec(s.blue,r.blue,d),c.alpha=Rt(s.alpha,r.alpha,d),ga.transform(c))},mc=new Set(["none","hidden"]);function D2(a,i){return mc.has(a)?s=>s<=0?a:i:s=>s>=1?i:a}function C2(a,i){return s=>Rt(a,i,s)}function Ic(a){return typeof a=="number"?C2:typeof a=="string"?$c(a)?mr:qt.test(a)?kp:V2:Array.isArray(a)?Dy:typeof a=="object"?qt.test(a)?kp:O2:mr}function Dy(a,i){const s=[...a],r=s.length,c=a.map((d,f)=>Ic(d)(d,i[f]));return d=>{for(let f=0;f<r;f++)s[f]=c[f](d);return s}}function O2(a,i){const s={...a,...i},r={};for(const c in s)a[c]!==void 0&&i[c]!==void 0&&(r[c]=Ic(a[c])(a[c],i[c]));return c=>{for(const d in r)s[d]=r[d](c);return s}}function z2(a,i){const s=[],r={color:0,var:0,number:0};for(let c=0;c<i.values.length;c++){const d=i.types[c],f=a.indexes[d][r[d]],p=a.values[f]??0;s[c]=p,r[d]++}return s}const V2=(a,i)=>{const s=Qn.createTransformer(i),r=Rl(a),c=Rl(i);return r.indexes.var.length===c.indexes.var.length&&r.indexes.color.length===c.indexes.color.length&&r.indexes.number.length>=c.indexes.number.length?mc.has(a)&&!c.values.length||mc.has(i)&&!r.values.length?D2(a,i):kl(Dy(z2(r,c),c.values),s):mr(a,i)};function Cy(a,i,s){return typeof a=="number"&&typeof i=="number"&&typeof s=="number"?Rt(a,i,s):Ic(a)(a,i)}const _2=a=>{const i=({timestamp:s})=>a(s);return{start:(s=!0)=>Ct.update(i,s),stop:()=>Zn(i),now:()=>te.isProcessing?te.timestamp:he.now()}},Oy=(a,i,s=10)=>{let r="";const c=Math.max(Math.round(i/s),2);for(let d=0;d<c;d++)r+=Math.round(a(d/(c-1))*1e4)/1e4+", ";return`linear(${r.substring(0,r.length-2)})`},pr=2e4;function tf(a){let i=0;const s=50;let r=a.next(i);for(;!r.done&&i<pr;)i+=s,r=a.next(i);return i>=pr?1/0:i}function U2(a,i=100,s){const r=s({...a,keyframes:[0,i]}),c=Math.min(tf(r),pr);return{type:"keyframes",ease:d=>r.next(c*d).value/i,duration:Pe(c)}}const k2=5;function zy(a,i,s){const r=Math.max(i-k2,0);return dy(s-a(r),i-r)}const _t={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},nc=.001;function B2({duration:a=_t.duration,bounce:i=_t.bounce,velocity:s=_t.velocity,mass:r=_t.mass}){let c,d,f=1-i;f=gn(_t.minDamping,_t.maxDamping,f),a=gn(_t.minDuration,_t.maxDuration,Pe(a)),f<1?(c=h=>{const v=h*f,b=v*a,S=v-s,E=pc(h,f),M=Math.exp(-b);return nc-S/E*M},d=h=>{const b=h*f*a,S=b*s+s,E=Math.pow(f,2)*Math.pow(h,2)*a,M=Math.exp(-b),V=pc(Math.pow(h,2),f);return(-c(h)+nc>0?-1:1)*((S-E)*M)/V}):(c=h=>{const v=Math.exp(-h*a),b=(h-s)*a+1;return-nc+v*b},d=h=>{const v=Math.exp(-h*a),b=(s-h)*(a*a);return v*b});const p=5/a,m=H2(c,d,p);if(a=Qe(a),isNaN(m))return{stiffness:_t.stiffness,damping:_t.damping,duration:a};{const h=Math.pow(m,2)*r;return{stiffness:h,damping:f*2*Math.sqrt(r*h),duration:a}}}const L2=12;function H2(a,i,s){let r=s;for(let c=1;c<L2;c++)r=r-a(r)/i(r);return r}function pc(a,i){return a*Math.sqrt(1-i*i)}const q2=["duration","bounce"],Y2=["stiffness","damping","mass"];function Bp(a,i){return i.some(s=>a[s]!==void 0)}function G2(a){let i={velocity:_t.velocity,stiffness:_t.stiffness,damping:_t.damping,mass:_t.mass,isResolvedFromDuration:!1,...a};if(!Bp(a,Y2)&&Bp(a,q2))if(a.visualDuration){const s=a.visualDuration,r=2*Math.PI/(s*1.2),c=r*r,d=2*gn(.05,1,1-(a.bounce||0))*Math.sqrt(c);i={...i,mass:_t.mass,stiffness:c,damping:d}}else{const s=B2(a);i={...i,...s,mass:_t.mass},i.isResolvedFromDuration=!0}return i}function yr(a=_t.visualDuration,i=_t.bounce){const s=typeof a!="object"?{visualDuration:a,keyframes:[0,1],bounce:i}:a;let{restSpeed:r,restDelta:c}=s;const d=s.keyframes[0],f=s.keyframes[s.keyframes.length-1],p={done:!1,value:d},{stiffness:m,damping:h,mass:v,duration:b,velocity:S,isResolvedFromDuration:E}=G2({...s,velocity:-Pe(s.velocity||0)}),M=S||0,V=h/(2*Math.sqrt(m*v)),_=f-d,C=Pe(Math.sqrt(m/v)),q=Math.abs(_)<5;r||(r=q?_t.restSpeed.granular:_t.restSpeed.default),c||(c=q?_t.restDelta.granular:_t.restDelta.default);let L;if(V<1){const Y=pc(C,V);L=$=>{const at=Math.exp(-V*C*$);return f-at*((M+V*C*_)/Y*Math.sin(Y*$)+_*Math.cos(Y*$))}}else if(V===1)L=Y=>f-Math.exp(-C*Y)*(_+(M+C*_)*Y);else{const Y=C*Math.sqrt(V*V-1);L=$=>{const at=Math.exp(-V*C*$),F=Math.min(Y*$,300);return f-at*((M+V*C*_)*Math.sinh(F)+Y*_*Math.cosh(F))/Y}}const P={calculatedDuration:E&&b||null,next:Y=>{const $=L(Y);if(E)p.done=Y>=b;else{let at=Y===0?M:0;V<1&&(at=Y===0?Qe(M):zy(L,Y,$));const F=Math.abs(at)<=r,rt=Math.abs(f-$)<=c;p.done=F&&rt}return p.value=p.done?f:$,p},toString:()=>{const Y=Math.min(tf(P),pr),$=Oy(at=>P.next(Y*at).value,Y,30);return Y+"ms "+$},toTransition:()=>{}};return P}yr.applyToOptions=a=>{const i=U2(a,100,yr);return a.ease=i.ease,a.duration=Qe(i.duration),a.type="keyframes",a};function yc({keyframes:a,velocity:i=0,power:s=.8,timeConstant:r=325,bounceDamping:c=10,bounceStiffness:d=500,modifyTarget:f,min:p,max:m,restDelta:h=.5,restSpeed:v}){const b=a[0],S={done:!1,value:b},E=F=>p!==void 0&&F<p||m!==void 0&&F>m,M=F=>p===void 0?m:m===void 0||Math.abs(p-F)<Math.abs(m-F)?p:m;let V=s*i;const _=b+V,C=f===void 0?_:f(_);C!==_&&(V=C-b);const q=F=>-V*Math.exp(-F/r),L=F=>C+q(F),P=F=>{const rt=q(F),vt=L(F);S.done=Math.abs(rt)<=h,S.value=S.done?C:vt};let Y,$;const at=F=>{E(S.value)&&(Y=F,$=yr({keyframes:[S.value,M(S.value)],velocity:zy(L,F,S.value),damping:c,stiffness:d,restDelta:h,restSpeed:v}))};return at(0),{calculatedDuration:null,next:F=>{let rt=!1;return!$&&Y===void 0&&(rt=!0,P(F),at(F)),Y!==void 0&&F>=Y?$.next(F-Y):(!rt&&P(F),S)}}}function K2(a,i,s){const r=[],c=s||vn.mix||Cy,d=a.length-1;for(let f=0;f<d;f++){let p=c(a[f],a[f+1]);if(i){const m=Array.isArray(i)?i[f]||_e:i;p=kl(m,p)}r.push(p)}return r}function X2(a,i,{clamp:s=!0,ease:r,mixer:c}={}){const d=a.length;if(Kc(d===i.length),d===1)return()=>i[0];if(d===2&&i[0]===i[1])return()=>i[1];const f=a[0]===a[1];a[0]>a[d-1]&&(a=[...a].reverse(),i=[...i].reverse());const p=K2(i,r,c),m=p.length,h=v=>{if(f&&v<a[0])return i[0];let b=0;if(m>1)for(;b<a.length-2&&!(v<a[b+1]);b++);const S=Nl(a[b],a[b+1],v);return p[b](S)};return s?v=>h(gn(a[0],a[d-1],v)):h}function Z2(a,i){const s=a[a.length-1];for(let r=1;r<=i;r++){const c=Nl(0,i,r);a.push(Rt(s,1,c))}}function Q2(a){const i=[0];return Z2(i,a.length-1),i}function P2(a,i){return a.map(s=>s*i)}function J2(a,i){return a.map(()=>i||Sy).splice(0,a.length-1)}function Tl({duration:a=300,keyframes:i,times:s,ease:r="easeInOut"}){const c=s2(r)?r.map(Op):Op(r),d={done:!1,value:i[0]},f=P2(s&&s.length===i.length?s:Q2(i),a),p=X2(f,i,{ease:Array.isArray(c)?c:J2(i,c)});return{calculatedDuration:a,next:m=>(d.value=p(m),d.done=m>=a,d)}}const $2=a=>a!==null;function ef(a,{repeat:i,repeatType:s="loop"},r,c=1){const d=a.filter($2),p=c<0||i&&s!=="loop"&&i%2===1?0:d.length-1;return!p||r===void 0?d[p]:r}const F2={decay:yc,inertia:yc,tween:Tl,keyframes:Tl,spring:yr};function Vy(a){typeof a.type=="string"&&(a.type=F2[a.type])}class nf{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(i=>{this.resolve=i})}notifyFinished(){this.resolve()}then(i,s){return this.finished.then(i,s)}}const W2=a=>a/100;class af extends nf{constructor(i){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{var r,c;const{motionValue:s}=this.options;s&&s.updatedAt!==he.now()&&this.tick(he.now()),this.isStopped=!0,this.state!=="idle"&&(this.teardown(),(c=(r=this.options).onStop)==null||c.call(r))},this.options=i,this.initAnimation(),this.play(),i.autoplay===!1&&this.pause()}initAnimation(){const{options:i}=this;Vy(i);const{type:s=Tl,repeat:r=0,repeatDelay:c=0,repeatType:d,velocity:f=0}=i;let{keyframes:p}=i;const m=s||Tl;m!==Tl&&typeof p[0]!="number"&&(this.mixKeyframes=kl(W2,Cy(p[0],p[1])),p=[0,100]);const h=m({...i,keyframes:p});d==="mirror"&&(this.mirroredGenerator=m({...i,keyframes:[...p].reverse(),velocity:-f})),h.calculatedDuration===null&&(h.calculatedDuration=tf(h));const{calculatedDuration:v}=h;this.calculatedDuration=v,this.resolvedDuration=v+c,this.totalDuration=this.resolvedDuration*(r+1)-c,this.generator=h}updateTime(i){const s=Math.round(i-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=s}tick(i,s=!1){const{generator:r,totalDuration:c,mixKeyframes:d,mirroredGenerator:f,resolvedDuration:p,calculatedDuration:m}=this;if(this.startTime===null)return r.next(0);const{delay:h=0,keyframes:v,repeat:b,repeatType:S,repeatDelay:E,type:M,onUpdate:V,finalKeyframe:_}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,i):this.speed<0&&(this.startTime=Math.min(i-c/this.speed,this.startTime)),s?this.currentTime=i:this.updateTime(i);const C=this.currentTime-h*(this.playbackSpeed>=0?1:-1),q=this.playbackSpeed>=0?C<0:C>c;this.currentTime=Math.max(C,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=c);let L=this.currentTime,P=r;if(b){const F=Math.min(this.currentTime,c)/p;let rt=Math.floor(F),vt=F%1;!vt&&F>=1&&(vt=1),vt===1&&rt--,rt=Math.min(rt,b+1),!!(rt%2)&&(S==="reverse"?(vt=1-vt,E&&(vt-=E/p)):S==="mirror"&&(P=f)),L=gn(0,1,vt)*p}const Y=q?{done:!1,value:v[0]}:P.next(L);d&&(Y.value=d(Y.value));let{done:$}=Y;!q&&m!==null&&($=this.playbackSpeed>=0?this.currentTime>=c:this.currentTime<=0);const at=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&$);return at&&M!==yc&&(Y.value=ef(v,this.options,_,this.speed)),V&&V(Y.value),at&&this.finish(),Y}then(i,s){return this.finished.then(i,s)}get duration(){return Pe(this.calculatedDuration)}get time(){return Pe(this.currentTime)}set time(i){var s;i=Qe(i),this.currentTime=i,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=i:this.driver&&(this.startTime=this.driver.now()-i/this.playbackSpeed),(s=this.driver)==null||s.start(!1)}get speed(){return this.playbackSpeed}set speed(i){this.updateTime(he.now());const s=this.playbackSpeed!==i;this.playbackSpeed=i,s&&(this.time=Pe(this.currentTime))}play(){var c,d;if(this.isStopped)return;const{driver:i=_2,startTime:s}=this.options;this.driver||(this.driver=i(f=>this.tick(f))),(d=(c=this.options).onPlay)==null||d.call(c);const r=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=r):this.holdTime!==null?this.startTime=r-this.holdTime:this.startTime||(this.startTime=s??r),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(he.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){var i,s;this.notifyFinished(),this.teardown(),this.state="finished",(s=(i=this.options).onComplete)==null||s.call(i)}cancel(){var i,s;this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),(s=(i=this.options).onCancel)==null||s.call(i)}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(i){return this.startTime=0,this.tick(i,!0)}attachTimeline(i){var s;return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),(s=this.driver)==null||s.stop(),i.observe(this)}}function I2(a){for(let i=1;i<a.length;i++)a[i]??(a[i]=a[i-1])}const va=a=>a*180/Math.PI,gc=a=>{const i=va(Math.atan2(a[1],a[0]));return vc(i)},tS={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:a=>(Math.abs(a[0])+Math.abs(a[3]))/2,rotate:gc,rotateZ:gc,skewX:a=>va(Math.atan(a[1])),skewY:a=>va(Math.atan(a[2])),skew:a=>(Math.abs(a[1])+Math.abs(a[2]))/2},vc=a=>(a=a%360,a<0&&(a+=360),a),Lp=gc,Hp=a=>Math.sqrt(a[0]*a[0]+a[1]*a[1]),qp=a=>Math.sqrt(a[4]*a[4]+a[5]*a[5]),eS={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:Hp,scaleY:qp,scale:a=>(Hp(a)+qp(a))/2,rotateX:a=>vc(va(Math.atan2(a[6],a[5]))),rotateY:a=>vc(va(Math.atan2(-a[2],a[0]))),rotateZ:Lp,rotate:Lp,skewX:a=>va(Math.atan(a[4])),skewY:a=>va(Math.atan(a[1])),skew:a=>(Math.abs(a[1])+Math.abs(a[4]))/2};function xc(a){return a.includes("scale")?1:0}function bc(a,i){if(!a||a==="none")return xc(i);const s=a.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let r,c;if(s)r=eS,c=s;else{const p=a.match(/^matrix\(([-\d.e\s,]+)\)$/u);r=tS,c=p}if(!c)return xc(i);const d=r[i],f=c[1].split(",").map(aS);return typeof d=="function"?d(f):f[d]}const nS=(a,i)=>{const{transform:s="none"}=getComputedStyle(a);return bc(s,i)};function aS(a){return parseFloat(a.trim())}const yi=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],gi=new Set(yi),Yp=a=>a===pi||a===lt,iS=new Set(["x","y","z"]),lS=yi.filter(a=>!iS.has(a));function sS(a){const i=[];return lS.forEach(s=>{const r=a.getValue(s);r!==void 0&&(i.push([s,r.get()]),r.set(s.startsWith("scale")?1:0))}),i}const xa={width:({x:a},{paddingLeft:i="0",paddingRight:s="0"})=>a.max-a.min-parseFloat(i)-parseFloat(s),height:({y:a},{paddingTop:i="0",paddingBottom:s="0"})=>a.max-a.min-parseFloat(i)-parseFloat(s),top:(a,{top:i})=>parseFloat(i),left:(a,{left:i})=>parseFloat(i),bottom:({y:a},{top:i})=>parseFloat(i)+(a.max-a.min),right:({x:a},{left:i})=>parseFloat(i)+(a.max-a.min),x:(a,{transform:i})=>bc(i,"x"),y:(a,{transform:i})=>bc(i,"y")};xa.translateX=xa.x;xa.translateY=xa.y;const ba=new Set;let Sc=!1,Tc=!1,Ac=!1;function _y(){if(Tc){const a=Array.from(ba).filter(r=>r.needsMeasurement),i=new Set(a.map(r=>r.element)),s=new Map;i.forEach(r=>{const c=sS(r);c.length&&(s.set(r,c),r.render())}),a.forEach(r=>r.measureInitialState()),i.forEach(r=>{r.render();const c=s.get(r);c&&c.forEach(([d,f])=>{var p;(p=r.getValue(d))==null||p.set(f)})}),a.forEach(r=>r.measureEndState()),a.forEach(r=>{r.suspendedScrollY!==void 0&&window.scrollTo(0,r.suspendedScrollY)})}Tc=!1,Sc=!1,ba.forEach(a=>a.complete(Ac)),ba.clear()}function Uy(){ba.forEach(a=>{a.readKeyframes(),a.needsMeasurement&&(Tc=!0)})}function rS(){Ac=!0,Uy(),_y(),Ac=!1}class lf{constructor(i,s,r,c,d,f=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...i],this.onComplete=s,this.name=r,this.motionValue=c,this.element=d,this.isAsync=f}scheduleResolve(){this.state="scheduled",this.isAsync?(ba.add(this),Sc||(Sc=!0,Ct.read(Uy),Ct.resolveKeyframes(_y))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:i,name:s,element:r,motionValue:c}=this;if(i[0]===null){const d=c==null?void 0:c.get(),f=i[i.length-1];if(d!==void 0)i[0]=d;else if(r&&s){const p=r.readValue(s,f);p!=null&&(i[0]=p)}i[0]===void 0&&(i[0]=f),c&&d===void 0&&c.set(i[0])}I2(i)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(i=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,i),ba.delete(this)}cancel(){this.state==="scheduled"&&(ba.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const oS=a=>a.startsWith("--");function uS(a,i,s){oS(i)?a.style.setProperty(i,s):a.style[i]=s}const cS=Xc(()=>window.ScrollTimeline!==void 0),fS={};function dS(a,i){const s=Xc(a);return()=>fS[i]??s()}const ky=dS(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),bl=([a,i,s,r])=>`cubic-bezier(${a}, ${i}, ${s}, ${r})`,Gp={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:bl([0,.65,.55,1]),circOut:bl([.55,0,1,.45]),backIn:bl([.31,.01,.66,-.59]),backOut:bl([.33,1.53,.69,.99])};function By(a,i){if(a)return typeof a=="function"?ky()?Oy(a,i):"ease-out":Ty(a)?bl(a):Array.isArray(a)?a.map(s=>By(s,i)||Gp.easeOut):Gp[a]}function hS(a,i,s,{delay:r=0,duration:c=300,repeat:d=0,repeatType:f="loop",ease:p="easeOut",times:m}={},h=void 0){const v={[i]:s};m&&(v.offset=m);const b=By(p,c);Array.isArray(b)&&(v.easing=b);const S={delay:r,duration:c,easing:Array.isArray(b)?"linear":b,fill:"both",iterations:d+1,direction:f==="reverse"?"alternate":"normal"};return h&&(S.pseudoElement=h),a.animate(v,S)}function Ly(a){return typeof a=="function"&&"applyToOptions"in a}function mS({type:a,...i}){return Ly(a)&&ky()?a.applyToOptions(i):(i.duration??(i.duration=300),i.ease??(i.ease="easeOut"),i)}class pS extends nf{constructor(i){if(super(),this.finishedTime=null,this.isStopped=!1,!i)return;const{element:s,name:r,keyframes:c,pseudoElement:d,allowFlatten:f=!1,finalKeyframe:p,onComplete:m}=i;this.isPseudoElement=!!d,this.allowFlatten=f,this.options=i,Kc(typeof i.type!="string");const h=mS(i);this.animation=hS(s,r,c,h,d),h.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!d){const v=ef(c,this.options,p,this.speed);this.updateMotionValue?this.updateMotionValue(v):uS(s,r,v),this.animation.cancel()}m==null||m(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){var i,s;(s=(i=this.animation).finish)==null||s.call(i)}cancel(){try{this.animation.cancel()}catch{}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:i}=this;i==="idle"||i==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){var i,s;this.isPseudoElement||(s=(i=this.animation).commitStyles)==null||s.call(i)}get duration(){var s,r;const i=((r=(s=this.animation.effect)==null?void 0:s.getComputedTiming)==null?void 0:r.call(s).duration)||0;return Pe(Number(i))}get time(){return Pe(Number(this.animation.currentTime)||0)}set time(i){this.finishedTime=null,this.animation.currentTime=Qe(i)}get speed(){return this.animation.playbackRate}set speed(i){i<0&&(this.finishedTime=null),this.animation.playbackRate=i}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(i){this.animation.startTime=i}attachTimeline({timeline:i,observe:s}){var r;return this.allowFlatten&&((r=this.animation.effect)==null||r.updateTiming({easing:"linear"})),this.animation.onfinish=null,i&&cS()?(this.animation.timeline=i,_e):s(this)}}const Hy={anticipate:vy,backInOut:gy,circInOut:by};function yS(a){return a in Hy}function gS(a){typeof a.ease=="string"&&yS(a.ease)&&(a.ease=Hy[a.ease])}const Kp=10;class vS extends pS{constructor(i){gS(i),Vy(i),super(i),i.startTime&&(this.startTime=i.startTime),this.options=i}updateMotionValue(i){const{motionValue:s,onUpdate:r,onComplete:c,element:d,...f}=this.options;if(!s)return;if(i!==void 0){s.set(i);return}const p=new af({...f,autoplay:!1}),m=Qe(this.finishedTime??this.time);s.setWithVelocity(p.sample(m-Kp).value,p.sample(m).value,Kp),p.stop()}}const Xp=(a,i)=>i==="zIndex"?!1:!!(typeof a=="number"||Array.isArray(a)||typeof a=="string"&&(Qn.test(a)||a==="0")&&!a.startsWith("url("));function xS(a){const i=a[0];if(a.length===1)return!0;for(let s=0;s<a.length;s++)if(a[s]!==i)return!0}function bS(a,i,s,r){const c=a[0];if(c===null)return!1;if(i==="display"||i==="visibility")return!0;const d=a[a.length-1],f=Xp(c,i),p=Xp(d,i);return!f||!p?!1:xS(a)||(s==="spring"||Ly(s))&&r}function sf(a){return cy(a)&&"offsetHeight"in a}const SS=new Set(["opacity","clipPath","filter","transform"]),TS=Xc(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function AS(a){var h;const{motionValue:i,name:s,repeatDelay:r,repeatType:c,damping:d,type:f}=a;if(!sf((h=i==null?void 0:i.owner)==null?void 0:h.current))return!1;const{onUpdate:p,transformTemplate:m}=i.owner.getProps();return TS()&&s&&SS.has(s)&&(s!=="transform"||!m)&&!p&&!r&&c!=="mirror"&&d!==0&&f!=="inertia"}const jS=40;class ES extends nf{constructor({autoplay:i=!0,delay:s=0,type:r="keyframes",repeat:c=0,repeatDelay:d=0,repeatType:f="loop",keyframes:p,name:m,motionValue:h,element:v,...b}){var M;super(),this.stop=()=>{var V,_;this._animation&&(this._animation.stop(),(V=this.stopTimeline)==null||V.call(this)),(_=this.keyframeResolver)==null||_.cancel()},this.createdAt=he.now();const S={autoplay:i,delay:s,type:r,repeat:c,repeatDelay:d,repeatType:f,name:m,motionValue:h,element:v,...b},E=(v==null?void 0:v.KeyframeResolver)||lf;this.keyframeResolver=new E(p,(V,_,C)=>this.onKeyframesResolved(V,_,S,!C),m,h,v),(M=this.keyframeResolver)==null||M.scheduleResolve()}onKeyframesResolved(i,s,r,c){this.keyframeResolver=void 0;const{name:d,type:f,velocity:p,delay:m,isHandoff:h,onUpdate:v}=r;this.resolvedAt=he.now(),bS(i,d,f,p)||((vn.instantAnimations||!m)&&(v==null||v(ef(i,r,s))),i[0]=i[i.length-1],r.duration=0,r.repeat=0);const S={startTime:c?this.resolvedAt?this.resolvedAt-this.createdAt>jS?this.resolvedAt:this.createdAt:this.createdAt:void 0,finalKeyframe:s,...r,keyframes:i},E=!h&&AS(S)?new vS({...S,element:S.motionValue.owner.current}):new af(S);E.finished.then(()=>this.notifyFinished()).catch(_e),this.pendingTimeline&&(this.stopTimeline=E.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=E}get finished(){return this._animation?this.animation.finished:this._finished}then(i,s){return this.finished.finally(i).then(()=>{})}get animation(){var i;return this._animation||((i=this.keyframeResolver)==null||i.resume(),rS()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(i){this.animation.time=i}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(i){this.animation.speed=i}get startTime(){return this.animation.startTime}attachTimeline(i){return this._animation?this.stopTimeline=this.animation.attachTimeline(i):this.pendingTimeline=i,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){var i;this._animation&&this.animation.cancel(),(i=this.keyframeResolver)==null||i.cancel()}}const wS=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function NS(a){const i=wS.exec(a);if(!i)return[,];const[,s,r,c]=i;return[`--${s??r}`,c]}function qy(a,i,s=1){const[r,c]=NS(a);if(!r)return;const d=window.getComputedStyle(i).getPropertyValue(r);if(d){const f=d.trim();return uy(f)?parseFloat(f):f}return $c(c)?qy(c,i,s+1):c}function rf(a,i){return(a==null?void 0:a[i])??(a==null?void 0:a.default)??a}const Yy=new Set(["width","height","top","left","right","bottom",...yi]),MS={test:a=>a==="auto",parse:a=>a},Gy=a=>i=>i.test(a),Ky=[pi,lt,Je,Xn,x2,v2,MS],Zp=a=>Ky.find(Gy(a));function RS(a){return typeof a=="number"?a===0:a!==null?a==="none"||a==="0"||fy(a):!0}const DS=new Set(["brightness","contrast","saturate","opacity"]);function CS(a){const[i,s]=a.slice(0,-1).split("(");if(i==="drop-shadow")return a;const[r]=s.match(Fc)||[];if(!r)return a;const c=s.replace(r,"");let d=DS.has(i)?1:0;return r!==s&&(d*=100),i+"("+d+c+")"}const OS=/\b([a-z-]*)\(.*?\)/gu,jc={...Qn,getAnimatableNone:a=>{const i=a.match(OS);return i?i.map(CS).join(" "):a}},Qp={...pi,transform:Math.round},zS={rotate:Xn,rotateX:Xn,rotateY:Xn,rotateZ:Xn,scale:ir,scaleX:ir,scaleY:ir,scaleZ:ir,skew:Xn,skewX:Xn,skewY:Xn,distance:lt,translateX:lt,translateY:lt,translateZ:lt,x:lt,y:lt,z:lt,perspective:lt,transformPerspective:lt,opacity:Ml,originX:Vp,originY:Vp,originZ:lt},of={borderWidth:lt,borderTopWidth:lt,borderRightWidth:lt,borderBottomWidth:lt,borderLeftWidth:lt,borderRadius:lt,radius:lt,borderTopLeftRadius:lt,borderTopRightRadius:lt,borderBottomRightRadius:lt,borderBottomLeftRadius:lt,width:lt,maxWidth:lt,height:lt,maxHeight:lt,top:lt,right:lt,bottom:lt,left:lt,padding:lt,paddingTop:lt,paddingRight:lt,paddingBottom:lt,paddingLeft:lt,margin:lt,marginTop:lt,marginRight:lt,marginBottom:lt,marginLeft:lt,backgroundPositionX:lt,backgroundPositionY:lt,...zS,zIndex:Qp,fillOpacity:Ml,strokeOpacity:Ml,numOctaves:Qp},VS={...of,color:qt,backgroundColor:qt,outlineColor:qt,fill:qt,stroke:qt,borderColor:qt,borderTopColor:qt,borderRightColor:qt,borderBottomColor:qt,borderLeftColor:qt,filter:jc,WebkitFilter:jc},Xy=a=>VS[a];function Zy(a,i){let s=Xy(a);return s!==jc&&(s=Qn),s.getAnimatableNone?s.getAnimatableNone(i):void 0}const _S=new Set(["auto","none","0"]);function US(a,i,s){let r=0,c;for(;r<a.length&&!c;){const d=a[r];typeof d=="string"&&!_S.has(d)&&Rl(d).values.length&&(c=a[r]),r++}if(c&&s)for(const d of i)a[d]=Zy(s,c)}class kS extends lf{constructor(i,s,r,c,d){super(i,s,r,c,d,!0)}readKeyframes(){const{unresolvedKeyframes:i,element:s,name:r}=this;if(!s||!s.current)return;super.readKeyframes();for(let m=0;m<i.length;m++){let h=i[m];if(typeof h=="string"&&(h=h.trim(),$c(h))){const v=qy(h,s.current);v!==void 0&&(i[m]=v),m===i.length-1&&(this.finalKeyframe=h)}}if(this.resolveNoneKeyframes(),!Yy.has(r)||i.length!==2)return;const[c,d]=i,f=Zp(c),p=Zp(d);if(f!==p)if(Yp(f)&&Yp(p))for(let m=0;m<i.length;m++){const h=i[m];typeof h=="string"&&(i[m]=parseFloat(h))}else xa[r]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:i,name:s}=this,r=[];for(let c=0;c<i.length;c++)(i[c]===null||RS(i[c]))&&r.push(c);r.length&&US(i,r,s)}measureInitialState(){const{element:i,unresolvedKeyframes:s,name:r}=this;if(!i||!i.current)return;r==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=xa[r](i.measureViewportBox(),window.getComputedStyle(i.current)),s[0]=this.measuredOrigin;const c=s[s.length-1];c!==void 0&&i.getValue(r,c).jump(c,!1)}measureEndState(){var p;const{element:i,name:s,unresolvedKeyframes:r}=this;if(!i||!i.current)return;const c=i.getValue(s);c&&c.jump(this.measuredOrigin,!1);const d=r.length-1,f=r[d];r[d]=xa[s](i.measureViewportBox(),window.getComputedStyle(i.current)),f!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=f),(p=this.removedTransforms)!=null&&p.length&&this.removedTransforms.forEach(([m,h])=>{i.getValue(m).set(h)}),this.resolveNoneKeyframes()}}function Qy(a,i,s){if(a instanceof EventTarget)return[a];if(typeof a=="string"){const c=document.querySelectorAll(a);return c?Array.from(c):[]}return Array.from(a)}const Py=(a,i)=>i&&typeof a=="number"?i.transform(a):a,Pp=30,BS=a=>!isNaN(parseFloat(a));class LS{constructor(i,s={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(r,c=!0)=>{var f,p;const d=he.now();if(this.updatedAt!==d&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(r),this.current!==this.prev&&((f=this.events.change)==null||f.notify(this.current),this.dependents))for(const m of this.dependents)m.dirty();c&&((p=this.events.renderRequest)==null||p.notify(this.current))},this.hasAnimated=!1,this.setCurrent(i),this.owner=s.owner}setCurrent(i){this.current=i,this.updatedAt=he.now(),this.canTrackVelocity===null&&i!==void 0&&(this.canTrackVelocity=BS(this.current))}setPrevFrameValue(i=this.current){this.prevFrameValue=i,this.prevUpdatedAt=this.updatedAt}onChange(i){return this.on("change",i)}on(i,s){this.events[i]||(this.events[i]=new Zc);const r=this.events[i].add(s);return i==="change"?()=>{r(),Ct.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const i in this.events)this.events[i].clear()}attach(i,s){this.passiveEffect=i,this.stopPassiveEffect=s}set(i,s=!0){!s||!this.passiveEffect?this.updateAndNotify(i,s):this.passiveEffect(i,this.updateAndNotify)}setWithVelocity(i,s,r){this.set(s),this.prev=void 0,this.prevFrameValue=i,this.prevUpdatedAt=this.updatedAt-r}jump(i,s=!0){this.updateAndNotify(i),this.prev=i,this.prevUpdatedAt=this.prevFrameValue=void 0,s&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){var i;(i=this.events.change)==null||i.notify(this.current)}addDependent(i){this.dependents||(this.dependents=new Set),this.dependents.add(i)}removeDependent(i){this.dependents&&this.dependents.delete(i)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const i=he.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||i-this.updatedAt>Pp)return 0;const s=Math.min(this.updatedAt-this.prevUpdatedAt,Pp);return dy(parseFloat(this.current)-parseFloat(this.prevFrameValue),s)}start(i){return this.stop(),new Promise(s=>{this.hasAnimated=!0,this.animation=i(s),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){var i,s;(i=this.dependents)==null||i.clear(),(s=this.events.destroy)==null||s.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function fi(a,i){return new LS(a,i)}const{schedule:uf}=Ay(queueMicrotask,!1),Le={x:!1,y:!1};function Jy(){return Le.x||Le.y}function HS(a){return a==="x"||a==="y"?Le[a]?null:(Le[a]=!0,()=>{Le[a]=!1}):Le.x||Le.y?null:(Le.x=Le.y=!0,()=>{Le.x=Le.y=!1})}function $y(a,i){const s=Qy(a),r=new AbortController,c={passive:!0,...i,signal:r.signal};return[s,c,()=>r.abort()]}function Jp(a){return!(a.pointerType==="touch"||Jy())}function qS(a,i,s={}){const[r,c,d]=$y(a,s),f=p=>{if(!Jp(p))return;const{target:m}=p,h=i(m,p);if(typeof h!="function"||!m)return;const v=b=>{Jp(b)&&(h(b),m.removeEventListener("pointerleave",v))};m.addEventListener("pointerleave",v,c)};return r.forEach(p=>{p.addEventListener("pointerenter",f,c)}),d}const Fy=(a,i)=>i?a===i?!0:Fy(a,i.parentElement):!1,cf=a=>a.pointerType==="mouse"?typeof a.button!="number"||a.button<=0:a.isPrimary!==!1,YS=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function GS(a){return YS.has(a.tagName)||a.tabIndex!==-1}const cr=new WeakSet;function $p(a){return i=>{i.key==="Enter"&&a(i)}}function ac(a,i){a.dispatchEvent(new PointerEvent("pointer"+i,{isPrimary:!0,bubbles:!0}))}const KS=(a,i)=>{const s=a.currentTarget;if(!s)return;const r=$p(()=>{if(cr.has(s))return;ac(s,"down");const c=$p(()=>{ac(s,"up")}),d=()=>ac(s,"cancel");s.addEventListener("keyup",c,i),s.addEventListener("blur",d,i)});s.addEventListener("keydown",r,i),s.addEventListener("blur",()=>s.removeEventListener("keydown",r),i)};function Fp(a){return cf(a)&&!Jy()}function XS(a,i,s={}){const[r,c,d]=$y(a,s),f=p=>{const m=p.currentTarget;if(!Fp(p))return;cr.add(m);const h=i(m,p),v=(E,M)=>{window.removeEventListener("pointerup",b),window.removeEventListener("pointercancel",S),cr.has(m)&&cr.delete(m),Fp(E)&&typeof h=="function"&&h(E,{success:M})},b=E=>{v(E,m===window||m===document||s.useGlobalTarget||Fy(m,E.target))},S=E=>{v(E,!1)};window.addEventListener("pointerup",b,c),window.addEventListener("pointercancel",S,c)};return r.forEach(p=>{(s.useGlobalTarget?window:p).addEventListener("pointerdown",f,c),sf(p)&&(p.addEventListener("focus",h=>KS(h,c)),!GS(p)&&!p.hasAttribute("tabindex")&&(p.tabIndex=0))}),d}function Wy(a){return cy(a)&&"ownerSVGElement"in a}function ZS(a){return Wy(a)&&a.tagName==="svg"}const ie=a=>!!(a&&a.getVelocity),QS=[...Ky,qt,Qn],PS=a=>QS.find(Gy(a)),ff=w.createContext({transformPagePoint:a=>a,isStatic:!1,reducedMotion:"never"});class JS extends w.Component{getSnapshotBeforeUpdate(i){const s=this.props.childRef.current;if(s&&i.isPresent&&!this.props.isPresent){const r=s.offsetParent,c=sf(r)&&r.offsetWidth||0,d=this.props.sizeRef.current;d.height=s.offsetHeight||0,d.width=s.offsetWidth||0,d.top=s.offsetTop,d.left=s.offsetLeft,d.right=c-d.width-d.left}return null}componentDidUpdate(){}render(){return this.props.children}}function $S({children:a,isPresent:i,anchorX:s}){const r=w.useId(),c=w.useRef(null),d=w.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:f}=w.useContext(ff);return w.useInsertionEffect(()=>{const{width:p,height:m,top:h,left:v,right:b}=d.current;if(i||!c.current||!p||!m)return;const S=s==="left"?`left: ${v}`:`right: ${b}`;c.current.dataset.motionPopId=r;const E=document.createElement("style");return f&&(E.nonce=f),document.head.appendChild(E),E.sheet&&E.sheet.insertRule(`
          [data-motion-pop-id="${r}"] {
            position: absolute !important;
            width: ${p}px !important;
            height: ${m}px !important;
            ${S}px !important;
            top: ${h}px !important;
          }
        `),()=>{document.head.contains(E)&&document.head.removeChild(E)}},[i]),g.jsx(JS,{isPresent:i,childRef:c,sizeRef:d,children:w.cloneElement(a,{ref:c})})}const FS=({children:a,initial:i,isPresent:s,onExitComplete:r,custom:c,presenceAffectsLayout:d,mode:f,anchorX:p})=>{const m=Hc(WS),h=w.useId();let v=!0,b=w.useMemo(()=>(v=!1,{id:h,initial:i,isPresent:s,custom:c,onExitComplete:S=>{m.set(S,!0);for(const E of m.values())if(!E)return;r&&r()},register:S=>(m.set(S,!1),()=>m.delete(S))}),[s,m,r]);return d&&v&&(b={...b}),w.useMemo(()=>{m.forEach((S,E)=>m.set(E,!1))},[s]),w.useEffect(()=>{!s&&!m.size&&r&&r()},[s]),f==="popLayout"&&(a=g.jsx($S,{isPresent:s,anchorX:p,children:a})),g.jsx(Tr.Provider,{value:b,children:a})};function WS(){return new Map}function Iy(a=!0){const i=w.useContext(Tr);if(i===null)return[!0,null];const{isPresent:s,onExitComplete:r,register:c}=i,d=w.useId();w.useEffect(()=>{if(a)return c(d)},[a]);const f=w.useCallback(()=>a&&r&&r(d),[d,r,a]);return!s&&r?[!1,f]:[!0]}const lr=a=>a.key||"";function Wp(a){const i=[];return w.Children.forEach(a,s=>{w.isValidElement(s)&&i.push(s)}),i}const IS=({children:a,custom:i,initial:s=!0,onExitComplete:r,presenceAffectsLayout:c=!0,mode:d="sync",propagate:f=!1,anchorX:p="left"})=>{const[m,h]=Iy(f),v=w.useMemo(()=>Wp(a),[a]),b=f&&!m?[]:v.map(lr),S=w.useRef(!0),E=w.useRef(v),M=Hc(()=>new Map),[V,_]=w.useState(v),[C,q]=w.useState(v);oy(()=>{S.current=!1,E.current=v;for(let Y=0;Y<C.length;Y++){const $=lr(C[Y]);b.includes($)?M.delete($):M.get($)!==!0&&M.set($,!1)}},[C,b.length,b.join("-")]);const L=[];if(v!==V){let Y=[...v];for(let $=0;$<C.length;$++){const at=C[$],F=lr(at);b.includes(F)||(Y.splice($,0,at),L.push(at))}return d==="wait"&&L.length&&(Y=L),q(Wp(Y)),_(v),null}const{forceRender:P}=w.useContext(Lc);return g.jsx(g.Fragment,{children:C.map(Y=>{const $=lr(Y),at=f&&!m?!1:v===C||b.includes($),F=()=>{if(M.has($))M.set($,!0);else return;let rt=!0;M.forEach(vt=>{vt||(rt=!1)}),rt&&(P==null||P(),q(E.current),f&&(h==null||h()),r&&r())};return g.jsx(FS,{isPresent:at,initial:!S.current||s?void 0:!1,custom:i,presenceAffectsLayout:c,mode:d,onExitComplete:at?void 0:F,anchorX:p,children:Y},$)})})},tg=w.createContext({strict:!1}),Ip={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},di={};for(const a in Ip)di[a]={isEnabled:i=>Ip[a].some(s=>!!i[s])};function tT(a){for(const i in a)di[i]={...di[i],...a[i]}}const eT=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function gr(a){return a.startsWith("while")||a.startsWith("drag")&&a!=="draggable"||a.startsWith("layout")||a.startsWith("onTap")||a.startsWith("onPan")||a.startsWith("onLayout")||eT.has(a)}let eg=a=>!gr(a);function nT(a){typeof a=="function"&&(eg=i=>i.startsWith("on")?!gr(i):a(i))}try{nT(require("@emotion/is-prop-valid").default)}catch{}function aT(a,i,s){const r={};for(const c in a)c==="values"&&typeof a.values=="object"||(eg(c)||s===!0&&gr(c)||!i&&!gr(c)||a.draggable&&c.startsWith("onDrag"))&&(r[c]=a[c]);return r}function iT(a){if(typeof Proxy>"u")return a;const i=new Map,s=(...r)=>a(...r);return new Proxy(s,{get:(r,c)=>c==="create"?a:(i.has(c)||i.set(c,a(c)),i.get(c))})}const Ar=w.createContext({});function jr(a){return a!==null&&typeof a=="object"&&typeof a.start=="function"}function Dl(a){return typeof a=="string"||Array.isArray(a)}const df=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],hf=["initial",...df];function Er(a){return jr(a.animate)||hf.some(i=>Dl(a[i]))}function ng(a){return!!(Er(a)||a.variants)}function lT(a,i){if(Er(a)){const{initial:s,animate:r}=a;return{initial:s===!1||Dl(s)?s:void 0,animate:Dl(r)?r:void 0}}return a.inherit!==!1?i:{}}function sT(a){const{initial:i,animate:s}=lT(a,w.useContext(Ar));return w.useMemo(()=>({initial:i,animate:s}),[t0(i),t0(s)])}function t0(a){return Array.isArray(a)?a.join(" "):a}const rT=Symbol.for("motionComponentSymbol");function ri(a){return a&&typeof a=="object"&&Object.prototype.hasOwnProperty.call(a,"current")}function oT(a,i,s){return w.useCallback(r=>{r&&a.onMount&&a.onMount(r),i&&(r?i.mount(r):i.unmount()),s&&(typeof s=="function"?s(r):ri(s)&&(s.current=r))},[i])}const mf=a=>a.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),uT="framerAppearId",ag="data-"+mf(uT),ig=w.createContext({});function cT(a,i,s,r,c){var V,_;const{visualElement:d}=w.useContext(Ar),f=w.useContext(tg),p=w.useContext(Tr),m=w.useContext(ff).reducedMotion,h=w.useRef(null);r=r||f.renderer,!h.current&&r&&(h.current=r(a,{visualState:i,parent:d,props:s,presenceContext:p,blockInitialAnimation:p?p.initial===!1:!1,reducedMotionConfig:m}));const v=h.current,b=w.useContext(ig);v&&!v.projection&&c&&(v.type==="html"||v.type==="svg")&&fT(h.current,s,c,b);const S=w.useRef(!1);w.useInsertionEffect(()=>{v&&S.current&&v.update(s,p)});const E=s[ag],M=w.useRef(!!E&&!((V=window.MotionHandoffIsComplete)!=null&&V.call(window,E))&&((_=window.MotionHasOptimisedAnimation)==null?void 0:_.call(window,E)));return oy(()=>{v&&(S.current=!0,window.MotionIsMounted=!0,v.updateFeatures(),uf.render(v.render),M.current&&v.animationState&&v.animationState.animateChanges())}),w.useEffect(()=>{v&&(!M.current&&v.animationState&&v.animationState.animateChanges(),M.current&&(queueMicrotask(()=>{var C;(C=window.MotionHandoffMarkAsComplete)==null||C.call(window,E)}),M.current=!1))}),v}function fT(a,i,s,r){const{layoutId:c,layout:d,drag:f,dragConstraints:p,layoutScroll:m,layoutRoot:h,layoutCrossfade:v}=i;a.projection=new s(a.latestValues,i["data-framer-portal-id"]?void 0:lg(a.parent)),a.projection.setOptions({layoutId:c,layout:d,alwaysMeasureLayout:!!f||p&&ri(p),visualElement:a,animationType:typeof d=="string"?d:"both",initialPromotionConfig:r,crossfade:v,layoutScroll:m,layoutRoot:h})}function lg(a){if(a)return a.options.allowProjection!==!1?a.projection:lg(a.parent)}function dT({preloadedFeatures:a,createVisualElement:i,useRender:s,useVisualState:r,Component:c}){a&&tT(a);function d(p,m){let h;const v={...w.useContext(ff),...p,layoutId:hT(p)},{isStatic:b}=v,S=sT(p),E=r(p,b);if(!b&&qc){mT();const M=pT(v);h=M.MeasureLayout,S.visualElement=cT(c,E,v,i,M.ProjectionNode)}return g.jsxs(Ar.Provider,{value:S,children:[h&&S.visualElement?g.jsx(h,{visualElement:S.visualElement,...v}):null,s(c,p,oT(E,S.visualElement,m),E,b,S.visualElement)]})}d.displayName=`motion.${typeof c=="string"?c:`create(${c.displayName??c.name??""})`}`;const f=w.forwardRef(d);return f[rT]=c,f}function hT({layoutId:a}){const i=w.useContext(Lc).id;return i&&a!==void 0?i+"-"+a:a}function mT(a,i){w.useContext(tg).strict}function pT(a){const{drag:i,layout:s}=di;if(!i&&!s)return{};const r={...i,...s};return{MeasureLayout:i!=null&&i.isEnabled(a)||s!=null&&s.isEnabled(a)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}const Cl={};function yT(a){for(const i in a)Cl[i]=a[i],Jc(i)&&(Cl[i].isCSSVariable=!0)}function sg(a,{layout:i,layoutId:s}){return gi.has(a)||a.startsWith("origin")||(i||s!==void 0)&&(!!Cl[a]||a==="opacity")}const gT={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},vT=yi.length;function xT(a,i,s){let r="",c=!0;for(let d=0;d<vT;d++){const f=yi[d],p=a[f];if(p===void 0)continue;let m=!0;if(typeof p=="number"?m=p===(f.startsWith("scale")?1:0):m=parseFloat(p)===0,!m||s){const h=Py(p,of[f]);if(!m){c=!1;const v=gT[f]||f;r+=`${v}(${h}) `}s&&(i[f]=h)}}return r=r.trim(),s?r=s(i,c?"":r):c&&(r="none"),r}function pf(a,i,s){const{style:r,vars:c,transformOrigin:d}=a;let f=!1,p=!1;for(const m in i){const h=i[m];if(gi.has(m)){f=!0;continue}else if(Jc(m)){c[m]=h;continue}else{const v=Py(h,of[m]);m.startsWith("origin")?(p=!0,d[m]=v):r[m]=v}}if(i.transform||(f||s?r.transform=xT(i,a.transform,s):r.transform&&(r.transform="none")),p){const{originX:m="50%",originY:h="50%",originZ:v=0}=d;r.transformOrigin=`${m} ${h} ${v}`}}const yf=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function rg(a,i,s){for(const r in i)!ie(i[r])&&!sg(r,s)&&(a[r]=i[r])}function bT({transformTemplate:a},i){return w.useMemo(()=>{const s=yf();return pf(s,i,a),Object.assign({},s.vars,s.style)},[i])}function ST(a,i){const s=a.style||{},r={};return rg(r,s,a),Object.assign(r,bT(a,i)),r}function TT(a,i){const s={},r=ST(a,i);return a.drag&&a.dragListener!==!1&&(s.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=a.drag===!0?"none":`pan-${a.drag==="x"?"y":"x"}`),a.tabIndex===void 0&&(a.onTap||a.onTapStart||a.whileTap)&&(s.tabIndex=0),s.style=r,s}const AT={offset:"stroke-dashoffset",array:"stroke-dasharray"},jT={offset:"strokeDashoffset",array:"strokeDasharray"};function ET(a,i,s=1,r=0,c=!0){a.pathLength=1;const d=c?AT:jT;a[d.offset]=lt.transform(-r);const f=lt.transform(i),p=lt.transform(s);a[d.array]=`${f} ${p}`}function og(a,{attrX:i,attrY:s,attrScale:r,pathLength:c,pathSpacing:d=1,pathOffset:f=0,...p},m,h,v){if(pf(a,p,h),m){a.style.viewBox&&(a.attrs.viewBox=a.style.viewBox);return}a.attrs=a.style,a.style={};const{attrs:b,style:S}=a;b.transform&&(S.transform=b.transform,delete b.transform),(S.transform||b.transformOrigin)&&(S.transformOrigin=b.transformOrigin??"50% 50%",delete b.transformOrigin),S.transform&&(S.transformBox=(v==null?void 0:v.transformBox)??"fill-box",delete b.transformBox),i!==void 0&&(b.x=i),s!==void 0&&(b.y=s),r!==void 0&&(b.scale=r),c!==void 0&&ET(b,c,d,f,!1)}const ug=()=>({...yf(),attrs:{}}),cg=a=>typeof a=="string"&&a.toLowerCase()==="svg";function wT(a,i,s,r){const c=w.useMemo(()=>{const d=ug();return og(d,i,cg(r),a.transformTemplate,a.style),{...d.attrs,style:{...d.style}}},[i]);if(a.style){const d={};rg(d,a.style,a),c.style={...d,...c.style}}return c}const NT=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function gf(a){return typeof a!="string"||a.includes("-")?!1:!!(NT.indexOf(a)>-1||/[A-Z]/u.test(a))}function MT(a=!1){return(s,r,c,{latestValues:d},f)=>{const m=(gf(s)?wT:TT)(r,d,f,s),h=aT(r,typeof s=="string",a),v=s!==w.Fragment?{...h,...m,ref:c}:{},{children:b}=r,S=w.useMemo(()=>ie(b)?b.get():b,[b]);return w.createElement(s,{...v,children:S})}}function e0(a){const i=[{},{}];return a==null||a.values.forEach((s,r)=>{i[0][r]=s.get(),i[1][r]=s.getVelocity()}),i}function vf(a,i,s,r){if(typeof i=="function"){const[c,d]=e0(r);i=i(s!==void 0?s:a.custom,c,d)}if(typeof i=="string"&&(i=a.variants&&a.variants[i]),typeof i=="function"){const[c,d]=e0(r);i=i(s!==void 0?s:a.custom,c,d)}return i}function fr(a){return ie(a)?a.get():a}function RT({scrapeMotionValuesFromProps:a,createRenderState:i},s,r,c){return{latestValues:DT(s,r,c,a),renderState:i()}}const fg=a=>(i,s)=>{const r=w.useContext(Ar),c=w.useContext(Tr),d=()=>RT(a,i,r,c);return s?d():Hc(d)};function DT(a,i,s,r){const c={},d=r(a,{});for(const S in d)c[S]=fr(d[S]);let{initial:f,animate:p}=a;const m=Er(a),h=ng(a);i&&h&&!m&&a.inherit!==!1&&(f===void 0&&(f=i.initial),p===void 0&&(p=i.animate));let v=s?s.initial===!1:!1;v=v||f===!1;const b=v?p:f;if(b&&typeof b!="boolean"&&!jr(b)){const S=Array.isArray(b)?b:[b];for(let E=0;E<S.length;E++){const M=vf(a,S[E]);if(M){const{transitionEnd:V,transition:_,...C}=M;for(const q in C){let L=C[q];if(Array.isArray(L)){const P=v?L.length-1:0;L=L[P]}L!==null&&(c[q]=L)}for(const q in V)c[q]=V[q]}}}return c}function xf(a,i,s){var d;const{style:r}=a,c={};for(const f in r)(ie(r[f])||i.style&&ie(i.style[f])||sg(f,a)||((d=s==null?void 0:s.getValue(f))==null?void 0:d.liveStyle)!==void 0)&&(c[f]=r[f]);return c}const CT={useVisualState:fg({scrapeMotionValuesFromProps:xf,createRenderState:yf})};function dg(a,i,s){const r=xf(a,i,s);for(const c in a)if(ie(a[c])||ie(i[c])){const d=yi.indexOf(c)!==-1?"attr"+c.charAt(0).toUpperCase()+c.substring(1):c;r[d]=a[c]}return r}const OT={useVisualState:fg({scrapeMotionValuesFromProps:dg,createRenderState:ug})};function zT(a,i){return function(r,{forwardMotionProps:c}={forwardMotionProps:!1}){const f={...gf(r)?OT:CT,preloadedFeatures:a,useRender:MT(c),createVisualElement:i,Component:r};return dT(f)}}function Ol(a,i,s){const r=a.getProps();return vf(r,i,s!==void 0?s:r.custom,a)}const Ec=a=>Array.isArray(a);function VT(a,i,s){a.hasValue(i)?a.getValue(i).set(s):a.addValue(i,fi(s))}function _T(a){return Ec(a)?a[a.length-1]||0:a}function UT(a,i){const s=Ol(a,i);let{transitionEnd:r={},transition:c={},...d}=s||{};d={...d,...r};for(const f in d){const p=_T(d[f]);VT(a,f,p)}}function kT(a){return!!(ie(a)&&a.add)}function wc(a,i){const s=a.getValue("willChange");if(kT(s))return s.add(i);if(!s&&vn.WillChange){const r=new vn.WillChange("auto");a.addValue("willChange",r),r.add(i)}}function hg(a){return a.props[ag]}const BT=a=>a!==null;function LT(a,{repeat:i,repeatType:s="loop"},r){const c=a.filter(BT),d=i&&s!=="loop"&&i%2===1?0:c.length-1;return c[d]}const HT={type:"spring",stiffness:500,damping:25,restSpeed:10},qT=a=>({type:"spring",stiffness:550,damping:a===0?2*Math.sqrt(550):30,restSpeed:10}),YT={type:"keyframes",duration:.8},GT={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},KT=(a,{keyframes:i})=>i.length>2?YT:gi.has(a)?a.startsWith("scale")?qT(i[1]):HT:GT;function XT({when:a,delay:i,delayChildren:s,staggerChildren:r,staggerDirection:c,repeat:d,repeatType:f,repeatDelay:p,from:m,elapsed:h,...v}){return!!Object.keys(v).length}const bf=(a,i,s,r={},c,d)=>f=>{const p=rf(r,a)||{},m=p.delay||r.delay||0;let{elapsed:h=0}=r;h=h-Qe(m);const v={keyframes:Array.isArray(s)?s:[null,s],ease:"easeOut",velocity:i.getVelocity(),...p,delay:-h,onUpdate:S=>{i.set(S),p.onUpdate&&p.onUpdate(S)},onComplete:()=>{f(),p.onComplete&&p.onComplete()},name:a,motionValue:i,element:d?void 0:c};XT(p)||Object.assign(v,KT(a,v)),v.duration&&(v.duration=Qe(v.duration)),v.repeatDelay&&(v.repeatDelay=Qe(v.repeatDelay)),v.from!==void 0&&(v.keyframes[0]=v.from);let b=!1;if((v.type===!1||v.duration===0&&!v.repeatDelay)&&(v.duration=0,v.delay===0&&(b=!0)),(vn.instantAnimations||vn.skipAnimations)&&(b=!0,v.duration=0,v.delay=0),v.allowFlatten=!p.type&&!p.ease,b&&!d&&i.get()!==void 0){const S=LT(v.keyframes,p);if(S!==void 0){Ct.update(()=>{v.onUpdate(S),v.onComplete()});return}}return p.isSync?new af(v):new ES(v)};function ZT({protectedKeys:a,needsAnimating:i},s){const r=a.hasOwnProperty(s)&&i[s]!==!0;return i[s]=!1,r}function mg(a,i,{delay:s=0,transitionOverride:r,type:c}={}){let{transition:d=a.getDefaultTransition(),transitionEnd:f,...p}=i;r&&(d=r);const m=[],h=c&&a.animationState&&a.animationState.getState()[c];for(const v in p){const b=a.getValue(v,a.latestValues[v]??null),S=p[v];if(S===void 0||h&&ZT(h,v))continue;const E={delay:s,...rf(d||{},v)},M=b.get();if(M!==void 0&&!b.isAnimating&&!Array.isArray(S)&&S===M&&!E.velocity)continue;let V=!1;if(window.MotionHandoffAnimation){const C=hg(a);if(C){const q=window.MotionHandoffAnimation(C,v,Ct);q!==null&&(E.startTime=q,V=!0)}}wc(a,v),b.start(bf(v,b,S,a.shouldReduceMotion&&Yy.has(v)?{type:!1}:E,a,V));const _=b.animation;_&&m.push(_)}return f&&Promise.all(m).then(()=>{Ct.update(()=>{f&&UT(a,f)})}),m}function Nc(a,i,s={}){var m;const r=Ol(a,i,s.type==="exit"?(m=a.presenceContext)==null?void 0:m.custom:void 0);let{transition:c=a.getDefaultTransition()||{}}=r||{};s.transitionOverride&&(c=s.transitionOverride);const d=r?()=>Promise.all(mg(a,r,s)):()=>Promise.resolve(),f=a.variantChildren&&a.variantChildren.size?(h=0)=>{const{delayChildren:v=0,staggerChildren:b,staggerDirection:S}=c;return QT(a,i,v+h,b,S,s)}:()=>Promise.resolve(),{when:p}=c;if(p){const[h,v]=p==="beforeChildren"?[d,f]:[f,d];return h().then(()=>v())}else return Promise.all([d(),f(s.delay)])}function QT(a,i,s=0,r=0,c=1,d){const f=[],p=(a.variantChildren.size-1)*r,m=c===1?(h=0)=>h*r:(h=0)=>p-h*r;return Array.from(a.variantChildren).sort(PT).forEach((h,v)=>{h.notify("AnimationStart",i),f.push(Nc(h,i,{...d,delay:s+m(v)}).then(()=>h.notify("AnimationComplete",i)))}),Promise.all(f)}function PT(a,i){return a.sortNodePosition(i)}function JT(a,i,s={}){a.notify("AnimationStart",i);let r;if(Array.isArray(i)){const c=i.map(d=>Nc(a,d,s));r=Promise.all(c)}else if(typeof i=="string")r=Nc(a,i,s);else{const c=typeof i=="function"?Ol(a,i,s.custom):i;r=Promise.all(mg(a,c,s))}return r.then(()=>{a.notify("AnimationComplete",i)})}function pg(a,i){if(!Array.isArray(i))return!1;const s=i.length;if(s!==a.length)return!1;for(let r=0;r<s;r++)if(i[r]!==a[r])return!1;return!0}const $T=hf.length;function yg(a){if(!a)return;if(!a.isControllingVariants){const s=a.parent?yg(a.parent)||{}:{};return a.props.initial!==void 0&&(s.initial=a.props.initial),s}const i={};for(let s=0;s<$T;s++){const r=hf[s],c=a.props[r];(Dl(c)||c===!1)&&(i[r]=c)}return i}const FT=[...df].reverse(),WT=df.length;function IT(a){return i=>Promise.all(i.map(({animation:s,options:r})=>JT(a,s,r)))}function t3(a){let i=IT(a),s=n0(),r=!0;const c=m=>(h,v)=>{var S;const b=Ol(a,v,m==="exit"?(S=a.presenceContext)==null?void 0:S.custom:void 0);if(b){const{transition:E,transitionEnd:M,...V}=b;h={...h,...V,...M}}return h};function d(m){i=m(a)}function f(m){const{props:h}=a,v=yg(a.parent)||{},b=[],S=new Set;let E={},M=1/0;for(let _=0;_<WT;_++){const C=FT[_],q=s[C],L=h[C]!==void 0?h[C]:v[C],P=Dl(L),Y=C===m?q.isActive:null;Y===!1&&(M=_);let $=L===v[C]&&L!==h[C]&&P;if($&&r&&a.manuallyAnimateOnMount&&($=!1),q.protectedKeys={...E},!q.isActive&&Y===null||!L&&!q.prevProp||jr(L)||typeof L=="boolean")continue;const at=e3(q.prevProp,L);let F=at||C===m&&q.isActive&&!$&&P||_>M&&P,rt=!1;const vt=Array.isArray(L)?L:[L];let Yt=vt.reduce(c(C),{});Y===!1&&(Yt={});const{prevResolvedValues:Gt={}}=q,We={...Gt,...Yt},He=K=>{F=!0,S.has(K)&&(rt=!0,S.delete(K)),q.needsAnimating[K]=!0;const W=a.getValue(K);W&&(W.liveStyle=!1)};for(const K in We){const W=Yt[K],yt=Gt[K];if(E.hasOwnProperty(K))continue;let A=!1;Ec(W)&&Ec(yt)?A=!pg(W,yt):A=W!==yt,A?W!=null?He(K):S.add(K):W!==void 0&&S.has(K)?He(K):q.protectedKeys[K]=!0}q.prevProp=L,q.prevResolvedValues=Yt,q.isActive&&(E={...E,...Yt}),r&&a.blockInitialAnimation&&(F=!1),F&&(!($&&at)||rt)&&b.push(...vt.map(K=>({animation:K,options:{type:C}})))}if(S.size){const _={};if(typeof h.initial!="boolean"){const C=Ol(a,Array.isArray(h.initial)?h.initial[0]:h.initial);C&&C.transition&&(_.transition=C.transition)}S.forEach(C=>{const q=a.getBaseTarget(C),L=a.getValue(C);L&&(L.liveStyle=!0),_[C]=q??null}),b.push({animation:_})}let V=!!b.length;return r&&(h.initial===!1||h.initial===h.animate)&&!a.manuallyAnimateOnMount&&(V=!1),r=!1,V?i(b):Promise.resolve()}function p(m,h){var b;if(s[m].isActive===h)return Promise.resolve();(b=a.variantChildren)==null||b.forEach(S=>{var E;return(E=S.animationState)==null?void 0:E.setActive(m,h)}),s[m].isActive=h;const v=f(m);for(const S in s)s[S].protectedKeys={};return v}return{animateChanges:f,setActive:p,setAnimateFunction:d,getState:()=>s,reset:()=>{s=n0(),r=!0}}}function e3(a,i){return typeof i=="string"?i!==a:Array.isArray(i)?!pg(i,a):!1}function ma(a=!1){return{isActive:a,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function n0(){return{animate:ma(!0),whileInView:ma(),whileHover:ma(),whileTap:ma(),whileDrag:ma(),whileFocus:ma(),exit:ma()}}class Jn{constructor(i){this.isMounted=!1,this.node=i}update(){}}class n3 extends Jn{constructor(i){super(i),i.animationState||(i.animationState=t3(i))}updateAnimationControlsSubscription(){const{animate:i}=this.node.getProps();jr(i)&&(this.unmountControls=i.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:i}=this.node.getProps(),{animate:s}=this.node.prevProps||{};i!==s&&this.updateAnimationControlsSubscription()}unmount(){var i;this.node.animationState.reset(),(i=this.unmountControls)==null||i.call(this)}}let a3=0;class i3 extends Jn{constructor(){super(...arguments),this.id=a3++}update(){if(!this.node.presenceContext)return;const{isPresent:i,onExitComplete:s}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||i===r)return;const c=this.node.animationState.setActive("exit",!i);s&&!i&&c.then(()=>{s(this.id)})}mount(){const{register:i,onExitComplete:s}=this.node.presenceContext||{};s&&s(this.id),i&&(this.unmount=i(this.id))}unmount(){}}const l3={animation:{Feature:n3},exit:{Feature:i3}};function zl(a,i,s,r={passive:!0}){return a.addEventListener(i,s,r),()=>a.removeEventListener(i,s)}function Hl(a){return{point:{x:a.pageX,y:a.pageY}}}const s3=a=>i=>cf(i)&&a(i,Hl(i));function Al(a,i,s,r){return zl(a,i,s3(s),r)}function gg({top:a,left:i,right:s,bottom:r}){return{x:{min:i,max:s},y:{min:a,max:r}}}function r3({x:a,y:i}){return{top:i.min,right:a.max,bottom:i.max,left:a.min}}function o3(a,i){if(!i)return a;const s=i({x:a.left,y:a.top}),r=i({x:a.right,y:a.bottom});return{top:s.y,left:s.x,bottom:r.y,right:r.x}}const vg=1e-4,u3=1-vg,c3=1+vg,xg=.01,f3=0-xg,d3=0+xg;function re(a){return a.max-a.min}function h3(a,i,s){return Math.abs(a-i)<=s}function a0(a,i,s,r=.5){a.origin=r,a.originPoint=Rt(i.min,i.max,a.origin),a.scale=re(s)/re(i),a.translate=Rt(s.min,s.max,a.origin)-a.originPoint,(a.scale>=u3&&a.scale<=c3||isNaN(a.scale))&&(a.scale=1),(a.translate>=f3&&a.translate<=d3||isNaN(a.translate))&&(a.translate=0)}function jl(a,i,s,r){a0(a.x,i.x,s.x,r?r.originX:void 0),a0(a.y,i.y,s.y,r?r.originY:void 0)}function i0(a,i,s){a.min=s.min+i.min,a.max=a.min+re(i)}function m3(a,i,s){i0(a.x,i.x,s.x),i0(a.y,i.y,s.y)}function l0(a,i,s){a.min=i.min-s.min,a.max=a.min+re(i)}function El(a,i,s){l0(a.x,i.x,s.x),l0(a.y,i.y,s.y)}const s0=()=>({translate:0,scale:1,origin:0,originPoint:0}),oi=()=>({x:s0(),y:s0()}),r0=()=>({min:0,max:0}),Bt=()=>({x:r0(),y:r0()});function Ve(a){return[a("x"),a("y")]}function ic(a){return a===void 0||a===1}function Mc({scale:a,scaleX:i,scaleY:s}){return!ic(a)||!ic(i)||!ic(s)}function ya(a){return Mc(a)||bg(a)||a.z||a.rotate||a.rotateX||a.rotateY||a.skewX||a.skewY}function bg(a){return o0(a.x)||o0(a.y)}function o0(a){return a&&a!=="0%"}function vr(a,i,s){const r=a-s,c=i*r;return s+c}function u0(a,i,s,r,c){return c!==void 0&&(a=vr(a,c,r)),vr(a,s,r)+i}function Rc(a,i=0,s=1,r,c){a.min=u0(a.min,i,s,r,c),a.max=u0(a.max,i,s,r,c)}function Sg(a,{x:i,y:s}){Rc(a.x,i.translate,i.scale,i.originPoint),Rc(a.y,s.translate,s.scale,s.originPoint)}const c0=.999999999999,f0=1.0000000000001;function p3(a,i,s,r=!1){const c=s.length;if(!c)return;i.x=i.y=1;let d,f;for(let p=0;p<c;p++){d=s[p],f=d.projectionDelta;const{visualElement:m}=d.options;m&&m.props.style&&m.props.style.display==="contents"||(r&&d.options.layoutScroll&&d.scroll&&d!==d.root&&ci(a,{x:-d.scroll.offset.x,y:-d.scroll.offset.y}),f&&(i.x*=f.x.scale,i.y*=f.y.scale,Sg(a,f)),r&&ya(d.latestValues)&&ci(a,d.latestValues))}i.x<f0&&i.x>c0&&(i.x=1),i.y<f0&&i.y>c0&&(i.y=1)}function ui(a,i){a.min=a.min+i,a.max=a.max+i}function d0(a,i,s,r,c=.5){const d=Rt(a.min,a.max,c);Rc(a,i,s,d,r)}function ci(a,i){d0(a.x,i.x,i.scaleX,i.scale,i.originX),d0(a.y,i.y,i.scaleY,i.scale,i.originY)}function Tg(a,i){return gg(o3(a.getBoundingClientRect(),i))}function y3(a,i,s){const r=Tg(a,s),{scroll:c}=i;return c&&(ui(r.x,c.offset.x),ui(r.y,c.offset.y)),r}const Ag=({current:a})=>a?a.ownerDocument.defaultView:null,h0=(a,i)=>Math.abs(a-i);function g3(a,i){const s=h0(a.x,i.x),r=h0(a.y,i.y);return Math.sqrt(s**2+r**2)}class jg{constructor(i,s,{transformPagePoint:r,contextWindow:c,dragSnapToOrigin:d=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const b=sc(this.lastMoveEventInfo,this.history),S=this.startEvent!==null,E=g3(b.offset,{x:0,y:0})>=3;if(!S&&!E)return;const{point:M}=b,{timestamp:V}=te;this.history.push({...M,timestamp:V});const{onStart:_,onMove:C}=this.handlers;S||(_&&_(this.lastMoveEvent,b),this.startEvent=this.lastMoveEvent),C&&C(this.lastMoveEvent,b)},this.handlePointerMove=(b,S)=>{this.lastMoveEvent=b,this.lastMoveEventInfo=lc(S,this.transformPagePoint),Ct.update(this.updatePoint,!0)},this.handlePointerUp=(b,S)=>{this.end();const{onEnd:E,onSessionEnd:M,resumeAnimation:V}=this.handlers;if(this.dragSnapToOrigin&&V&&V(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const _=sc(b.type==="pointercancel"?this.lastMoveEventInfo:lc(S,this.transformPagePoint),this.history);this.startEvent&&E&&E(b,_),M&&M(b,_)},!cf(i))return;this.dragSnapToOrigin=d,this.handlers=s,this.transformPagePoint=r,this.contextWindow=c||window;const f=Hl(i),p=lc(f,this.transformPagePoint),{point:m}=p,{timestamp:h}=te;this.history=[{...m,timestamp:h}];const{onSessionStart:v}=s;v&&v(i,sc(p,this.history)),this.removeListeners=kl(Al(this.contextWindow,"pointermove",this.handlePointerMove),Al(this.contextWindow,"pointerup",this.handlePointerUp),Al(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(i){this.handlers=i}end(){this.removeListeners&&this.removeListeners(),Zn(this.updatePoint)}}function lc(a,i){return i?{point:i(a.point)}:a}function m0(a,i){return{x:a.x-i.x,y:a.y-i.y}}function sc({point:a},i){return{point:a,delta:m0(a,Eg(i)),offset:m0(a,v3(i)),velocity:x3(i,.1)}}function v3(a){return a[0]}function Eg(a){return a[a.length-1]}function x3(a,i){if(a.length<2)return{x:0,y:0};let s=a.length-1,r=null;const c=Eg(a);for(;s>=0&&(r=a[s],!(c.timestamp-r.timestamp>Qe(i)));)s--;if(!r)return{x:0,y:0};const d=Pe(c.timestamp-r.timestamp);if(d===0)return{x:0,y:0};const f={x:(c.x-r.x)/d,y:(c.y-r.y)/d};return f.x===1/0&&(f.x=0),f.y===1/0&&(f.y=0),f}function b3(a,{min:i,max:s},r){return i!==void 0&&a<i?a=r?Rt(i,a,r.min):Math.max(a,i):s!==void 0&&a>s&&(a=r?Rt(s,a,r.max):Math.min(a,s)),a}function p0(a,i,s){return{min:i!==void 0?a.min+i:void 0,max:s!==void 0?a.max+s-(a.max-a.min):void 0}}function S3(a,{top:i,left:s,bottom:r,right:c}){return{x:p0(a.x,s,c),y:p0(a.y,i,r)}}function y0(a,i){let s=i.min-a.min,r=i.max-a.max;return i.max-i.min<a.max-a.min&&([s,r]=[r,s]),{min:s,max:r}}function T3(a,i){return{x:y0(a.x,i.x),y:y0(a.y,i.y)}}function A3(a,i){let s=.5;const r=re(a),c=re(i);return c>r?s=Nl(i.min,i.max-r,a.min):r>c&&(s=Nl(a.min,a.max-c,i.min)),gn(0,1,s)}function j3(a,i){const s={};return i.min!==void 0&&(s.min=i.min-a.min),i.max!==void 0&&(s.max=i.max-a.min),s}const Dc=.35;function E3(a=Dc){return a===!1?a=0:a===!0&&(a=Dc),{x:g0(a,"left","right"),y:g0(a,"top","bottom")}}function g0(a,i,s){return{min:v0(a,i),max:v0(a,s)}}function v0(a,i){return typeof a=="number"?a:a[i]||0}const w3=new WeakMap;class N3{constructor(i){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=Bt(),this.visualElement=i}start(i,{snapToCursor:s=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const c=v=>{const{dragSnapToOrigin:b}=this.getProps();b?this.pauseAnimation():this.stopAnimation(),s&&this.snapToCursor(Hl(v).point)},d=(v,b)=>{const{drag:S,dragPropagation:E,onDragStart:M}=this.getProps();if(S&&!E&&(this.openDragLock&&this.openDragLock(),this.openDragLock=HS(S),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Ve(_=>{let C=this.getAxisMotionValue(_).get()||0;if(Je.test(C)){const{projection:q}=this.visualElement;if(q&&q.layout){const L=q.layout.layoutBox[_];L&&(C=re(L)*(parseFloat(C)/100))}}this.originPoint[_]=C}),M&&Ct.postRender(()=>M(v,b)),wc(this.visualElement,"transform");const{animationState:V}=this.visualElement;V&&V.setActive("whileDrag",!0)},f=(v,b)=>{const{dragPropagation:S,dragDirectionLock:E,onDirectionLock:M,onDrag:V}=this.getProps();if(!S&&!this.openDragLock)return;const{offset:_}=b;if(E&&this.currentDirection===null){this.currentDirection=M3(_),this.currentDirection!==null&&M&&M(this.currentDirection);return}this.updateAxis("x",b.point,_),this.updateAxis("y",b.point,_),this.visualElement.render(),V&&V(v,b)},p=(v,b)=>this.stop(v,b),m=()=>Ve(v=>{var b;return this.getAnimationState(v)==="paused"&&((b=this.getAxisMotionValue(v).animation)==null?void 0:b.play())}),{dragSnapToOrigin:h}=this.getProps();this.panSession=new jg(i,{onSessionStart:c,onStart:d,onMove:f,onSessionEnd:p,resumeAnimation:m},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:h,contextWindow:Ag(this.visualElement)})}stop(i,s){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:c}=s;this.startAnimation(c);const{onDragEnd:d}=this.getProps();d&&Ct.postRender(()=>d(i,s))}cancel(){this.isDragging=!1;const{projection:i,animationState:s}=this.visualElement;i&&(i.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),s&&s.setActive("whileDrag",!1)}updateAxis(i,s,r){const{drag:c}=this.getProps();if(!r||!sr(i,c,this.currentDirection))return;const d=this.getAxisMotionValue(i);let f=this.originPoint[i]+r[i];this.constraints&&this.constraints[i]&&(f=b3(f,this.constraints[i],this.elastic[i])),d.set(f)}resolveConstraints(){var d;const{dragConstraints:i,dragElastic:s}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(d=this.visualElement.projection)==null?void 0:d.layout,c=this.constraints;i&&ri(i)?this.constraints||(this.constraints=this.resolveRefConstraints()):i&&r?this.constraints=S3(r.layoutBox,i):this.constraints=!1,this.elastic=E3(s),c!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&Ve(f=>{this.constraints!==!1&&this.getAxisMotionValue(f)&&(this.constraints[f]=j3(r.layoutBox[f],this.constraints[f]))})}resolveRefConstraints(){const{dragConstraints:i,onMeasureDragConstraints:s}=this.getProps();if(!i||!ri(i))return!1;const r=i.current,{projection:c}=this.visualElement;if(!c||!c.layout)return!1;const d=y3(r,c.root,this.visualElement.getTransformPagePoint());let f=T3(c.layout.layoutBox,d);if(s){const p=s(r3(f));this.hasMutatedConstraints=!!p,p&&(f=gg(p))}return f}startAnimation(i){const{drag:s,dragMomentum:r,dragElastic:c,dragTransition:d,dragSnapToOrigin:f,onDragTransitionEnd:p}=this.getProps(),m=this.constraints||{},h=Ve(v=>{if(!sr(v,s,this.currentDirection))return;let b=m&&m[v]||{};f&&(b={min:0,max:0});const S=c?200:1e6,E=c?40:1e7,M={type:"inertia",velocity:r?i[v]:0,bounceStiffness:S,bounceDamping:E,timeConstant:750,restDelta:1,restSpeed:10,...d,...b};return this.startAxisValueAnimation(v,M)});return Promise.all(h).then(p)}startAxisValueAnimation(i,s){const r=this.getAxisMotionValue(i);return wc(this.visualElement,i),r.start(bf(i,r,0,s,this.visualElement,!1))}stopAnimation(){Ve(i=>this.getAxisMotionValue(i).stop())}pauseAnimation(){Ve(i=>{var s;return(s=this.getAxisMotionValue(i).animation)==null?void 0:s.pause()})}getAnimationState(i){var s;return(s=this.getAxisMotionValue(i).animation)==null?void 0:s.state}getAxisMotionValue(i){const s=`_drag${i.toUpperCase()}`,r=this.visualElement.getProps(),c=r[s];return c||this.visualElement.getValue(i,(r.initial?r.initial[i]:void 0)||0)}snapToCursor(i){Ve(s=>{const{drag:r}=this.getProps();if(!sr(s,r,this.currentDirection))return;const{projection:c}=this.visualElement,d=this.getAxisMotionValue(s);if(c&&c.layout){const{min:f,max:p}=c.layout.layoutBox[s];d.set(i[s]-Rt(f,p,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:i,dragConstraints:s}=this.getProps(),{projection:r}=this.visualElement;if(!ri(s)||!r||!this.constraints)return;this.stopAnimation();const c={x:0,y:0};Ve(f=>{const p=this.getAxisMotionValue(f);if(p&&this.constraints!==!1){const m=p.get();c[f]=A3({min:m,max:m},this.constraints[f])}});const{transformTemplate:d}=this.visualElement.getProps();this.visualElement.current.style.transform=d?d({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),Ve(f=>{if(!sr(f,i,null))return;const p=this.getAxisMotionValue(f),{min:m,max:h}=this.constraints[f];p.set(Rt(m,h,c[f]))})}addListeners(){if(!this.visualElement.current)return;w3.set(this.visualElement,this);const i=this.visualElement.current,s=Al(i,"pointerdown",m=>{const{drag:h,dragListener:v=!0}=this.getProps();h&&v&&this.start(m)}),r=()=>{const{dragConstraints:m}=this.getProps();ri(m)&&m.current&&(this.constraints=this.resolveRefConstraints())},{projection:c}=this.visualElement,d=c.addEventListener("measure",r);c&&!c.layout&&(c.root&&c.root.updateScroll(),c.updateLayout()),Ct.read(r);const f=zl(window,"resize",()=>this.scalePositionWithinConstraints()),p=c.addEventListener("didUpdate",({delta:m,hasLayoutChanged:h})=>{this.isDragging&&h&&(Ve(v=>{const b=this.getAxisMotionValue(v);b&&(this.originPoint[v]+=m[v].translate,b.set(b.get()+m[v].translate))}),this.visualElement.render())});return()=>{f(),s(),d(),p&&p()}}getProps(){const i=this.visualElement.getProps(),{drag:s=!1,dragDirectionLock:r=!1,dragPropagation:c=!1,dragConstraints:d=!1,dragElastic:f=Dc,dragMomentum:p=!0}=i;return{...i,drag:s,dragDirectionLock:r,dragPropagation:c,dragConstraints:d,dragElastic:f,dragMomentum:p}}}function sr(a,i,s){return(i===!0||i===a)&&(s===null||s===a)}function M3(a,i=10){let s=null;return Math.abs(a.y)>i?s="y":Math.abs(a.x)>i&&(s="x"),s}class R3 extends Jn{constructor(i){super(i),this.removeGroupControls=_e,this.removeListeners=_e,this.controls=new N3(i)}mount(){const{dragControls:i}=this.node.getProps();i&&(this.removeGroupControls=i.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||_e}unmount(){this.removeGroupControls(),this.removeListeners()}}const x0=a=>(i,s)=>{a&&Ct.postRender(()=>a(i,s))};class D3 extends Jn{constructor(){super(...arguments),this.removePointerDownListener=_e}onPointerDown(i){this.session=new jg(i,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Ag(this.node)})}createPanHandlers(){const{onPanSessionStart:i,onPanStart:s,onPan:r,onPanEnd:c}=this.node.getProps();return{onSessionStart:x0(i),onStart:x0(s),onMove:r,onEnd:(d,f)=>{delete this.session,c&&Ct.postRender(()=>c(d,f))}}}mount(){this.removePointerDownListener=Al(this.node.current,"pointerdown",i=>this.onPointerDown(i))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const dr={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function b0(a,i){return i.max===i.min?0:a/(i.max-i.min)*100}const xl={correct:(a,i)=>{if(!i.target)return a;if(typeof a=="string")if(lt.test(a))a=parseFloat(a);else return a;const s=b0(a,i.target.x),r=b0(a,i.target.y);return`${s}% ${r}%`}},C3={correct:(a,{treeScale:i,projectionDelta:s})=>{const r=a,c=Qn.parse(a);if(c.length>5)return r;const d=Qn.createTransformer(a),f=typeof c[0]!="number"?1:0,p=s.x.scale*i.x,m=s.y.scale*i.y;c[0+f]/=p,c[1+f]/=m;const h=Rt(p,m,.5);return typeof c[2+f]=="number"&&(c[2+f]/=h),typeof c[3+f]=="number"&&(c[3+f]/=h),d(c)}};class O3 extends w.Component{componentDidMount(){const{visualElement:i,layoutGroup:s,switchLayoutGroup:r,layoutId:c}=this.props,{projection:d}=i;yT(z3),d&&(s.group&&s.group.add(d),r&&r.register&&c&&r.register(d),d.root.didUpdate(),d.addEventListener("animationComplete",()=>{this.safeToRemove()}),d.setOptions({...d.options,onExitComplete:()=>this.safeToRemove()})),dr.hasEverUpdated=!0}getSnapshotBeforeUpdate(i){const{layoutDependency:s,visualElement:r,drag:c,isPresent:d}=this.props,{projection:f}=r;return f&&(f.isPresent=d,c||i.layoutDependency!==s||s===void 0||i.isPresent!==d?f.willUpdate():this.safeToRemove(),i.isPresent!==d&&(d?f.promote():f.relegate()||Ct.postRender(()=>{const p=f.getStack();(!p||!p.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:i}=this.props.visualElement;i&&(i.root.didUpdate(),uf.postRender(()=>{!i.currentAnimation&&i.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:i,layoutGroup:s,switchLayoutGroup:r}=this.props,{projection:c}=i;c&&(c.scheduleCheckAfterUnmount(),s&&s.group&&s.group.remove(c),r&&r.deregister&&r.deregister(c))}safeToRemove(){const{safeToRemove:i}=this.props;i&&i()}render(){return null}}function wg(a){const[i,s]=Iy(),r=w.useContext(Lc);return g.jsx(O3,{...a,layoutGroup:r,switchLayoutGroup:w.useContext(ig),isPresent:i,safeToRemove:s})}const z3={borderRadius:{...xl,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:xl,borderTopRightRadius:xl,borderBottomLeftRadius:xl,borderBottomRightRadius:xl,boxShadow:C3};function V3(a,i,s){const r=ie(a)?a:fi(a);return r.start(bf("",r,i,s)),r.animation}const _3=(a,i)=>a.depth-i.depth;class U3{constructor(){this.children=[],this.isDirty=!1}add(i){Yc(this.children,i),this.isDirty=!0}remove(i){Gc(this.children,i),this.isDirty=!0}forEach(i){this.isDirty&&this.children.sort(_3),this.isDirty=!1,this.children.forEach(i)}}function k3(a,i){const s=he.now(),r=({timestamp:c})=>{const d=c-s;d>=i&&(Zn(r),a(d-i))};return Ct.setup(r,!0),()=>Zn(r)}const Ng=["TopLeft","TopRight","BottomLeft","BottomRight"],B3=Ng.length,S0=a=>typeof a=="string"?parseFloat(a):a,T0=a=>typeof a=="number"||lt.test(a);function L3(a,i,s,r,c,d){c?(a.opacity=Rt(0,s.opacity??1,H3(r)),a.opacityExit=Rt(i.opacity??1,0,q3(r))):d&&(a.opacity=Rt(i.opacity??1,s.opacity??1,r));for(let f=0;f<B3;f++){const p=`border${Ng[f]}Radius`;let m=A0(i,p),h=A0(s,p);if(m===void 0&&h===void 0)continue;m||(m=0),h||(h=0),m===0||h===0||T0(m)===T0(h)?(a[p]=Math.max(Rt(S0(m),S0(h),r),0),(Je.test(h)||Je.test(m))&&(a[p]+="%")):a[p]=h}(i.rotate||s.rotate)&&(a.rotate=Rt(i.rotate||0,s.rotate||0,r))}function A0(a,i){return a[i]!==void 0?a[i]:a.borderRadius}const H3=Mg(0,.5,xy),q3=Mg(.5,.95,_e);function Mg(a,i,s){return r=>r<a?0:r>i?1:s(Nl(a,i,r))}function j0(a,i){a.min=i.min,a.max=i.max}function ze(a,i){j0(a.x,i.x),j0(a.y,i.y)}function E0(a,i){a.translate=i.translate,a.scale=i.scale,a.originPoint=i.originPoint,a.origin=i.origin}function w0(a,i,s,r,c){return a-=i,a=vr(a,1/s,r),c!==void 0&&(a=vr(a,1/c,r)),a}function Y3(a,i=0,s=1,r=.5,c,d=a,f=a){if(Je.test(i)&&(i=parseFloat(i),i=Rt(f.min,f.max,i/100)-f.min),typeof i!="number")return;let p=Rt(d.min,d.max,r);a===d&&(p-=i),a.min=w0(a.min,i,s,p,c),a.max=w0(a.max,i,s,p,c)}function N0(a,i,[s,r,c],d,f){Y3(a,i[s],i[r],i[c],i.scale,d,f)}const G3=["x","scaleX","originX"],K3=["y","scaleY","originY"];function M0(a,i,s,r){N0(a.x,i,G3,s?s.x:void 0,r?r.x:void 0),N0(a.y,i,K3,s?s.y:void 0,r?r.y:void 0)}function R0(a){return a.translate===0&&a.scale===1}function Rg(a){return R0(a.x)&&R0(a.y)}function D0(a,i){return a.min===i.min&&a.max===i.max}function X3(a,i){return D0(a.x,i.x)&&D0(a.y,i.y)}function C0(a,i){return Math.round(a.min)===Math.round(i.min)&&Math.round(a.max)===Math.round(i.max)}function Dg(a,i){return C0(a.x,i.x)&&C0(a.y,i.y)}function O0(a){return re(a.x)/re(a.y)}function z0(a,i){return a.translate===i.translate&&a.scale===i.scale&&a.originPoint===i.originPoint}class Z3{constructor(){this.members=[]}add(i){Yc(this.members,i),i.scheduleRender()}remove(i){if(Gc(this.members,i),i===this.prevLead&&(this.prevLead=void 0),i===this.lead){const s=this.members[this.members.length-1];s&&this.promote(s)}}relegate(i){const s=this.members.findIndex(c=>i===c);if(s===0)return!1;let r;for(let c=s;c>=0;c--){const d=this.members[c];if(d.isPresent!==!1){r=d;break}}return r?(this.promote(r),!0):!1}promote(i,s){const r=this.lead;if(i!==r&&(this.prevLead=r,this.lead=i,i.show(),r)){r.instance&&r.scheduleRender(),i.scheduleRender(),i.resumeFrom=r,s&&(i.resumeFrom.preserveOpacity=!0),r.snapshot&&(i.snapshot=r.snapshot,i.snapshot.latestValues=r.animationValues||r.latestValues),i.root&&i.root.isUpdating&&(i.isLayoutDirty=!0);const{crossfade:c}=i.options;c===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(i=>{const{options:s,resumingFrom:r}=i;s.onExitComplete&&s.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(i=>{i.instance&&i.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Q3(a,i,s){let r="";const c=a.x.translate/i.x,d=a.y.translate/i.y,f=(s==null?void 0:s.z)||0;if((c||d||f)&&(r=`translate3d(${c}px, ${d}px, ${f}px) `),(i.x!==1||i.y!==1)&&(r+=`scale(${1/i.x}, ${1/i.y}) `),s){const{transformPerspective:h,rotate:v,rotateX:b,rotateY:S,skewX:E,skewY:M}=s;h&&(r=`perspective(${h}px) ${r}`),v&&(r+=`rotate(${v}deg) `),b&&(r+=`rotateX(${b}deg) `),S&&(r+=`rotateY(${S}deg) `),E&&(r+=`skewX(${E}deg) `),M&&(r+=`skewY(${M}deg) `)}const p=a.x.scale*i.x,m=a.y.scale*i.y;return(p!==1||m!==1)&&(r+=`scale(${p}, ${m})`),r||"none"}const rc=["","X","Y","Z"],P3={visibility:"hidden"},J3=1e3;let $3=0;function oc(a,i,s,r){const{latestValues:c}=i;c[a]&&(s[a]=c[a],i.setStaticValue(a,0),r&&(r[a]=0))}function Cg(a){if(a.hasCheckedOptimisedAppear=!0,a.root===a)return;const{visualElement:i}=a.options;if(!i)return;const s=hg(i);if(window.MotionHasOptimisedAnimation(s,"transform")){const{layout:c,layoutId:d}=a.options;window.MotionCancelOptimisedAnimation(s,"transform",Ct,!(c||d))}const{parent:r}=a;r&&!r.hasCheckedOptimisedAppear&&Cg(r)}function Og({attachResizeListener:a,defaultParent:i,measureScroll:s,checkIsScrollRoot:r,resetTransform:c}){return class{constructor(f={},p=i==null?void 0:i()){this.id=$3++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(I3),this.nodes.forEach(iA),this.nodes.forEach(lA),this.nodes.forEach(tA)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=f,this.root=p?p.root||p:this,this.path=p?[...p.path,p]:[],this.parent=p,this.depth=p?p.depth+1:0;for(let m=0;m<this.path.length;m++)this.path[m].shouldResetTransform=!0;this.root===this&&(this.nodes=new U3)}addEventListener(f,p){return this.eventHandlers.has(f)||this.eventHandlers.set(f,new Zc),this.eventHandlers.get(f).add(p)}notifyListeners(f,...p){const m=this.eventHandlers.get(f);m&&m.notify(...p)}hasListeners(f){return this.eventHandlers.has(f)}mount(f){if(this.instance)return;this.isSVG=Wy(f)&&!ZS(f),this.instance=f;const{layoutId:p,layout:m,visualElement:h}=this.options;if(h&&!h.current&&h.mount(f),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(m||p)&&(this.isLayoutDirty=!0),a){let v;const b=()=>this.root.updateBlockedByResize=!1;a(f,()=>{this.root.updateBlockedByResize=!0,v&&v(),v=k3(b,250),dr.hasAnimatedSinceResize&&(dr.hasAnimatedSinceResize=!1,this.nodes.forEach(_0))})}p&&this.root.registerSharedNode(p,this),this.options.animate!==!1&&h&&(p||m)&&this.addEventListener("didUpdate",({delta:v,hasLayoutChanged:b,hasRelativeLayoutChanged:S,layout:E})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const M=this.options.transition||h.getDefaultTransition()||cA,{onLayoutAnimationStart:V,onLayoutAnimationComplete:_}=h.getProps(),C=!this.targetLayout||!Dg(this.targetLayout,E),q=!b&&S;if(this.options.layoutRoot||this.resumeFrom||q||b&&(C||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const L={...rf(M,"layout"),onPlay:V,onComplete:_};(h.shouldReduceMotion||this.options.layoutRoot)&&(L.delay=0,L.type=!1),this.startAnimation(L),this.setAnimationOrigin(v,q)}else b||_0(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=E})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const f=this.getStack();f&&f.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),Zn(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(sA),this.animationId++)}getTransformTemplate(){const{visualElement:f}=this.options;return f&&f.getProps().transformTemplate}willUpdate(f=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&Cg(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let v=0;v<this.path.length;v++){const b=this.path[v];b.shouldResetTransform=!0,b.updateScroll("snapshot"),b.options.layoutRoot&&b.willUpdate(!1)}const{layoutId:p,layout:m}=this.options;if(p===void 0&&!m)return;const h=this.getTransformTemplate();this.prevTransformTemplateValue=h?h(this.latestValues,""):void 0,this.updateSnapshot(),f&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(V0);return}this.isUpdating||this.nodes.forEach(nA),this.isUpdating=!1,this.nodes.forEach(aA),this.nodes.forEach(F3),this.nodes.forEach(W3),this.clearAllSnapshots();const p=he.now();te.delta=gn(0,1e3/60,p-te.timestamp),te.timestamp=p,te.isProcessing=!0,Wu.update.process(te),Wu.preRender.process(te),Wu.render.process(te),te.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,uf.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(eA),this.sharedNodes.forEach(rA)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,Ct.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){Ct.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!re(this.snapshot.measuredBox.x)&&!re(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let m=0;m<this.path.length;m++)this.path[m].updateScroll();const f=this.layout;this.layout=this.measure(!1),this.layoutCorrected=Bt(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:p}=this.options;p&&p.notify("LayoutMeasure",this.layout.layoutBox,f?f.layoutBox:void 0)}updateScroll(f="measure"){let p=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===f&&(p=!1),p&&this.instance){const m=r(this.instance);this.scroll={animationId:this.root.animationId,phase:f,isRoot:m,offset:s(this.instance),wasRoot:this.scroll?this.scroll.isRoot:m}}}resetTransform(){if(!c)return;const f=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,p=this.projectionDelta&&!Rg(this.projectionDelta),m=this.getTransformTemplate(),h=m?m(this.latestValues,""):void 0,v=h!==this.prevTransformTemplateValue;f&&this.instance&&(p||ya(this.latestValues)||v)&&(c(this.instance,h),this.shouldResetTransform=!1,this.scheduleRender())}measure(f=!0){const p=this.measurePageBox();let m=this.removeElementScroll(p);return f&&(m=this.removeTransform(m)),fA(m),{animationId:this.root.animationId,measuredBox:p,layoutBox:m,latestValues:{},source:this.id}}measurePageBox(){var h;const{visualElement:f}=this.options;if(!f)return Bt();const p=f.measureViewportBox();if(!(((h=this.scroll)==null?void 0:h.wasRoot)||this.path.some(dA))){const{scroll:v}=this.root;v&&(ui(p.x,v.offset.x),ui(p.y,v.offset.y))}return p}removeElementScroll(f){var m;const p=Bt();if(ze(p,f),(m=this.scroll)!=null&&m.wasRoot)return p;for(let h=0;h<this.path.length;h++){const v=this.path[h],{scroll:b,options:S}=v;v!==this.root&&b&&S.layoutScroll&&(b.wasRoot&&ze(p,f),ui(p.x,b.offset.x),ui(p.y,b.offset.y))}return p}applyTransform(f,p=!1){const m=Bt();ze(m,f);for(let h=0;h<this.path.length;h++){const v=this.path[h];!p&&v.options.layoutScroll&&v.scroll&&v!==v.root&&ci(m,{x:-v.scroll.offset.x,y:-v.scroll.offset.y}),ya(v.latestValues)&&ci(m,v.latestValues)}return ya(this.latestValues)&&ci(m,this.latestValues),m}removeTransform(f){const p=Bt();ze(p,f);for(let m=0;m<this.path.length;m++){const h=this.path[m];if(!h.instance||!ya(h.latestValues))continue;Mc(h.latestValues)&&h.updateSnapshot();const v=Bt(),b=h.measurePageBox();ze(v,b),M0(p,h.latestValues,h.snapshot?h.snapshot.layoutBox:void 0,v)}return ya(this.latestValues)&&M0(p,this.latestValues),p}setTargetDelta(f){this.targetDelta=f,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(f){this.options={...this.options,...f,crossfade:f.crossfade!==void 0?f.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==te.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(f=!1){var S;const p=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=p.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=p.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=p.isSharedProjectionDirty);const m=!!this.resumingFrom||this!==p;if(!(f||m&&this.isSharedProjectionDirty||this.isProjectionDirty||(S=this.parent)!=null&&S.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:v,layoutId:b}=this.options;if(!(!this.layout||!(v||b))){if(this.resolvedRelativeTargetAt=te.timestamp,!this.targetDelta&&!this.relativeTarget){const E=this.getClosestProjectingParent();E&&E.layout&&this.animationProgress!==1?(this.relativeParent=E,this.forceRelativeParentToResolveTarget(),this.relativeTarget=Bt(),this.relativeTargetOrigin=Bt(),El(this.relativeTargetOrigin,this.layout.layoutBox,E.layout.layoutBox),ze(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=Bt(),this.targetWithTransforms=Bt()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),m3(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):ze(this.target,this.layout.layoutBox),Sg(this.target,this.targetDelta)):ze(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const E=this.getClosestProjectingParent();E&&!!E.resumingFrom==!!this.resumingFrom&&!E.options.layoutScroll&&E.target&&this.animationProgress!==1?(this.relativeParent=E,this.forceRelativeParentToResolveTarget(),this.relativeTarget=Bt(),this.relativeTargetOrigin=Bt(),El(this.relativeTargetOrigin,this.target,E.target),ze(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||Mc(this.parent.latestValues)||bg(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var M;const f=this.getLead(),p=!!this.resumingFrom||this!==f;let m=!0;if((this.isProjectionDirty||(M=this.parent)!=null&&M.isProjectionDirty)&&(m=!1),p&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(m=!1),this.resolvedRelativeTargetAt===te.timestamp&&(m=!1),m)return;const{layout:h,layoutId:v}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(h||v))return;ze(this.layoutCorrected,this.layout.layoutBox);const b=this.treeScale.x,S=this.treeScale.y;p3(this.layoutCorrected,this.treeScale,this.path,p),f.layout&&!f.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(f.target=f.layout.layoutBox,f.targetWithTransforms=Bt());const{target:E}=f;if(!E){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(E0(this.prevProjectionDelta.x,this.projectionDelta.x),E0(this.prevProjectionDelta.y,this.projectionDelta.y)),jl(this.projectionDelta,this.layoutCorrected,E,this.latestValues),(this.treeScale.x!==b||this.treeScale.y!==S||!z0(this.projectionDelta.x,this.prevProjectionDelta.x)||!z0(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",E))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(f=!0){var p;if((p=this.options.visualElement)==null||p.scheduleRender(),f){const m=this.getStack();m&&m.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=oi(),this.projectionDelta=oi(),this.projectionDeltaWithTransform=oi()}setAnimationOrigin(f,p=!1){const m=this.snapshot,h=m?m.latestValues:{},v={...this.latestValues},b=oi();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!p;const S=Bt(),E=m?m.source:void 0,M=this.layout?this.layout.source:void 0,V=E!==M,_=this.getStack(),C=!_||_.members.length<=1,q=!!(V&&!C&&this.options.crossfade===!0&&!this.path.some(uA));this.animationProgress=0;let L;this.mixTargetDelta=P=>{const Y=P/1e3;U0(b.x,f.x,Y),U0(b.y,f.y,Y),this.setTargetDelta(b),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(El(S,this.layout.layoutBox,this.relativeParent.layout.layoutBox),oA(this.relativeTarget,this.relativeTargetOrigin,S,Y),L&&X3(this.relativeTarget,L)&&(this.isProjectionDirty=!1),L||(L=Bt()),ze(L,this.relativeTarget)),V&&(this.animationValues=v,L3(v,h,this.latestValues,Y,q,C)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=Y},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(f){var p,m,h;this.notifyListeners("animationStart"),(p=this.currentAnimation)==null||p.stop(),(h=(m=this.resumingFrom)==null?void 0:m.currentAnimation)==null||h.stop(),this.pendingAnimation&&(Zn(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=Ct.update(()=>{dr.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=fi(0)),this.currentAnimation=V3(this.motionValue,[0,1e3],{...f,velocity:0,isSync:!0,onUpdate:v=>{this.mixTargetDelta(v),f.onUpdate&&f.onUpdate(v)},onStop:()=>{},onComplete:()=>{f.onComplete&&f.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const f=this.getStack();f&&f.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(J3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const f=this.getLead();let{targetWithTransforms:p,target:m,layout:h,latestValues:v}=f;if(!(!p||!m||!h)){if(this!==f&&this.layout&&h&&zg(this.options.animationType,this.layout.layoutBox,h.layoutBox)){m=this.target||Bt();const b=re(this.layout.layoutBox.x);m.x.min=f.target.x.min,m.x.max=m.x.min+b;const S=re(this.layout.layoutBox.y);m.y.min=f.target.y.min,m.y.max=m.y.min+S}ze(p,m),ci(p,v),jl(this.projectionDeltaWithTransform,this.layoutCorrected,p,v)}}registerSharedNode(f,p){this.sharedNodes.has(f)||this.sharedNodes.set(f,new Z3),this.sharedNodes.get(f).add(p);const h=p.options.initialPromotionConfig;p.promote({transition:h?h.transition:void 0,preserveFollowOpacity:h&&h.shouldPreserveFollowOpacity?h.shouldPreserveFollowOpacity(p):void 0})}isLead(){const f=this.getStack();return f?f.lead===this:!0}getLead(){var p;const{layoutId:f}=this.options;return f?((p=this.getStack())==null?void 0:p.lead)||this:this}getPrevLead(){var p;const{layoutId:f}=this.options;return f?(p=this.getStack())==null?void 0:p.prevLead:void 0}getStack(){const{layoutId:f}=this.options;if(f)return this.root.sharedNodes.get(f)}promote({needsReset:f,transition:p,preserveFollowOpacity:m}={}){const h=this.getStack();h&&h.promote(this,m),f&&(this.projectionDelta=void 0,this.needsReset=!0),p&&this.setOptions({transition:p})}relegate(){const f=this.getStack();return f?f.relegate(this):!1}resetSkewAndRotation(){const{visualElement:f}=this.options;if(!f)return;let p=!1;const{latestValues:m}=f;if((m.z||m.rotate||m.rotateX||m.rotateY||m.rotateZ||m.skewX||m.skewY)&&(p=!0),!p)return;const h={};m.z&&oc("z",f,h,this.animationValues);for(let v=0;v<rc.length;v++)oc(`rotate${rc[v]}`,f,h,this.animationValues),oc(`skew${rc[v]}`,f,h,this.animationValues);f.render();for(const v in h)f.setStaticValue(v,h[v]),this.animationValues&&(this.animationValues[v]=h[v]);f.scheduleRender()}getProjectionStyles(f){if(!this.instance||this.isSVG)return;if(!this.isVisible)return P3;const p={visibility:""},m=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,p.opacity="",p.pointerEvents=fr(f==null?void 0:f.pointerEvents)||"",p.transform=m?m(this.latestValues,""):"none",p;const h=this.getLead();if(!this.projectionDelta||!this.layout||!h.target){const E={};return this.options.layoutId&&(E.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,E.pointerEvents=fr(f==null?void 0:f.pointerEvents)||""),this.hasProjected&&!ya(this.latestValues)&&(E.transform=m?m({},""):"none",this.hasProjected=!1),E}const v=h.animationValues||h.latestValues;this.applyTransformsToTarget(),p.transform=Q3(this.projectionDeltaWithTransform,this.treeScale,v),m&&(p.transform=m(v,p.transform));const{x:b,y:S}=this.projectionDelta;p.transformOrigin=`${b.origin*100}% ${S.origin*100}% 0`,h.animationValues?p.opacity=h===this?v.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:v.opacityExit:p.opacity=h===this?v.opacity!==void 0?v.opacity:"":v.opacityExit!==void 0?v.opacityExit:0;for(const E in Cl){if(v[E]===void 0)continue;const{correct:M,applyTo:V,isCSSVariable:_}=Cl[E],C=p.transform==="none"?v[E]:M(v[E],h);if(V){const q=V.length;for(let L=0;L<q;L++)p[V[L]]=C}else _?this.options.visualElement.renderState.vars[E]=C:p[E]=C}return this.options.layoutId&&(p.pointerEvents=h===this?fr(f==null?void 0:f.pointerEvents)||"":"none"),p}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(f=>{var p;return(p=f.currentAnimation)==null?void 0:p.stop()}),this.root.nodes.forEach(V0),this.root.sharedNodes.clear()}}}function F3(a){a.updateLayout()}function W3(a){var s;const i=((s=a.resumeFrom)==null?void 0:s.snapshot)||a.snapshot;if(a.isLead()&&a.layout&&i&&a.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:c}=a.layout,{animationType:d}=a.options,f=i.source!==a.layout.source;d==="size"?Ve(b=>{const S=f?i.measuredBox[b]:i.layoutBox[b],E=re(S);S.min=r[b].min,S.max=S.min+E}):zg(d,i.layoutBox,r)&&Ve(b=>{const S=f?i.measuredBox[b]:i.layoutBox[b],E=re(r[b]);S.max=S.min+E,a.relativeTarget&&!a.currentAnimation&&(a.isProjectionDirty=!0,a.relativeTarget[b].max=a.relativeTarget[b].min+E)});const p=oi();jl(p,r,i.layoutBox);const m=oi();f?jl(m,a.applyTransform(c,!0),i.measuredBox):jl(m,r,i.layoutBox);const h=!Rg(p);let v=!1;if(!a.resumeFrom){const b=a.getClosestProjectingParent();if(b&&!b.resumeFrom){const{snapshot:S,layout:E}=b;if(S&&E){const M=Bt();El(M,i.layoutBox,S.layoutBox);const V=Bt();El(V,r,E.layoutBox),Dg(M,V)||(v=!0),b.options.layoutRoot&&(a.relativeTarget=V,a.relativeTargetOrigin=M,a.relativeParent=b)}}}a.notifyListeners("didUpdate",{layout:r,snapshot:i,delta:m,layoutDelta:p,hasLayoutChanged:h,hasRelativeLayoutChanged:v})}else if(a.isLead()){const{onExitComplete:r}=a.options;r&&r()}a.options.transition=void 0}function I3(a){a.parent&&(a.isProjecting()||(a.isProjectionDirty=a.parent.isProjectionDirty),a.isSharedProjectionDirty||(a.isSharedProjectionDirty=!!(a.isProjectionDirty||a.parent.isProjectionDirty||a.parent.isSharedProjectionDirty)),a.isTransformDirty||(a.isTransformDirty=a.parent.isTransformDirty))}function tA(a){a.isProjectionDirty=a.isSharedProjectionDirty=a.isTransformDirty=!1}function eA(a){a.clearSnapshot()}function V0(a){a.clearMeasurements()}function nA(a){a.isLayoutDirty=!1}function aA(a){const{visualElement:i}=a.options;i&&i.getProps().onBeforeLayoutMeasure&&i.notify("BeforeLayoutMeasure"),a.resetTransform()}function _0(a){a.finishAnimation(),a.targetDelta=a.relativeTarget=a.target=void 0,a.isProjectionDirty=!0}function iA(a){a.resolveTargetDelta()}function lA(a){a.calcProjection()}function sA(a){a.resetSkewAndRotation()}function rA(a){a.removeLeadSnapshot()}function U0(a,i,s){a.translate=Rt(i.translate,0,s),a.scale=Rt(i.scale,1,s),a.origin=i.origin,a.originPoint=i.originPoint}function k0(a,i,s,r){a.min=Rt(i.min,s.min,r),a.max=Rt(i.max,s.max,r)}function oA(a,i,s,r){k0(a.x,i.x,s.x,r),k0(a.y,i.y,s.y,r)}function uA(a){return a.animationValues&&a.animationValues.opacityExit!==void 0}const cA={duration:.45,ease:[.4,0,.1,1]},B0=a=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(a),L0=B0("applewebkit/")&&!B0("chrome/")?Math.round:_e;function H0(a){a.min=L0(a.min),a.max=L0(a.max)}function fA(a){H0(a.x),H0(a.y)}function zg(a,i,s){return a==="position"||a==="preserve-aspect"&&!h3(O0(i),O0(s),.2)}function dA(a){var i;return a!==a.root&&((i=a.scroll)==null?void 0:i.wasRoot)}const hA=Og({attachResizeListener:(a,i)=>zl(a,"resize",i),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),uc={current:void 0},Vg=Og({measureScroll:a=>({x:a.scrollLeft,y:a.scrollTop}),defaultParent:()=>{if(!uc.current){const a=new hA({});a.mount(window),a.setOptions({layoutScroll:!0}),uc.current=a}return uc.current},resetTransform:(a,i)=>{a.style.transform=i!==void 0?i:"none"},checkIsScrollRoot:a=>window.getComputedStyle(a).position==="fixed"}),mA={pan:{Feature:D3},drag:{Feature:R3,ProjectionNode:Vg,MeasureLayout:wg}};function q0(a,i,s){const{props:r}=a;a.animationState&&r.whileHover&&a.animationState.setActive("whileHover",s==="Start");const c="onHover"+s,d=r[c];d&&Ct.postRender(()=>d(i,Hl(i)))}class pA extends Jn{mount(){const{current:i}=this.node;i&&(this.unmount=qS(i,(s,r)=>(q0(this.node,r,"Start"),c=>q0(this.node,c,"End"))))}unmount(){}}class yA extends Jn{constructor(){super(...arguments),this.isActive=!1}onFocus(){let i=!1;try{i=this.node.current.matches(":focus-visible")}catch{i=!0}!i||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=kl(zl(this.node.current,"focus",()=>this.onFocus()),zl(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function Y0(a,i,s){const{props:r}=a;if(a.current instanceof HTMLButtonElement&&a.current.disabled)return;a.animationState&&r.whileTap&&a.animationState.setActive("whileTap",s==="Start");const c="onTap"+(s==="End"?"":s),d=r[c];d&&Ct.postRender(()=>d(i,Hl(i)))}class gA extends Jn{mount(){const{current:i}=this.node;i&&(this.unmount=XS(i,(s,r)=>(Y0(this.node,r,"Start"),(c,{success:d})=>Y0(this.node,c,d?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const Cc=new WeakMap,cc=new WeakMap,vA=a=>{const i=Cc.get(a.target);i&&i(a)},xA=a=>{a.forEach(vA)};function bA({root:a,...i}){const s=a||document;cc.has(s)||cc.set(s,{});const r=cc.get(s),c=JSON.stringify(i);return r[c]||(r[c]=new IntersectionObserver(xA,{root:a,...i})),r[c]}function SA(a,i,s){const r=bA(i);return Cc.set(a,s),r.observe(a),()=>{Cc.delete(a),r.unobserve(a)}}const TA={some:0,all:1};class AA extends Jn{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:i={}}=this.node.getProps(),{root:s,margin:r,amount:c="some",once:d}=i,f={root:s?s.current:void 0,rootMargin:r,threshold:typeof c=="number"?c:TA[c]},p=m=>{const{isIntersecting:h}=m;if(this.isInView===h||(this.isInView=h,d&&!h&&this.hasEnteredView))return;h&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",h);const{onViewportEnter:v,onViewportLeave:b}=this.node.getProps(),S=h?v:b;S&&S(m)};return SA(this.node.current,f,p)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:i,prevProps:s}=this.node;["amount","margin","root"].some(jA(i,s))&&this.startObserver()}unmount(){}}function jA({viewport:a={}},{viewport:i={}}={}){return s=>a[s]!==i[s]}const EA={inView:{Feature:AA},tap:{Feature:gA},focus:{Feature:yA},hover:{Feature:pA}},wA={layout:{ProjectionNode:Vg,MeasureLayout:wg}},Oc={current:null},_g={current:!1};function NA(){if(_g.current=!0,!!qc)if(window.matchMedia){const a=window.matchMedia("(prefers-reduced-motion)"),i=()=>Oc.current=a.matches;a.addListener(i),i()}else Oc.current=!1}const MA=new WeakMap;function RA(a,i,s){for(const r in i){const c=i[r],d=s[r];if(ie(c))a.addValue(r,c);else if(ie(d))a.addValue(r,fi(c,{owner:a}));else if(d!==c)if(a.hasValue(r)){const f=a.getValue(r);f.liveStyle===!0?f.jump(c):f.hasAnimated||f.set(c)}else{const f=a.getStaticValue(r);a.addValue(r,fi(f!==void 0?f:c,{owner:a}))}}for(const r in s)i[r]===void 0&&a.removeValue(r);return i}const G0=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class DA{scrapeMotionValuesFromProps(i,s,r){return{}}constructor({parent:i,props:s,presenceContext:r,reducedMotionConfig:c,blockInitialAnimation:d,visualState:f},p={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=lf,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const S=he.now();this.renderScheduledAt<S&&(this.renderScheduledAt=S,Ct.render(this.render,!1,!0))};const{latestValues:m,renderState:h}=f;this.latestValues=m,this.baseTarget={...m},this.initialValues=s.initial?{...m}:{},this.renderState=h,this.parent=i,this.props=s,this.presenceContext=r,this.depth=i?i.depth+1:0,this.reducedMotionConfig=c,this.options=p,this.blockInitialAnimation=!!d,this.isControllingVariants=Er(s),this.isVariantNode=ng(s),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(i&&i.current);const{willChange:v,...b}=this.scrapeMotionValuesFromProps(s,{},this);for(const S in b){const E=b[S];m[S]!==void 0&&ie(E)&&E.set(m[S],!1)}}mount(i){this.current=i,MA.set(i,this),this.projection&&!this.projection.instance&&this.projection.mount(i),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((s,r)=>this.bindToMotionValue(r,s)),_g.current||NA(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Oc.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),Zn(this.notifyUpdate),Zn(this.render),this.valueSubscriptions.forEach(i=>i()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const i in this.events)this.events[i].clear();for(const i in this.features){const s=this.features[i];s&&(s.unmount(),s.isMounted=!1)}this.current=null}bindToMotionValue(i,s){this.valueSubscriptions.has(i)&&this.valueSubscriptions.get(i)();const r=gi.has(i);r&&this.onBindTransform&&this.onBindTransform();const c=s.on("change",p=>{this.latestValues[i]=p,this.props.onUpdate&&Ct.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),d=s.on("renderRequest",this.scheduleRender);let f;window.MotionCheckAppearSync&&(f=window.MotionCheckAppearSync(this,i,s)),this.valueSubscriptions.set(i,()=>{c(),d(),f&&f(),s.owner&&s.stop()})}sortNodePosition(i){return!this.current||!this.sortInstanceNodePosition||this.type!==i.type?0:this.sortInstanceNodePosition(this.current,i.current)}updateFeatures(){let i="animation";for(i in di){const s=di[i];if(!s)continue;const{isEnabled:r,Feature:c}=s;if(!this.features[i]&&c&&r(this.props)&&(this.features[i]=new c(this)),this.features[i]){const d=this.features[i];d.isMounted?d.update():(d.mount(),d.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):Bt()}getStaticValue(i){return this.latestValues[i]}setStaticValue(i,s){this.latestValues[i]=s}update(i,s){(i.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=i,this.prevPresenceContext=this.presenceContext,this.presenceContext=s;for(let r=0;r<G0.length;r++){const c=G0[r];this.propEventSubscriptions[c]&&(this.propEventSubscriptions[c](),delete this.propEventSubscriptions[c]);const d="on"+c,f=i[d];f&&(this.propEventSubscriptions[c]=this.on(c,f))}this.prevMotionValues=RA(this,this.scrapeMotionValuesFromProps(i,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(i){return this.props.variants?this.props.variants[i]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(i){const s=this.getClosestVariantNode();if(s)return s.variantChildren&&s.variantChildren.add(i),()=>s.variantChildren.delete(i)}addValue(i,s){const r=this.values.get(i);s!==r&&(r&&this.removeValue(i),this.bindToMotionValue(i,s),this.values.set(i,s),this.latestValues[i]=s.get())}removeValue(i){this.values.delete(i);const s=this.valueSubscriptions.get(i);s&&(s(),this.valueSubscriptions.delete(i)),delete this.latestValues[i],this.removeValueFromRenderState(i,this.renderState)}hasValue(i){return this.values.has(i)}getValue(i,s){if(this.props.values&&this.props.values[i])return this.props.values[i];let r=this.values.get(i);return r===void 0&&s!==void 0&&(r=fi(s===null?void 0:s,{owner:this}),this.addValue(i,r)),r}readValue(i,s){let r=this.latestValues[i]!==void 0||!this.current?this.latestValues[i]:this.getBaseTargetFromProps(this.props,i)??this.readValueFromInstance(this.current,i,this.options);return r!=null&&(typeof r=="string"&&(uy(r)||fy(r))?r=parseFloat(r):!PS(r)&&Qn.test(s)&&(r=Zy(i,s)),this.setBaseTarget(i,ie(r)?r.get():r)),ie(r)?r.get():r}setBaseTarget(i,s){this.baseTarget[i]=s}getBaseTarget(i){var d;const{initial:s}=this.props;let r;if(typeof s=="string"||typeof s=="object"){const f=vf(this.props,s,(d=this.presenceContext)==null?void 0:d.custom);f&&(r=f[i])}if(s&&r!==void 0)return r;const c=this.getBaseTargetFromProps(this.props,i);return c!==void 0&&!ie(c)?c:this.initialValues[i]!==void 0&&r===void 0?void 0:this.baseTarget[i]}on(i,s){return this.events[i]||(this.events[i]=new Zc),this.events[i].add(s)}notify(i,...s){this.events[i]&&this.events[i].notify(...s)}}class Ug extends DA{constructor(){super(...arguments),this.KeyframeResolver=kS}sortInstanceNodePosition(i,s){return i.compareDocumentPosition(s)&2?1:-1}getBaseTargetFromProps(i,s){return i.style?i.style[s]:void 0}removeValueFromRenderState(i,{vars:s,style:r}){delete s[i],delete r[i]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:i}=this.props;ie(i)&&(this.childSubscription=i.on("change",s=>{this.current&&(this.current.textContent=`${s}`)}))}}function kg(a,{style:i,vars:s},r,c){Object.assign(a.style,i,c&&c.getProjectionStyles(r));for(const d in s)a.style.setProperty(d,s[d])}function CA(a){return window.getComputedStyle(a)}class OA extends Ug{constructor(){super(...arguments),this.type="html",this.renderInstance=kg}readValueFromInstance(i,s){var r;if(gi.has(s))return(r=this.projection)!=null&&r.isProjecting?xc(s):nS(i,s);{const c=CA(i),d=(Jc(s)?c.getPropertyValue(s):c[s])||0;return typeof d=="string"?d.trim():d}}measureInstanceViewportBox(i,{transformPagePoint:s}){return Tg(i,s)}build(i,s,r){pf(i,s,r.transformTemplate)}scrapeMotionValuesFromProps(i,s,r){return xf(i,s,r)}}const Bg=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function zA(a,i,s,r){kg(a,i,void 0,r);for(const c in i.attrs)a.setAttribute(Bg.has(c)?c:mf(c),i.attrs[c])}class VA extends Ug{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=Bt}getBaseTargetFromProps(i,s){return i[s]}readValueFromInstance(i,s){if(gi.has(s)){const r=Xy(s);return r&&r.default||0}return s=Bg.has(s)?s:mf(s),i.getAttribute(s)}scrapeMotionValuesFromProps(i,s,r){return dg(i,s,r)}build(i,s,r){og(i,s,this.isSVGTag,r.transformTemplate,r.style)}renderInstance(i,s,r,c){zA(i,s,r,c)}mount(i){this.isSVGTag=cg(i.tagName),super.mount(i)}}const _A=(a,i)=>gf(a)?new VA(i):new OA(i,{allowProjection:a!==w.Fragment}),UA=zT({...l3,...EA,...mA,...wA},_A),Z=iT(UA),kA={some:0,all:1};function BA(a,i,{root:s,margin:r,amount:c="some"}={}){const d=Qy(a),f=new WeakMap,p=h=>{h.forEach(v=>{const b=f.get(v.target);if(v.isIntersecting!==!!b)if(v.isIntersecting){const S=i(v.target,v);typeof S=="function"?f.set(v.target,S):m.unobserve(v.target)}else typeof b=="function"&&(b(v),f.delete(v.target))})},m=new IntersectionObserver(p,{root:s,rootMargin:r,threshold:typeof c=="number"?c:kA[c]});return d.forEach(h=>m.observe(h)),()=>m.disconnect()}function LA(a,{root:i,margin:s,amount:r,once:c=!1,initial:d=!1}={}){const[f,p]=w.useState(d);return w.useEffect(()=>{if(!a.current||c&&f)return;const m=()=>(p(!0),c?void 0:()=>p(!1)),h={root:i&&i.current||void 0,margin:s,amount:r};return BA(a.current,m,h)},[i,a,s,c,r]),f}/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const HA=a=>a.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),qA=a=>a.replace(/^([A-Z])|[\s-_]+(\w)/g,(i,s,r)=>r?r.toUpperCase():s.toLowerCase()),K0=a=>{const i=qA(a);return i.charAt(0).toUpperCase()+i.slice(1)},Lg=(...a)=>a.filter((i,s,r)=>!!i&&i.trim()!==""&&r.indexOf(i)===s).join(" ").trim(),YA=a=>{for(const i in a)if(i.startsWith("aria-")||i==="role"||i==="title")return!0};/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var GA={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const KA=w.forwardRef(({color:a="currentColor",size:i=24,strokeWidth:s=2,absoluteStrokeWidth:r,className:c="",children:d,iconNode:f,...p},m)=>w.createElement("svg",{ref:m,...GA,width:i,height:i,stroke:a,strokeWidth:r?Number(s)*24/Number(i):s,className:Lg("lucide",c),...!d&&!YA(p)&&{"aria-hidden":"true"},...p},[...f.map(([h,v])=>w.createElement(h,v)),...Array.isArray(d)?d:[d]]));/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ot=(a,i)=>{const s=w.forwardRef(({className:r,...c},d)=>w.createElement(KA,{ref:d,iconNode:i,className:Lg(`lucide-${HA(K0(a))}`,`lucide-${a}`,r),...c}));return s.displayName=K0(a),s};/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const XA=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]],Hg=Ot("arrow-right",XA);/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ZA=[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]],QA=Ot("arrow-up",ZA);/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const PA=[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]],JA=Ot("award",PA);/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $A=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],qg=Ot("calendar",$A);/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const FA=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]],Yg=Ot("circle-check-big",FA);/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const WA=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]],wr=Ot("clock",WA);/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const IA=[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]],tj=Ot("facebook",IA);/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ej=[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]],nj=Ot("heart",ej);/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const aj=[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]],ij=Ot("instagram",aj);/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lj=[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]],Gg=Ot("mail",lj);/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sj=[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]],xr=Ot("map-pin",sj);/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rj=[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]],oj=Ot("menu",rj);/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uj=[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]],Sf=Ot("phone",uj);/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cj=[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]],Kg=Ot("play",cj);/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fj=[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]],dj=Ot("send",fj);/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hj=[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]],X0=Ot("shield",hj);/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mj=[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]],Tf=Ot("star",mj);/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pj=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]],Xg=Ot("target",pj);/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yj=[["path",{d:"M10 14.66v1.626a2 2 0 0 1-.976 1.696A5 5 0 0 0 7 21.978",key:"1n3hpd"}],["path",{d:"M14 14.66v1.626a2 2 0 0 0 .976 1.696A5 5 0 0 1 17 21.978",key:"rfe1zi"}],["path",{d:"M18 9h1.5a1 1 0 0 0 0-5H18",key:"7xy6bh"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M6 9a6 6 0 0 0 12 0V3a1 1 0 0 0-1-1H7a1 1 0 0 0-1 1z",key:"1mhfuq"}],["path",{d:"M6 9H4.5a1 1 0 0 1 0-5H6",key:"tex48p"}]],gj=Ot("trophy",yj);/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vj=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],Nr=Ot("users",vj);/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xj=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],Zg=Ot("x",xj);/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bj=[["path",{d:"M2.5 17a24.12 24.12 0 0 1 0-10 2 2 0 0 1 1.4-1.4 49.56 49.56 0 0 1 16.2 0A2 2 0 0 1 21.5 7a24.12 24.12 0 0 1 0 10 2 2 0 0 1-1.4 1.4 49.55 49.55 0 0 1-16.2 0A2 2 0 0 1 2.5 17",key:"1q2vi4"}],["path",{d:"m10 15 5-3-5-3z",key:"1jp15x"}]],Sj=Ot("youtube",bj);/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tj=[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]],Af=Ot("zap",Tj),Aj=()=>{const[a,i]=w.useState(!1),[s,r]=w.useState(!1),c=Pn();w.useEffect(()=>{const f=()=>{r(window.scrollY>50)};return window.addEventListener("scroll",f),()=>window.removeEventListener("scroll",f)},[]);const d=[{name:"Domů",path:"/"},{name:"O nás",path:"/about"},{name:"Tréninky",path:"/training"},{name:"Trenéři",path:"/trainers"},{name:"Galerie",path:"/gallery"},{name:"Kontakt",path:"/contact"}];return g.jsxs(Z.nav,{initial:{y:-100},animate:{y:0},className:`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${s?"bg-white/95 backdrop-blur-md shadow-lg":"bg-transparent"}`,children:[g.jsx("div",{className:"container-custom",children:g.jsxs("div",{className:"flex items-center justify-between h-16 lg:h-20",children:[g.jsxs(se,{to:"/",className:"flex items-center space-x-2 group",children:[g.jsxs("div",{className:"relative",children:[g.jsx(Af,{className:`w-8 h-8 transition-colors duration-300 ${s?"text-primary-600":"text-white"} group-hover:text-primary-500`}),g.jsx("div",{className:"absolute inset-0 bg-primary-500 rounded-full blur-lg opacity-20 group-hover:opacity-40 transition-opacity duration-300"})]}),g.jsxs("div",{className:"flex flex-col",children:[g.jsx("span",{className:`font-display font-bold text-xl transition-colors duration-300 ${s?"text-gray-900":"text-white"}`,children:"COBRA RYU"}),g.jsx("span",{className:`text-xs font-medium transition-colors duration-300 ${s?"text-gray-600":"text-gray-300"}`,children:"STRAKONICE"})]})]}),g.jsxs("div",{className:"hidden lg:flex items-center space-x-8",children:[d.map(f=>g.jsxs(se,{to:f.path,className:`relative font-medium transition-colors duration-300 hover:text-primary-500 ${c.pathname===f.path?s?"text-primary-600":"text-primary-400":s?"text-gray-700":"text-white"}`,children:[f.name,c.pathname===f.path&&g.jsx(Z.div,{layoutId:"activeTab",className:"absolute -bottom-1 left-0 right-0 h-0.5 bg-primary-500",initial:!1,transition:{type:"spring",stiffness:500,damping:30}})]},f.path)),g.jsx(se,{to:"/contact",className:"btn-primary ml-4",children:"Přidej se k nám"})]}),g.jsx("button",{onClick:()=>i(!a),className:`lg:hidden p-2 rounded-lg transition-colors duration-300 ${s?"text-gray-700 hover:bg-gray-100":"text-white hover:bg-white/10"}`,children:a?g.jsx(Zg,{className:"w-6 h-6"}):g.jsx(oj,{className:"w-6 h-6"})})]})}),g.jsx(IS,{children:a&&g.jsx(Z.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"lg:hidden bg-white border-t border-gray-200",children:g.jsx("div",{className:"container-custom py-4",children:g.jsxs("div",{className:"flex flex-col space-y-4",children:[d.map(f=>g.jsx(se,{to:f.path,onClick:()=>i(!1),className:`font-medium py-2 transition-colors duration-300 ${c.pathname===f.path?"text-primary-600":"text-gray-700 hover:text-primary-500"}`,children:f.name},f.path)),g.jsx(se,{to:"/contact",onClick:()=>i(!1),className:"btn-primary w-fit mt-4",children:"Přidej se k nám"})]})})})})]})},jj=()=>{const a={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2,delayChildren:.3}}},i={hidden:{y:30,opacity:0},visible:{y:0,opacity:1,transition:{duration:.8,ease:"easeOut"}}},s={animate:{y:[-10,10,-10],transition:{duration:6,repeat:1/0,ease:"easeInOut"}}};return g.jsxs("section",{className:"relative min-h-screen flex items-center justify-center overflow-hidden",children:[g.jsxs("div",{className:"absolute inset-0 z-0",children:[g.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-black/80 via-primary-900/60 to-black/90 z-10"}),g.jsx("img",{src:"https://images.unsplash.com/photo-1544717297-fa95b6ee9643?ixlib=rb-4.0.3&auto=format&fit=crop&w=2069&q=80",alt:"Martial Arts Training",className:"w-full h-full object-cover"})]}),g.jsx(Z.div,{variants:s,animate:"animate",className:"absolute top-20 left-10 w-20 h-20 bg-primary-500/20 rounded-full blur-xl"}),g.jsx(Z.div,{variants:s,animate:"animate",className:"absolute bottom-32 right-16 w-32 h-32 bg-primary-400/10 rounded-full blur-2xl",style:{animationDelay:"2s"}}),g.jsx("div",{className:"relative z-20 container-custom text-center",children:g.jsxs(Z.div,{variants:a,initial:"hidden",animate:"visible",className:"max-w-5xl mx-auto",children:[g.jsxs(Z.div,{variants:i,className:"inline-flex items-center px-4 py-2 bg-primary-500/20 backdrop-blur-sm border border-primary-400/30 rounded-full text-primary-300 text-sm font-medium mb-8",children:[g.jsx(X0,{className:"w-4 h-4 mr-2"}),"Moderní škola bojových umění"]}),g.jsxs(Z.h1,{variants:i,className:"text-5xl md:text-7xl lg:text-8xl font-display font-bold text-white mb-6 leading-tight",children:["COBRA",g.jsx("span",{className:"block text-gradient",children:"RYU"}),g.jsx("span",{className:"block text-2xl md:text-3xl lg:text-4xl font-normal text-gray-300 mt-4",children:"Strakonice"})]}),g.jsxs(Z.p,{variants:i,className:"text-xl md:text-2xl text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed",children:["Objevte sílu, disciplínu a sebevědomí prostřednictvím",g.jsx("span",{className:"text-primary-400 font-semibold",children:" MMA"}),",",g.jsx("span",{className:"text-primary-400 font-semibold",children:" Allkampf-Jitsu"})," a",g.jsx("span",{className:"text-primary-400 font-semibold",children:" Karate"}),". Připojte se k naší komunitě bojovníků."]}),g.jsxs(Z.div,{variants:i,className:"flex flex-col sm:flex-row gap-6 justify-center items-center mb-16",children:[g.jsxs(se,{to:"/contact",className:"group btn-primary text-lg px-10 py-4 flex items-center",children:["Přidej se k nám",g.jsx(Hg,{className:"w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-200"})]}),g.jsxs("button",{className:"group btn-secondary text-lg px-10 py-4 flex items-center",children:[g.jsx(Kg,{className:"w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-200"}),"Sleduj video"]})]}),g.jsxs(Z.div,{variants:i,className:"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto",children:[g.jsxs("div",{className:"glass-effect p-6 text-center group hover:bg-white/20 transition-all duration-300",children:[g.jsx(Xg,{className:"w-8 h-8 text-primary-400 mx-auto mb-4 group-hover:scale-110 transition-transform duration-300"}),g.jsx("h3",{className:"text-white font-semibold text-lg mb-2",children:"Profesionální výuka"}),g.jsx("p",{className:"text-gray-300 text-sm",children:"Zkušení trenéři s mezinárodními certifikáty"})]}),g.jsxs("div",{className:"glass-effect p-6 text-center group hover:bg-white/20 transition-all duration-300",children:[g.jsx(Nr,{className:"w-8 h-8 text-primary-400 mx-auto mb-4 group-hover:scale-110 transition-transform duration-300"}),g.jsx("h3",{className:"text-white font-semibold text-lg mb-2",children:"Všechny věkové kategorie"}),g.jsx("p",{className:"text-gray-300 text-sm",children:"Od dětí po dospělé, každý najde svou cestu"})]}),g.jsxs("div",{className:"glass-effect p-6 text-center group hover:bg-white/20 transition-all duration-300",children:[g.jsx(X0,{className:"w-8 h-8 text-primary-400 mx-auto mb-4 group-hover:scale-110 transition-transform duration-300"}),g.jsx("h3",{className:"text-white font-semibold text-lg mb-2",children:"Moderní vybavení"}),g.jsx("p",{className:"text-gray-300 text-sm",children:"Nejnovější tréninkové pomůcky a bezpečnost"})]})]})]})}),g.jsx(Z.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:1.5,duration:.8},className:"absolute bottom-8 left-1/2 transform -translate-x-1/2",children:g.jsxs("div",{className:"flex flex-col items-center text-white/60",children:[g.jsx("span",{className:"text-sm mb-2",children:"Scroll dolů"}),g.jsx(Z.div,{animate:{y:[0,8,0]},transition:{duration:2,repeat:1/0},className:"w-6 h-10 border-2 border-white/30 rounded-full flex justify-center",children:g.jsx("div",{className:"w-1 h-3 bg-white/60 rounded-full mt-2"})})]})})]})},Qg=()=>{const a=[{icon:Xg,title:"Cílená příprava",description:"Individuální přístup k každému členovi podle jeho cílů a možností"},{icon:nj,title:"Komunita",description:"Vytváříme silnou a podporující komunitu, kde se každý cítí jako doma"},{icon:Af,title:"Moderní metody",description:"Kombinujeme tradiční techniky s nejnovějšími tréninkovými metodami"}],i=["Respekt k sobě i ostatním","Disciplína a vytrvalost","Neustálé zlepšování","Bezpečnost na prvním místě","Týmový duch a fair play","Osobní růst každého člena"],s={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2}}},r={hidden:{y:30,opacity:0},visible:{y:0,opacity:1,transition:{duration:.6,ease:"easeOut"}}};return g.jsx("section",{id:"about",className:"section-padding bg-white",children:g.jsx("div",{className:"container-custom",children:g.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center",children:[g.jsxs(Z.div,{variants:s,initial:"hidden",whileInView:"visible",viewport:{once:!0,margin:"-100px"},children:[g.jsxs(Z.div,{variants:r,className:"mb-8",children:[g.jsx("span",{className:"inline-block px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-medium mb-4",children:"Proč si vybrat COBRA RYU?"}),g.jsxs("h2",{className:"text-4xl lg:text-5xl font-display font-bold text-gray-900 mb-6",children:["Více než jen",g.jsx("span",{className:"text-gradient block",children:"bojové umění"})]}),g.jsx("p",{className:"text-xl text-gray-600 leading-relaxed",children:"COBRA RYU Strakonice není jen místo, kde se učíte bojovat. Je to komunita, kde se formují charaktery, buduje sebevědomí a vytváří přátelství na celý život."})]}),g.jsx(Z.div,{variants:r,className:"space-y-6 mb-8",children:a.map((c,d)=>g.jsxs("div",{className:"flex items-start space-x-4 group",children:[g.jsx("div",{className:"flex-shrink-0 w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center group-hover:bg-primary-500 transition-colors duration-300",children:g.jsx(c.icon,{className:"w-6 h-6 text-primary-600 group-hover:text-white transition-colors duration-300"})}),g.jsxs("div",{children:[g.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:c.title}),g.jsx("p",{className:"text-gray-600",children:c.description})]})]},d))}),g.jsxs(Z.div,{variants:r,children:[g.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Naše hodnoty"}),g.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3",children:i.map((c,d)=>g.jsxs("div",{className:"flex items-center space-x-3",children:[g.jsx(Yg,{className:"w-5 h-5 text-primary-500 flex-shrink-0"}),g.jsx("span",{className:"text-gray-700",children:c})]},d))})]})]}),g.jsxs(Z.div,{initial:{opacity:0,x:30},whileInView:{opacity:1,x:0},viewport:{once:!0},transition:{duration:.8},className:"relative",children:[g.jsxs("div",{className:"relative z-10 rounded-2xl overflow-hidden shadow-2xl",children:[g.jsx("img",{src:"https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80",alt:"Training Session",className:"w-full h-96 object-cover"}),g.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"})]}),g.jsx(Z.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{delay:.4,duration:.6},className:"absolute -bottom-8 -left-8 bg-white rounded-xl shadow-xl p-6 border border-gray-100",children:g.jsxs("div",{className:"flex items-center space-x-4",children:[g.jsx("div",{className:"w-12 h-12 bg-primary-500 rounded-full flex items-center justify-center",children:g.jsx("span",{className:"text-white font-bold text-lg",children:"15+"})}),g.jsxs("div",{children:[g.jsx("p",{className:"text-sm text-gray-600",children:"Let zkušeností"}),g.jsx("p",{className:"font-semibold text-gray-900",children:"v bojových uměních"})]})]})}),g.jsx("div",{className:"absolute -top-4 -right-4 w-24 h-24 bg-primary-500/10 rounded-full blur-xl"}),g.jsx("div",{className:"absolute -bottom-4 -right-4 w-32 h-32 bg-primary-400/5 rounded-full blur-2xl"})]})]})})})},Pg=()=>{const a=[{name:"MMA Začátečníci",description:"Základy smíšených bojových umění pro úplné začátečníky",level:"Začátečník",age:"16+ let",duration:"90 min",schedule:"Po, St 18:00-19:30",price:"1200 Kč/měsíc",features:["Základní techniky","Kondiční příprava","Bezpečnost","Základy grappingu"],color:"from-blue-500 to-blue-600"},{name:"MMA Pokročilí",description:"Pokročilé techniky a sparring pro zkušené bojovníky",level:"Pokročilý",age:"18+ let",duration:"90 min",schedule:"Po, St 19:30-21:00",price:"1400 Kč/měsíc",features:["Pokročilé techniky","Sparring","Turnajová příprava","Individuální koučink"],color:"from-red-500 to-red-600"},{name:"Allkampf-Jitsu",description:"Moderní sebeobrana a praktické bojové techniky",level:"Všechny úrovně",age:"14+ let",duration:"75 min",schedule:"Út, Čt 19:00-20:15",price:"1100 Kč/měsíc",features:["Sebeobrana","Praktické techniky","Mentální příprava","Situační výcvik"],color:"from-green-500 to-green-600"},{name:"Dětské MMA",description:"Bojové umění pro děti - zábavnou a bezpečnou formou",level:"Začátečník",age:"6-14 let",duration:"60 min",schedule:"Út, Čt 17:00-18:00",price:"800 Kč/měsíc",features:["Herní forma","Základní techniky","Disciplína","Sebevědomí"],color:"from-purple-500 to-purple-600"},{name:"Karate Kyokushin",description:"Tradiční karate s důrazem na sílu a vytrvalost",level:"Všechny úrovně",age:"12+ let",duration:"75 min",schedule:"Pá 18:00-19:15",price:"1000 Kč/měsíc",features:["Tradiční techniky","Kata","Kumite","Filozofie"],color:"from-orange-500 to-orange-600"},{name:"Kondiční trénink",description:"Funkční trénink a příprava kondice pro bojovníky",level:"Všechny úrovně",age:"16+ let",duration:"60 min",schedule:"Pá 19:30-20:30",price:"600 Kč/měsíc",features:["Funkční cvičení","Kardio","Síla","Flexibilita"],color:"from-gray-500 to-gray-600"}],i={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},s={hidden:{y:30,opacity:0},visible:{y:0,opacity:1,transition:{duration:.6,ease:"easeOut"}}};return g.jsx("section",{id:"training",className:"section-padding bg-gradient-to-br from-gray-50 to-gray-100",children:g.jsxs("div",{className:"container-custom",children:[g.jsxs(Z.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.6},className:"text-center mb-16",children:[g.jsx("span",{className:"inline-block px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-medium mb-4",children:"Naše programy"}),g.jsxs("h2",{className:"text-4xl lg:text-5xl font-display font-bold text-gray-900 mb-6",children:["Rozvrh",g.jsx("span",{className:"text-gradient block",children:"tréninků"})]}),g.jsx("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Vyberte si z našich specializovaných programů. Každý trénink je navržen tak, aby vás posunul na vyšší úroveň."})]}),g.jsx(Z.div,{variants:i,initial:"hidden",whileInView:"visible",viewport:{once:!0,margin:"-100px"},className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:a.map((r,c)=>g.jsxs(Z.div,{variants:s,className:"group relative bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden",children:[g.jsxs("div",{className:`bg-gradient-to-r ${r.color} p-6 text-white relative overflow-hidden`,children:[g.jsx("div",{className:"absolute top-0 right-0 w-20 h-20 bg-white/10 rounded-full -translate-y-10 translate-x-10"}),g.jsxs("div",{className:"relative z-10",children:[g.jsx("h3",{className:"text-xl font-bold mb-2",children:r.name}),g.jsx("p",{className:"text-white/90 text-sm",children:r.description})]})]}),g.jsxs("div",{className:"p-6",children:[g.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-6",children:[g.jsxs("div",{className:"flex items-center space-x-2",children:[g.jsx(Tf,{className:"w-4 h-4 text-gray-400"}),g.jsx("span",{className:"text-sm text-gray-600",children:r.level})]}),g.jsxs("div",{className:"flex items-center space-x-2",children:[g.jsx(Nr,{className:"w-4 h-4 text-gray-400"}),g.jsx("span",{className:"text-sm text-gray-600",children:r.age})]}),g.jsxs("div",{className:"flex items-center space-x-2",children:[g.jsx(wr,{className:"w-4 h-4 text-gray-400"}),g.jsx("span",{className:"text-sm text-gray-600",children:r.duration})]}),g.jsxs("div",{className:"flex items-center space-x-2",children:[g.jsx(qg,{className:"w-4 h-4 text-gray-400"}),g.jsx("span",{className:"text-sm text-gray-600",children:r.schedule})]})]}),g.jsxs("div",{className:"mb-6",children:[g.jsx("h4",{className:"font-semibold text-gray-900 mb-3",children:"Co se naučíte:"}),g.jsx("ul",{className:"space-y-2",children:r.features.map((d,f)=>g.jsxs("li",{className:"flex items-center space-x-2",children:[g.jsx("div",{className:"w-1.5 h-1.5 bg-primary-500 rounded-full"}),g.jsx("span",{className:"text-sm text-gray-600",children:d})]},f))})]}),g.jsxs("div",{className:"flex items-center justify-between",children:[g.jsx("div",{children:g.jsx("span",{className:"text-2xl font-bold text-gray-900",children:r.price})}),g.jsx(Z.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"btn-outline text-sm px-4 py-2",children:"Přihlásit se"})]})]})]},c))}),g.jsxs(Z.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{delay:.4,duration:.6},className:"text-center mt-16 bg-white rounded-2xl p-8 shadow-lg",children:[g.jsx("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"První trénink zdarma!"}),g.jsx("p",{className:"text-gray-600 mb-6 max-w-2xl mx-auto",children:"Nejste si jisti, který program je pro vás ten pravý? Přijďte si vyzkoušet jakýkoliv trénink zdarma a poznejte naši komunitu."}),g.jsx(Z.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"btn-primary",children:"Rezervovat zkušební trénink"})]})]})})},Jg=()=>{const a=[{name:"Martin Novák",title:"Hlavní trenér MMA",experience:"15 let zkušeností",specialization:["MMA","Kickbox","Grappling","Kondiční příprava"],achievements:["Mistr ČR v MMA (2018, 2019)","Certifikovaný trenér UFC Gym","3x vítěz regionálních turnajů","Trenér roku 2020"],bio:"Bývalý profesionální bojovník s bohatými zkušenostmi z domácích i zahraničních turnajů. Specializuje se na komplexní přípravu bojovníků všech úrovní.",image:"https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",rating:4.9,students:120},{name:"Tomáš Svoboda",title:"Trenér Allkampf-Jitsu",experience:"12 let zkušeností",specialization:["Allkampf-Jitsu","Sebeobrana","Krav Maga","Taktický výcvik"],achievements:["Instruktor Allkampf-Jitsu 3. Dan","Kurzy sebeobrany pro bezpečnostní složky","Certifikát IKMF Krav Maga","Specialista na ženskou sebeobranu"],bio:"Specialista na moderní sebeobranu a praktické bojové techniky. Pracuje s bezpečnostními složkami a vede kurzy sebeobrany.",image:"https://images.unsplash.com/photo-1566753323558-f4e0952af115?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",rating:4.8,students:85},{name:"Jana Kratochvílová",title:"Trenérka dětských skupin",experience:"8 let zkušeností",specialization:["Dětské MMA","Karate","Gymnastika","Sportovní psychologie"],achievements:["Trenérka mládeže 1. třídy","Kurzy dětské sportovní psychologie","Organizátorka dětských turnajů","Specialistka na práci s dětmi s ADHD"],bio:"Odbornice na práci s dětmi a mládeží. Vytváří pozitivní a bezpečné prostředí, kde se děti učí nejen bojové techniky, ale i životní hodnoty.",image:"https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",rating:5,students:95},{name:"Pavel Dvořák",title:"Trenér Karate Kyokushin",experience:"20 let zkušeností",specialization:["Karate Kyokushin","Kata","Kumite","Meditace"],achievements:["Mistr Karate 4. Dan","Mezinárodní rozhodčí IKO","Účastník MS v Karate (3x)","Držitel černého pásu od 1998"],bio:"Veterán karate s dvacetiletou praxí. Kombinuje tradiční přístup s moderními metodami výuky. Důraz klade na disciplínu a duchovní rozvoj.",image:"https://images.unsplash.com/photo-1552374196-1ab2a1c593e8?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",rating:4.9,students:65}],i={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2}}},s={hidden:{y:30,opacity:0},visible:{y:0,opacity:1,transition:{duration:.6,ease:"easeOut"}}};return g.jsx("section",{id:"trainers",className:"section-padding bg-white",children:g.jsxs("div",{className:"container-custom",children:[g.jsxs(Z.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.6},className:"text-center mb-16",children:[g.jsx("span",{className:"inline-block px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-medium mb-4",children:"Náš tým"}),g.jsxs("h2",{className:"text-4xl lg:text-5xl font-display font-bold text-gray-900 mb-6",children:["Poznejte naše",g.jsx("span",{className:"text-gradient block",children:"trenéry"})]}),g.jsx("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Náš tým tvoří zkušení a kvalifikovaní trenéři, kteří vás provedou světem bojových umění s trpělivostí, odborností a vášní."})]}),g.jsx(Z.div,{variants:i,initial:"hidden",whileInView:"visible",viewport:{once:!0,margin:"-100px"},className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:a.map((r,c)=>g.jsxs(Z.div,{variants:s,className:"group bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100",children:[g.jsxs("div",{className:"relative h-64 overflow-hidden",children:[g.jsx("img",{src:r.image,alt:r.name,className:"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"}),g.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"}),g.jsx("div",{className:"absolute bottom-4 left-4 right-4 flex justify-between items-end",children:g.jsxs("div",{className:"text-white",children:[g.jsxs("div",{className:"flex items-center space-x-1 mb-1",children:[g.jsx(Tf,{className:"w-4 h-4 text-yellow-400 fill-current"}),g.jsx("span",{className:"text-sm font-medium",children:r.rating})]}),g.jsxs("div",{className:"flex items-center space-x-1",children:[g.jsx(Nr,{className:"w-4 h-4"}),g.jsxs("span",{className:"text-sm",children:[r.students," studentů"]})]})]})})]}),g.jsxs("div",{className:"p-6",children:[g.jsxs("div",{className:"mb-4",children:[g.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-1",children:r.name}),g.jsx("p",{className:"text-primary-600 font-medium mb-2",children:r.title}),g.jsxs("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[g.jsx(qg,{className:"w-4 h-4"}),g.jsx("span",{children:r.experience})]})]}),g.jsx("p",{className:"text-gray-600 text-sm mb-4 leading-relaxed",children:r.bio}),g.jsxs("div",{className:"mb-4",children:[g.jsx("h4",{className:"font-semibold text-gray-900 mb-2 text-sm",children:"Specializace:"}),g.jsx("div",{className:"flex flex-wrap gap-2",children:r.specialization.map((d,f)=>g.jsx("span",{className:"px-2 py-1 bg-primary-100 text-primary-700 rounded-full text-xs font-medium",children:d},f))})]}),g.jsxs("div",{children:[g.jsxs("h4",{className:"font-semibold text-gray-900 mb-2 text-sm flex items-center",children:[g.jsx(JA,{className:"w-4 h-4 mr-1"}),"Úspěchy:"]}),g.jsx("ul",{className:"space-y-1",children:r.achievements.slice(0,2).map((d,f)=>g.jsxs("li",{className:"flex items-start space-x-2",children:[g.jsx("div",{className:"w-1.5 h-1.5 bg-primary-500 rounded-full mt-2 flex-shrink-0"}),g.jsx("span",{className:"text-xs text-gray-600",children:d})]},f))})]})]})]},c))}),g.jsxs(Z.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{delay:.4,duration:.6},className:"text-center mt-16",children:[g.jsx("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Chcete se stát trenérem?"}),g.jsx("p",{className:"text-gray-600 mb-6 max-w-2xl mx-auto",children:"Hledáme nadšené a kvalifikované trenéry, kteří by rozšířili náš tým. Pokud máte zkušenosti s bojovými uměními a chuť učit, ozvěte se nám!"}),g.jsx(Z.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"btn-primary",children:"Kontaktujte nás"})]})]})})},$g=()=>{const[a,i]=w.useState(null),[s,r]=w.useState("all"),c=[{id:"all",name:"Vše"},{id:"training",name:"Tréninky"},{id:"competitions",name:"Soutěže"},{id:"events",name:"Akce"},{id:"kids",name:"Děti"}],d=[{id:1,type:"image",src:"https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",category:"training",title:"MMA Trénink",description:"Intenzivní trénink smíšených bojových umění"},{id:2,type:"image",src:"https://images.unsplash.com/photo-1544717297-fa95b6ee9643?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",category:"training",title:"Grappling Session",description:"Technika a síla v grapplingovém tréninku"},{id:3,type:"image",src:"https://images.unsplash.com/photo-1549719386-74dfcbf7dbed?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",category:"competitions",title:"Regionální turnaj",description:"Naši bojovníci na regionálním turnaji"},{id:4,type:"image",src:"https://images.unsplash.com/photo-1566753323558-f4e0952af115?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",category:"training",title:"Karate Kata",description:"Preciznost a elegance v karate kata"},{id:5,type:"image",src:"https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",category:"kids",title:"Dětský trénink",description:"Mladí bojovníci se učí základy"},{id:6,type:"image",src:"https://images.unsplash.com/photo-1552374196-1ab2a1c593e8?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",category:"events",title:"Graduační ceremonie",description:"Slavnostní předávání pásů"},{id:7,type:"video",src:"https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",category:"training",title:"Tréninkové video",description:"Ukázka našich tréninků"},{id:8,type:"image",src:"https://images.unsplash.com/photo-1517438984742-1262db08379e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",category:"competitions",title:"Vítězné momenty",description:"Radost z úspěchu na turnaji"}],f=s==="all"?d:d.filter(h=>h.category===s),p={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},m={hidden:{y:20,opacity:0},visible:{y:0,opacity:1,transition:{duration:.5,ease:"easeOut"}}};return g.jsx("section",{id:"gallery",className:"section-padding bg-gradient-to-br from-gray-50 to-gray-100",children:g.jsxs("div",{className:"container-custom",children:[g.jsxs(Z.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.6},className:"text-center mb-16",children:[g.jsx("span",{className:"inline-block px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-medium mb-4",children:"Naše momenty"}),g.jsxs("h2",{className:"text-4xl lg:text-5xl font-display font-bold text-gray-900 mb-6",children:["Foto",g.jsx("span",{className:"text-gradient block",children:"galerie"})]}),g.jsx("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Podívejte se na atmosféru našich tréninků, úspěchy našich členů a nezapomenutelné momenty z našeho dojo."})]}),g.jsx(Z.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.6,delay:.2},className:"flex flex-wrap justify-center gap-4 mb-12",children:c.map(h=>g.jsx("button",{onClick:()=>r(h.id),className:`px-6 py-3 rounded-full font-medium transition-all duration-300 ${s===h.id?"bg-primary-600 text-white shadow-lg":"bg-white text-gray-700 hover:bg-primary-50 hover:text-primary-600"}`,children:h.name},h.id))}),g.jsx(Z.div,{variants:p,initial:"hidden",animate:"visible",className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:f.map((h,v)=>{var b;return g.jsxs(Z.div,{variants:m,className:"group relative aspect-square bg-gray-200 rounded-xl overflow-hidden cursor-pointer",onClick:()=>i(h),children:[g.jsx("img",{src:h.src,alt:h.title,className:"w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"}),g.jsxs("div",{className:"absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:[g.jsxs("div",{className:"absolute bottom-4 left-4 right-4 text-white",children:[g.jsx("h3",{className:"font-semibold mb-1",children:h.title}),g.jsx("p",{className:"text-sm text-gray-300",children:h.description})]}),h.type==="video"&&g.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:g.jsx("div",{className:"w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center",children:g.jsx(Kg,{className:"w-8 h-8 text-white ml-1"})})})]}),g.jsx("div",{className:"absolute top-4 left-4",children:g.jsx("span",{className:"px-2 py-1 bg-primary-600 text-white text-xs font-medium rounded-full",children:(b=c.find(S=>S.id===h.category))==null?void 0:b.name})})]},h.id)})},s),a&&g.jsx(Z.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4",onClick:()=>i(null),children:g.jsxs(Z.div,{initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.8,opacity:0},className:"relative max-w-4xl max-h-full",onClick:h=>h.stopPropagation(),children:[g.jsx("img",{src:a.src,alt:a.title,className:"w-full h-full object-contain rounded-lg"}),g.jsx("button",{onClick:()=>i(null),className:"absolute top-4 right-4 w-10 h-10 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-black/70 transition-colors duration-200",children:g.jsx(Zg,{className:"w-6 h-6"})}),g.jsxs("div",{className:"absolute bottom-4 left-4 right-4 bg-black/50 backdrop-blur-sm rounded-lg p-4 text-white",children:[g.jsx("h3",{className:"text-xl font-semibold mb-2",children:a.title}),g.jsx("p",{className:"text-gray-300",children:a.description})]})]})}),g.jsxs(Z.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{delay:.4,duration:.6},className:"text-center mt-16",children:[g.jsx("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Sledujte nás na sociálních sítích"}),g.jsx("p",{className:"text-gray-600 mb-6 max-w-2xl mx-auto",children:"Více fotek a videí z našich tréninků a akcí najdete na našich sociálních sítích. Buďte součástí naší komunity!"}),g.jsxs("div",{className:"flex justify-center gap-4",children:[g.jsx(Z.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"btn-primary",children:"Facebook"}),g.jsx(Z.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"btn-outline",children:"Instagram"})]})]})]})})},Ej=()=>{const[a,i]=w.useState({name:"",email:"",phone:"",program:"",message:""}),[s,r]=w.useState(!1),c=v=>{i({...a,[v.target.name]:v.target.value})},d=v=>{v.preventDefault(),setTimeout(()=>{r(!0),i({name:"",email:"",phone:"",program:"",message:""})},1e3)},f=["MMA Začátečníci","MMA Pokročilí","Allkampf-Jitsu","Dětské MMA","Karate Kyokushin","Kondiční trénink"],p=[{icon:xr,title:"Adresa",details:["Sportovní hala Strakonice","Strakonice 386 01","Česká republika"]},{icon:Sf,title:"Telefon",details:["+420 777 123 456","+420 608 987 654"]},{icon:Gg,title:"Email",details:["<EMAIL>","<EMAIL>"]},{icon:wr,title:"Otevírací doba",details:["Po-Čt: 17:00-21:00","Pá: 18:00-20:00","So-Ne: Podle rozvrhu"]}],m={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2}}},h={hidden:{y:30,opacity:0},visible:{y:0,opacity:1,transition:{duration:.6,ease:"easeOut"}}};return g.jsx("section",{id:"contact",className:"section-padding bg-white",children:g.jsxs("div",{className:"container-custom",children:[g.jsxs(Z.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.6},className:"text-center mb-16",children:[g.jsx("span",{className:"inline-block px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-medium mb-4",children:"Spojte se s námi"}),g.jsxs("h2",{className:"text-4xl lg:text-5xl font-display font-bold text-gray-900 mb-6",children:["Máte",g.jsx("span",{className:"text-gradient block",children:"otázky?"})]}),g.jsx("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Rádi odpovíme na všechny vaše dotazy a pomůžeme vám najít ten správný program pro vaše cíle."})]}),g.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-16",children:[g.jsx(Z.div,{variants:m,initial:"hidden",whileInView:"visible",viewport:{once:!0,margin:"-100px"},children:g.jsxs(Z.div,{variants:h,className:"bg-gray-50 rounded-2xl p-8",children:[g.jsx("h3",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Napište nám"}),s?g.jsxs(Z.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},className:"text-center py-8",children:[g.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:g.jsx(Yg,{className:"w-8 h-8 text-green-600"})}),g.jsx("h4",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Zpráva odeslána!"}),g.jsx("p",{className:"text-gray-600",children:"Děkujeme za váš zájem. Ozveme se vám co nejdříve."})]}):g.jsxs("form",{onSubmit:d,className:"space-y-6",children:[g.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-6",children:[g.jsxs("div",{children:[g.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Jméno a příjmení *"}),g.jsx("input",{type:"text",name:"name",value:a.name,onChange:c,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200",placeholder:"Vaše jméno"})]}),g.jsxs("div",{children:[g.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email *"}),g.jsx("input",{type:"email",name:"email",value:a.email,onChange:c,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200",placeholder:"<EMAIL>"})]})]}),g.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-6",children:[g.jsxs("div",{children:[g.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Telefon"}),g.jsx("input",{type:"tel",name:"phone",value:a.phone,onChange:c,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200",placeholder:"+420 777 123 456"})]}),g.jsxs("div",{children:[g.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Zájem o program"}),g.jsxs("select",{name:"program",value:a.program,onChange:c,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200",children:[g.jsx("option",{value:"",children:"Vyberte program"}),f.map(v=>g.jsx("option",{value:v,children:v},v))]})]})]}),g.jsxs("div",{children:[g.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Zpráva *"}),g.jsx("textarea",{name:"message",value:a.message,onChange:c,required:!0,rows:5,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200",placeholder:"Napište nám svůj dotaz nebo požadavek..."})]}),g.jsxs(Z.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"submit",className:"w-full btn-primary flex items-center justify-center",children:[g.jsx(dj,{className:"w-5 h-5 mr-2"}),"Odeslat zprávu"]})]})]})}),g.jsxs(Z.div,{variants:m,initial:"hidden",whileInView:"visible",viewport:{once:!0,margin:"-100px"},className:"space-y-8",children:[g.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-6",children:p.map((v,b)=>g.jsx(Z.div,{variants:h,className:"bg-white border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-shadow duration-300",children:g.jsxs("div",{className:"flex items-start space-x-4",children:[g.jsx("div",{className:"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center flex-shrink-0",children:g.jsx(v.icon,{className:"w-6 h-6 text-primary-600"})}),g.jsxs("div",{children:[g.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:v.title}),v.details.map((S,E)=>g.jsx("p",{className:"text-gray-600 text-sm",children:S},E))]})]})},b))}),g.jsxs(Z.div,{variants:h,className:"bg-gray-200 rounded-2xl h-64 flex items-center justify-center relative overflow-hidden",children:[g.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-primary-500/20 to-primary-600/10"}),g.jsxs("div",{className:"text-center z-10",children:[g.jsx(xr,{className:"w-12 h-12 text-primary-600 mx-auto mb-4"}),g.jsx("h4",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Interaktivní mapa"}),g.jsx("p",{className:"text-gray-600 text-sm",children:"Klikněte pro zobrazení v Google Maps"})]})]}),g.jsxs(Z.div,{variants:h,className:"bg-primary-50 rounded-2xl p-6",children:[g.jsx("h4",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Rychlé akce"}),g.jsxs("div",{className:"space-y-3",children:[g.jsx(Z.button,{whileHover:{scale:1.02},whileTap:{scale:.98},className:"w-full text-left p-3 bg-white rounded-lg hover:bg-primary-50 transition-colors duration-200 border border-gray-200",children:"📞 Zavolat hned teď"}),g.jsx(Z.button,{whileHover:{scale:1.02},whileTap:{scale:.98},className:"w-full text-left p-3 bg-white rounded-lg hover:bg-primary-50 transition-colors duration-200 border border-gray-200",children:"📅 Rezervovat zkušební trénink"}),g.jsx(Z.button,{whileHover:{scale:1.02},whileTap:{scale:.98},className:"w-full text-left p-3 bg-white rounded-lg hover:bg-primary-50 transition-colors duration-200 border border-gray-200",children:"📍 Navigovat k nám"})]})]})]})]})]})})},wj=()=>{const a=()=>{window.scrollTo({top:0,behavior:"smooth"})},i={company:[{name:"O nás",path:"/about"},{name:"Naši trenéři",path:"/trainers"},{name:"Galerie",path:"/gallery"},{name:"Kontakt",path:"/contact"}],programs:[{name:"MMA Začátečníci",path:"/training"},{name:"MMA Pokročilí",path:"/training"},{name:"Allkampf-Jitsu",path:"/training"},{name:"Dětské MMA",path:"/training"},{name:"Karate Kyokushin",path:"/training"},{name:"Kondiční trénink",path:"/training"}],support:[{name:"Časté otázky",path:"/faq"},{name:"Ceník",path:"/pricing"},{name:"Pravidla dojo",path:"/rules"},{name:"Bezpečnost",path:"/safety"}]},s=[{icon:tj,href:"#",label:"Facebook"},{icon:ij,href:"#",label:"Instagram"},{icon:Sj,href:"#",label:"YouTube"}];return g.jsxs("footer",{className:"bg-gray-900 text-white relative overflow-hidden",children:[g.jsxs("div",{className:"absolute inset-0 opacity-5",children:[g.jsx("div",{className:"absolute top-0 left-0 w-96 h-96 bg-primary-500 rounded-full blur-3xl"}),g.jsx("div",{className:"absolute bottom-0 right-0 w-96 h-96 bg-primary-400 rounded-full blur-3xl"})]}),g.jsxs("div",{className:"relative z-10",children:[g.jsx("div",{className:"container-custom py-16",children:g.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-12",children:[g.jsx("div",{className:"lg:col-span-1",children:g.jsxs(Z.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.6},children:[g.jsxs(se,{to:"/",className:"flex items-center space-x-3 mb-6",children:[g.jsxs("div",{className:"relative",children:[g.jsx(Af,{className:"w-10 h-10 text-primary-500"}),g.jsx("div",{className:"absolute inset-0 bg-primary-500 rounded-full blur-lg opacity-30"})]}),g.jsxs("div",{children:[g.jsx("span",{className:"font-display font-bold text-2xl",children:"COBRA RYU"}),g.jsx("span",{className:"block text-sm text-gray-400",children:"STRAKONICE"})]})]}),g.jsx("p",{className:"text-gray-400 mb-6 leading-relaxed",children:"Moderní škola bojových umění v srdci Strakonic. Objevte sílu, disciplínu a sebevědomí prostřednictvím našich specializovaných programů."}),g.jsxs("div",{className:"space-y-3",children:[g.jsxs("div",{className:"flex items-center space-x-3",children:[g.jsx(xr,{className:"w-5 h-5 text-primary-500 flex-shrink-0"}),g.jsx("span",{className:"text-gray-400 text-sm",children:"Strakonice 386 01"})]}),g.jsxs("div",{className:"flex items-center space-x-3",children:[g.jsx(Sf,{className:"w-5 h-5 text-primary-500 flex-shrink-0"}),g.jsx("span",{className:"text-gray-400 text-sm",children:"+420 777 123 456"})]}),g.jsxs("div",{className:"flex items-center space-x-3",children:[g.jsx(Gg,{className:"w-5 h-5 text-primary-500 flex-shrink-0"}),g.jsx("span",{className:"text-gray-400 text-sm",children:"<EMAIL>"})]})]})]})}),g.jsx("div",{className:"lg:col-span-3",children:g.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[g.jsxs(Z.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.6,delay:.1},children:[g.jsx("h3",{className:"font-semibold text-lg mb-6",children:"Klub"}),g.jsx("ul",{className:"space-y-3",children:i.company.map((r,c)=>g.jsx("li",{children:g.jsx(se,{to:r.path,className:"text-gray-400 hover:text-primary-400 transition-colors duration-200 text-sm",children:r.name})},c))})]}),g.jsxs(Z.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.6,delay:.2},children:[g.jsx("h3",{className:"font-semibold text-lg mb-6",children:"Programy"}),g.jsx("ul",{className:"space-y-3",children:i.programs.map((r,c)=>g.jsx("li",{children:g.jsx(se,{to:r.path,className:"text-gray-400 hover:text-primary-400 transition-colors duration-200 text-sm",children:r.name})},c))})]}),g.jsxs(Z.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.6,delay:.3},children:[g.jsx("h3",{className:"font-semibold text-lg mb-6",children:"Podpora"}),g.jsx("ul",{className:"space-y-3 mb-6",children:i.support.map((r,c)=>g.jsx("li",{children:g.jsx(se,{to:r.path,className:"text-gray-400 hover:text-primary-400 transition-colors duration-200 text-sm",children:r.name})},c))}),g.jsxs("div",{children:[g.jsx("h4",{className:"font-medium mb-4",children:"Sledujte nás"}),g.jsx("div",{className:"flex space-x-4",children:s.map((r,c)=>g.jsx(Z.a,{href:r.href,whileHover:{scale:1.1},whileTap:{scale:.9},className:"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-primary-600 transition-colors duration-200","aria-label":r.label,children:g.jsx(r.icon,{className:"w-5 h-5"})},c))})]})]})]})})]})}),g.jsx(Z.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.6},className:"border-t border-gray-800",children:g.jsx("div",{className:"container-custom py-8",children:g.jsxs("div",{className:"flex flex-col md:flex-row items-center justify-between",children:[g.jsxs("div",{className:"mb-6 md:mb-0",children:[g.jsx("h3",{className:"text-xl font-semibold mb-2",children:"Zůstaňte v obraze"}),g.jsx("p",{className:"text-gray-400",children:"Přihlaste se k odběru novinek a získejte nejnovější informace o našich programech."})]}),g.jsxs("div",{className:"flex w-full md:w-auto",children:[g.jsx("input",{type:"email",placeholder:"Váš email",className:"flex-1 md:w-64 px-4 py-3 bg-gray-800 border border-gray-700 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"}),g.jsx(Z.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"px-6 py-3 bg-primary-600 hover:bg-primary-700 rounded-r-lg transition-colors duration-200",children:"Odebírat"})]})]})})}),g.jsx("div",{className:"border-t border-gray-800",children:g.jsx("div",{className:"container-custom py-6",children:g.jsxs("div",{className:"flex flex-col md:flex-row items-center justify-between",children:[g.jsxs("p",{className:"text-gray-400 text-sm mb-4 md:mb-0",children:["© ",new Date().getFullYear()," COBRA RYU Strakonice. Všechna práva vyhrazena."]}),g.jsxs("div",{className:"flex items-center space-x-6",children:[g.jsx(se,{to:"/privacy",className:"text-gray-400 hover:text-primary-400 text-sm transition-colors duration-200",children:"Ochrana soukromí"}),g.jsx(se,{to:"/terms",className:"text-gray-400 hover:text-primary-400 text-sm transition-colors duration-200",children:"Podmínky použití"}),g.jsx(Z.button,{onClick:a,whileHover:{scale:1.1},whileTap:{scale:.9},className:"w-10 h-10 bg-primary-600 hover:bg-primary-700 rounded-lg flex items-center justify-center transition-colors duration-200","aria-label":"Scroll to top",children:g.jsx(QA,{className:"w-5 h-5"})})]})]})})})]})]})},Nj=({end:a,duration:i=2})=>{const[s,r]=w.useState(0),[c,d]=w.useState(!1),f=w.useRef(null),p=LA(f,{once:!0});return w.useEffect(()=>{if(p&&!c){d(!0);let m=null;const h=v=>{m===null&&(m=v);const b=Math.min((v-m)/(i*1e3),1);r(Math.floor(b*a)),b<1&&requestAnimationFrame(h)};requestAnimationFrame(h)}},[p,a,i,c]),g.jsx("span",{ref:f,children:s})},Mj=()=>{const a=[{icon:Nr,number:500,suffix:"+",label:"Aktivních členů",description:"Rostoucí komunita bojovníků"},{icon:gj,number:15,suffix:"+",label:"Let zkušeností",description:"Tradice a odbornost"},{icon:Tf,number:50,suffix:"+",label:"Turnajových vítězství",description:"Úspěchy našich členů"},{icon:wr,number:24,suffix:"/7",label:"Podpora komunity",description:"Vždy tu pro vás jsme"}],i={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2}}},s={hidden:{y:30,opacity:0},visible:{y:0,opacity:1,transition:{duration:.6,ease:"easeOut"}}};return g.jsx("section",{className:"section-padding bg-gradient-to-br from-gray-50 to-white",children:g.jsxs("div",{className:"container-custom",children:[g.jsx(Z.div,{variants:i,initial:"hidden",whileInView:"visible",viewport:{once:!0,margin:"-100px"},className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:a.map((r,c)=>g.jsx(Z.div,{variants:s,className:"group relative",children:g.jsxs("div",{className:"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 group-hover:border-primary-200",children:[g.jsx("div",{className:"absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-primary-500/10 to-primary-600/5 rounded-bl-2xl"}),g.jsx("div",{className:"relative z-10 mb-6",children:g.jsx("div",{className:"w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300",children:g.jsx(r.icon,{className:"w-8 h-8 text-white"})})}),g.jsx("div",{className:"relative z-10 mb-2",children:g.jsxs("span",{className:"text-4xl lg:text-5xl font-bold text-gray-900 font-display",children:[g.jsx(Nj,{end:r.number}),g.jsx("span",{className:"text-primary-600",children:r.suffix})]})}),g.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:r.label}),g.jsx("p",{className:"text-gray-600 text-sm leading-relaxed",children:r.description}),g.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-primary-500/5 to-primary-600/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]})},c))}),g.jsxs(Z.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{delay:.8,duration:.6},className:"text-center mt-16",children:[g.jsx("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Připraveni začít svou cestu?"}),g.jsx("p",{className:"text-gray-600 mb-8 max-w-2xl mx-auto",children:"Přidejte se k naší rostoucí komunitě a objevte, co ve vás dřímá. První trénink je zdarma!"}),g.jsx(Z.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"btn-primary",children:"Zkusit zdarma"})]})]})})},Rj=()=>{const a={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2}}},i={hidden:{y:30,opacity:0},visible:{y:0,opacity:1,transition:{duration:.6,ease:"easeOut"}}};return g.jsxs("section",{className:"section-padding bg-gradient-to-br from-primary-900 via-primary-800 to-primary-900 relative overflow-hidden",children:[g.jsxs("div",{className:"absolute inset-0",children:[g.jsx("div",{className:"absolute top-0 left-0 w-96 h-96 bg-primary-500/10 rounded-full blur-3xl"}),g.jsx("div",{className:"absolute bottom-0 right-0 w-96 h-96 bg-primary-400/10 rounded-full blur-3xl"})]}),g.jsx("div",{className:"container-custom relative z-10",children:g.jsxs(Z.div,{variants:a,initial:"hidden",whileInView:"visible",viewport:{once:!0,margin:"-100px"},className:"text-center",children:[g.jsxs(Z.div,{variants:i,className:"mb-16",children:[g.jsxs("h2",{className:"text-4xl lg:text-6xl font-display font-bold text-white mb-6",children:["Připraveni začít svou",g.jsx("span",{className:"block text-primary-300",children:"cestu bojovníka?"})]}),g.jsx("p",{className:"text-xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed",children:"Přidejte se k naší komunitě a objevte sílu, kterou v sobě máte. První trénink je zdarma a bez závazků!"}),g.jsxs("div",{className:"flex flex-col sm:flex-row gap-6 justify-center items-center",children:[g.jsxs(se,{to:"/contact",className:"group btn-secondary text-lg px-10 py-4 flex items-center",children:["Začít hned teď",g.jsx(Hg,{className:"w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-200"})]}),g.jsx(Z.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"text-white border-2 border-white/30 hover:border-white/50 font-semibold py-4 px-10 rounded-lg transition-all duration-200 backdrop-blur-sm",children:"Zobrazit rozvrh"})]})]}),g.jsxs(Z.div,{variants:a,className:"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto",children:[g.jsxs(Z.div,{variants:i,className:"glass-effect p-6 text-center group hover:bg-white/20 transition-all duration-300",children:[g.jsx("div",{className:"w-16 h-16 bg-primary-500/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary-500/30 transition-colors duration-300",children:g.jsx(xr,{className:"w-8 h-8 text-primary-300"})}),g.jsx("h3",{className:"text-white font-semibold text-lg mb-2",children:"Kde nás najdete"}),g.jsxs("p",{className:"text-gray-300 text-sm",children:["Sportovní hala Strakonice",g.jsx("br",{}),"Strakonice 386 01"]})]}),g.jsxs(Z.div,{variants:i,className:"glass-effect p-6 text-center group hover:bg-white/20 transition-all duration-300",children:[g.jsx("div",{className:"w-16 h-16 bg-primary-500/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary-500/30 transition-colors duration-300",children:g.jsx(wr,{className:"w-8 h-8 text-primary-300"})}),g.jsx("h3",{className:"text-white font-semibold text-lg mb-2",children:"Otevírací doba"}),g.jsxs("p",{className:"text-gray-300 text-sm",children:["Po-Čt: 17:00-21:00",g.jsx("br",{}),"Pá: 18:00-20:00"]})]}),g.jsxs(Z.div,{variants:i,className:"glass-effect p-6 text-center group hover:bg-white/20 transition-all duration-300",children:[g.jsx("div",{className:"w-16 h-16 bg-primary-500/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary-500/30 transition-colors duration-300",children:g.jsx(Sf,{className:"w-8 h-8 text-primary-300"})}),g.jsx("h3",{className:"text-white font-semibold text-lg mb-2",children:"Kontakt"}),g.jsxs("p",{className:"text-gray-300 text-sm",children:["+420 777 123 456",g.jsx("br",{}),"<EMAIL>"]})]})]}),g.jsx(Z.div,{variants:i,className:"mt-16 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-8 max-w-2xl mx-auto",children:g.jsxs("div",{className:"text-center",children:[g.jsx("div",{className:"inline-flex items-center px-4 py-2 bg-primary-500 text-white rounded-full text-sm font-medium mb-4",children:"🎯 Speciální nabídka"}),g.jsx("h3",{className:"text-2xl font-bold text-white mb-4",children:"První měsíc za polovinu!"}),g.jsx("p",{className:"text-gray-300 mb-6",children:"Pro nové členy nabízíme první měsíc tréninků za 50% cenu. Platí pro všechny naše programy."}),g.jsxs("div",{className:"flex items-center justify-center space-x-4 text-sm text-gray-300",children:[g.jsx("span",{children:"✓ Bez vstupních poplatků"}),g.jsx("span",{children:"✓ Včetně výpůjčky vybavení"}),g.jsx("span",{children:"✓ Osobní konzultace zdarma"})]})]})})]})})]})};function Dj(){return g.jsxs(Z.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.5},children:[g.jsx(jj,{}),g.jsx(Mj,{}),g.jsx(Qg,{}),g.jsx(Pg,{}),g.jsx(Jg,{}),g.jsx($g,{}),g.jsx(Rj,{})]})}function Cj(){return g.jsx(Kb,{children:g.jsxs("div",{className:"min-h-screen bg-white",children:[g.jsx(Aj,{}),g.jsx("main",{children:g.jsxs(bb,{children:[g.jsx(pa,{path:"/",element:g.jsx(Dj,{})}),g.jsx(pa,{path:"/about",element:g.jsx(Qg,{})}),g.jsx(pa,{path:"/training",element:g.jsx(Pg,{})}),g.jsx(pa,{path:"/trainers",element:g.jsx(Jg,{})}),g.jsx(pa,{path:"/gallery",element:g.jsx($g,{})}),g.jsx(pa,{path:"/contact",element:g.jsx(Ej,{})})]})}),g.jsx(wj,{})]})})}M1.createRoot(document.getElementById("root")).render(g.jsx(w.StrictMode,{children:g.jsx(Cj,{})}));
