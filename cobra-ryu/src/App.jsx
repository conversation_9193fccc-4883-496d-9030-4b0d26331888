import { useState, useEffect } from "react";
import { BrowserRouter as Router, Routes, Route, Link } from "react-router-dom";
import './App.css'

function ThemeToggle() {
  const [dark, setDark] = useState(() =>
    window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
  );
  useEffect(() => {
    if (dark) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [dark]);
  return (
    <button
      className="ml-4 px-3 py-2 rounded-lg bg-gray-200 dark:bg-gray-700 text-sm hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
      onClick={() => setDark((d) => !d)}
      aria-label="Přepnout tmavý/světlý režim"
    >
      {dark ? '🌙' : '☀️'}
    </button>
  );
}

function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  return (
    <header className="bg-white dark:bg-gray-900 shadow-lg sticky top-0 z-50 border-b border-gray-200 dark:border-gray-700">
      <nav className="container mx-auto flex items-center justify-between py-4 px-4">
        <Link to="/" className="text-3xl font-bold text-red-700 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 transition-colors">
          COBRA RYU
        </Link>
        
        {/* Desktop Navigation */}
        <div className="hidden md:flex items-center gap-6">
          <Link to="/about" className="text-gray-700 dark:text-gray-300 hover:text-red-700 dark:hover:text-red-400 font-medium transition-colors">O nás</Link>
          <Link to="/trainings" className="text-gray-700 dark:text-gray-300 hover:text-red-700 dark:hover:text-red-400 font-medium transition-colors">Tréninky</Link>
          <Link to="/coaches" className="text-gray-700 dark:text-gray-300 hover:text-red-700 dark:hover:text-red-400 font-medium transition-colors">Trenéři</Link>
          <Link to="/gallery" className="text-gray-700 dark:text-gray-300 hover:text-red-700 dark:hover:text-red-400 font-medium transition-colors">Fotogalerie</Link>
          <Link to="/contact" className="text-gray-700 dark:text-gray-300 hover:text-red-700 dark:hover:text-red-400 font-medium transition-colors">Kontakt</Link>
          <ThemeToggle />
        </div>

        {/* Mobile Menu Button */}
        <div className="md:hidden flex items-center gap-2">
          <ThemeToggle />
          <button
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            className="p-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
            aria-label="Toggle mobile menu"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              {mobileMenuOpen ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              )}
            </svg>
          </button>
        </div>
      </nav>

      {/* Mobile Navigation Menu */}
      {mobileMenuOpen && (
        <div className="md:hidden bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
          <div className="px-4 py-2 space-y-1">
            <Link 
              to="/about" 
              className="block px-3 py-2 text-gray-700 dark:text-gray-300 hover:text-red-700 dark:hover:text-red-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg font-medium transition-colors"
              onClick={() => setMobileMenuOpen(false)}
            >
              O nás
            </Link>
            <Link 
              to="/trainings" 
              className="block px-3 py-2 text-gray-700 dark:text-gray-300 hover:text-red-700 dark:hover:text-red-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg font-medium transition-colors"
              onClick={() => setMobileMenuOpen(false)}
            >
              Tréninky
            </Link>
            <Link 
              to="/coaches" 
              className="block px-3 py-2 text-gray-700 dark:text-gray-300 hover:text-red-700 dark:hover:text-red-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg font-medium transition-colors"
              onClick={() => setMobileMenuOpen(false)}
            >
              Trenéři
            </Link>
            <Link 
              to="/gallery" 
              className="block px-3 py-2 text-gray-700 dark:text-gray-300 hover:text-red-700 dark:hover:text-red-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg font-medium transition-colors"
              onClick={() => setMobileMenuOpen(false)}
            >
              Fotogalerie
            </Link>
            <Link 
              to="/contact" 
              className="block px-3 py-2 text-gray-700 dark:text-gray-300 hover:text-red-700 dark:hover:text-red-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg font-medium transition-colors"
              onClick={() => setMobileMenuOpen(false)}
            >
              Kontakt
            </Link>
          </div>
        </div>
      )}
    </header>
  );
}

function Footer() {
  return (
    <footer className="bg-gray-900 dark:bg-black text-white py-12 mt-16">
      <div className="container mx-auto px-4">
        <div className="grid md:grid-cols-3 gap-8">
          <div>
            <h3 className="text-xl font-bold text-red-400 mb-4">COBRA RYU</h3>
            <p className="text-gray-300">Moderní škola bojových umění v srdci Strakonic. MMA, ALKAMFjitsu a další bojové sporty pro všechny věkové kategorie.</p>
          </div>
          <div>
            <h4 className="font-semibold mb-4">Rychlé odkazy</h4>
            <div className="space-y-2">
              <Link to="/about" className="block text-gray-300 hover:text-red-400 transition-colors">O nás</Link>
              <Link to="/trainings" className="block text-gray-300 hover:text-red-400 transition-colors">Tréninky</Link>
              <Link to="/coaches" className="block text-gray-300 hover:text-red-400 transition-colors">Trenéři</Link>
              <Link to="/contact" className="block text-gray-300 hover:text-red-400 transition-colors">Kontakt</Link>
            </div>
          </div>
          <div>
            <h4 className="font-semibold mb-4">Kontakt</h4>
            <div className="space-y-2 text-gray-300">
              <p>📍 Strakonice, Česká republika</p>
              <p>📞 +420 XXX XXX XXX</p>
              <p>✉️ <EMAIL></p>
            </div>
          </div>
        </div>
        <div className="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
          &copy; {new Date().getFullYear()} COBRA RYU Strakonice &mdash; Moderní klub bojových umění
        </div>
      </div>
    </footer>
  );
}

function HomePage() {
  return (
    <div>
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-gray-900 via-red-900 to-black text-white py-24 overflow-hidden">
        <div className="absolute inset-0 bg-black/50"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-5xl md:text-7xl font-extrabold mb-6 leading-tight">
              COBRA RYU
              <span className="block text-3xl md:text-4xl text-red-400 font-normal mt-2">Strakonice</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-gray-200 max-w-2xl mx-auto">
              Moderní škola bojových umění pro všechny věkové kategorie. 
              MMA, ALKAMFjitsu a další bojové sporty v srdci Strakonic.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                to="/contact" 
                className="inline-block px-8 py-4 bg-red-700 text-white rounded-lg font-bold text-lg shadow-lg hover:bg-red-800 transform hover:scale-105 transition-all duration-200"
              >
                Přidej se k nám
              </Link>
              <Link 
                to="/trainings" 
                className="inline-block px-8 py-4 border-2 border-white text-white rounded-lg font-bold text-lg hover:bg-white hover:text-gray-900 transition-all duration-200"
              >
                Rozvrh tréninků
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Info Section */}
      <section className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center p-6">
              <div className="text-4xl mb-4">🥊</div>
              <h3 className="text-xl font-bold mb-2 text-gray-900 dark:text-white">MMA & ALKAMFjitsu</h3>
              <p className="text-gray-600 dark:text-gray-300">Moderní bojové umění kombinující různé techniky a styly</p>
            </div>
            <div className="text-center p-6">
              <div className="text-4xl mb-4">👥</div>
              <h3 className="text-xl font-bold mb-2 text-gray-900 dark:text-white">Pro všechny věky</h3>
              <p className="text-gray-600 dark:text-gray-300">Tréninky pro děti, mládež i dospělé všech úrovní</p>
            </div>
            <div className="text-center p-6">
              <div className="text-4xl mb-4">🏆</div>
              <h3 className="text-xl font-bold mb-2 text-gray-900 dark:text-white">Zkušení trenéři</h3>
              <p className="text-gray-600 dark:text-gray-300">Kvalifikovaní instruktoři s dlouholetými zkušenostmi</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

function AboutPage() {
  return (
    <section className="container mx-auto py-16 px-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold mb-8 text-center text-gray-900 dark:text-white">O nás</h1>
        
        <div className="prose prose-lg max-w-none dark:prose-invert">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 mb-8">
            <h2 className="text-2xl font-bold mb-4 text-red-700 dark:text-red-400">Naše historie</h2>
            <p className="mb-4">
              COBRA RYU Strakonice je moderní škola bojových umění, která vznikla z vášně pro bojové sporty 
              a touhy předávat znalosti dalším generacím. Naše cesta začala před několika lety s malou skupinou 
              nadšenců, kteří chtěli vytvořit místo, kde se lidé mohou učit, růst a překonávat své limity.
            </p>
            <p>
              Dnes jsme hrdí na to, že můžeme nabídnout kvalitní výuku MMA, ALKAMFjitsu a dalších bojových 
              umění v přátelském a podporujícím prostředí.
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 mb-8">
            <h2 className="text-2xl font-bold mb-4 text-red-700 dark:text-red-400">Naše filozofie</h2>
            <ul className="space-y-3">
              <li className="flex items-start">
                <span className="text-red-500 mr-2">•</span>
                <span><strong>Respekt:</strong> Respektujeme každého člena naší komunity bez ohledu na věk, pohlaví nebo úroveň</span>
              </li>
              <li className="flex items-start">
                <span className="text-red-500 mr-2">•</span>
                <span><strong>Disciplína:</strong> Učíme sebekázeň a vytrvalost jak na žíněnce, tak v životě</span>
              </li>
              <li className="flex items-start">
                <span className="text-red-500 mr-2">•</span>
                <span><strong>Růst:</strong> Podporujeme osobní rozvoj každého jednotlivce</span>
              </li>
              <li className="flex items-start">
                <span className="text-red-500 mr-2">•</span>
                <span><strong>Komunita:</strong> Vytváříme silnou a podporující komunitu bojovníků</span>
              </li>
            </ul>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
            <h2 className="text-2xl font-bold mb-4 text-red-700 dark:text-red-400">Co nabízíme</h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold mb-2">MMA (Mixed Martial Arts)</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">Komplexní bojové umění kombinující striking, grappling a ground fighting</p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">ALKAMFjitsu</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">Moderní systém sebeobrany a bojového umění</p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">Dětské tréninky</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">Speciálně navržené programy pro děti a mládež</p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">Kondiční tréninky</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">Zlepšení fyzické kondice a celkové fitness</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

function TrainingsPage() {
  const trainings = [
    {
      name: "MMA Začátečníci",
      time: "Pondělí, Středa 18:00-19:30",
      description: "Základy MMA pro úplné začátečníky",
      age: "16+ let",
      level: "Začátečník"
    },
    {
      name: "MMA Pokročilí",
      time: "Pondělí, Středa 19:30-21:00",
      description: "Pokročilé techniky a sparring",
      age: "18+ let",
      level: "Pokročilý"
    },
    {
      name: "ALKAMFjitsu",
      time: "Úterý, Čtvrtek 19:00-20:30",
      description: "Moderní sebeobrana a bojové techniky",
      age: "14+ let",
      level: "Všechny úrovně"
    },
    {
      name: "Dětské MMA",
      time: "Úterý, Čtvrtek 17:00-18:00",
      description: "Bojové umění pro děti - zábavnou formou",
      age: "6-14 let",
      level: "Začátečník"
    },
    {
      name: "Kondiční trénink",
      time: "Pátek 18:00-19:00",
      description: "Funkční trénink a příprava kondice",
      age: "16+ let",
      level: "Všechny úrovně"
    }
  ];

  return (
    <section className="container mx-auto py-16 px-4">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-4xl font-bold mb-8 text-center text-gray-900 dark:text-white">Rozvrh tréninků</h1>
        
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-2 text-red-800 dark:text-red-300">Zkušební trénink zdarma!</h2>
          <p className="text-red-700 dark:text-red-200">
            Přijďte si vyzkoušet jakýkoliv trénink zdarma. Stačí se ozvat předem a domluvit si termín.
          </p>
        </div>

        <div className="grid gap-6">
          {trainings.map((training, index) => (
            <div key={index} className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 border-l-4 border-red-500">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                <div className="flex-1">
                  <h3 className="text-xl font-bold mb-2 text-gray-900 dark:text-white">{training.name}</h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-2">{training.description}</p>
                  <div className="flex flex-wrap gap-4 text-sm">
                    <span className="bg-gray-100 dark:bg-gray-700 px-3 py-1 rounded-full">
                      🕐 {training.time}
                    </span>
                    <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full">
                      👥 {training.age}
                    </span>
                    <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded-full">
                      📊 {training.level}
                    </span>
                  </div>
                </div>
                <div className="mt-4 md:mt-0 md:ml-6">
                  <Link 
                    to="/contact" 
                    className="inline-block px-6 py-2 bg-red-700 text-white rounded-lg font-semibold hover:bg-red-800 transition-colors"
                  >
                    Přihlásit se
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-12 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
          <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">Důležité informace</h2>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold mb-2 text-red-700 dark:text-red-400">Co si přinést</h3>
              <ul className="space-y-1 text-gray-600 dark:text-gray-300">
                <li>• Sportovní oblečení</li>
                <li>• Ručník</li>
                <li>• Láhev na vodu</li>
                <li>• Ochranné pomůcky (pokud máte)</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-2 text-red-700 dark:text-red-400">Ceny</h3>
              <ul className="space-y-1 text-gray-600 dark:text-gray-300">
                <li>• Měsíční členství: 1200 Kč</li>
                <li>• Studentské: 900 Kč</li>
                <li>• Dětské: 800 Kč</li>
                <li>• Jednorázový trénink: 200 Kč</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

function CoachesPage() {
  const coaches = [
    {
      name: "Martin Novák",
      title: "Hlavní trenér MMA",
      experience: "15 let zkušeností",
      specialization: "MMA, Kickbox, Grappling",
      description: "Bývalý profesionální bojovník s bohatými zkušenostmi z domácích i zahraničních turnajů.",
      achievements: ["Mistr ČR v MMA", "3x vítěz regionálních turnajů", "Certifikovaný trenér"]
    },
    {
      name: "Tomáš Svoboda",
      title: "Trenér ALKAMFjitsu",
      experience: "12 let zkušeností",
      specialization: "ALKAMFjitsu, Sebeobrana",
      description: "Specialista na moderní sebeobranu a praktické bojové techniky.",
      achievements: ["Instruktor ALKAMFjitsu", "Kurzy sebeobrany", "Práce s bezpečnostními složkami"]
    },
    {
      name: "Jana Kratochvílová",
      title: "Trenérka dětských skupin",
      experience: "8 let zkušeností",
      specialization: "Dětské MMA, Kondiční příprava",
      description: "Odbornice na práci s dětmi a mládeží, vytváří pozitivní a bezpečné prostředí.",
      achievements: ["Trenérka mládeže", "Kurzy dětské psychologie", "Organizátorka dětských turnajů"]
    }
  ];

  return (
    <section className="container mx-auto py-16 px-4">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-4xl font-bold mb-8 text-center text-gray-900 dark:text-white">Naši trenéři</h1>
        <p className="text-center text-gray-600 dark:text-gray-300 mb-12 max-w-2xl mx-auto">
          Náš tým tvoří zkušení a kvalifikovaní trenéři, kteří vás provedou světem bojových umění 
          s trpělivostí, odborností a vášní pro tento sport.
        </p>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {coaches.map((coach, index) => (
            <div key={index} className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
              <div className="h-48 bg-gradient-to-br from-red-500 to-red-700 flex items-center justify-center">
                <div className="text-6xl text-white">👤</div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold mb-1 text-gray-900 dark:text-white">{coach.name}</h3>
                <p className="text-red-600 dark:text-red-400 font-semibold mb-2">{coach.title}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">{coach.experience}</p>
                <p className="text-gray-600 dark:text-gray-300 mb-4 text-sm">{coach.description}</p>
                
                <div className="mb-4">
                  <h4 className="font-semibold text-sm mb-2 text-gray-900 dark:text-white">Specializace:</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-300">{coach.specialization}</p>
                </div>

                <div>
                  <h4 className="font-semibold text-sm mb-2 text-gray-900 dark:text-white">Úspěchy:</h4>
                  <ul className="text-xs text-gray-600 dark:text-gray-300 space-y-1">
                    {coach.achievements.map((achievement, i) => (
                      <li key={i}>• {achievement}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-12 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center">
          <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">Chcete se stát trenérem?</h2>
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            Hledáme nadšené a kvalifikované trenéry, kteří by rozšířili náš tým. 
            Pokud máte zkušenosti s bojovými uměními a chuť učit, ozvěte se nám!
          </p>
          <Link 
            to="/contact" 
            className="inline-block px-6 py-3 bg-red-700 text-white rounded-lg font-semibold hover:bg-red-800 transition-colors"
          >
            Kontaktujte nás
          </Link>
        </div>
      </div>
    </section>
  );
}

function GalleryPage() {
  const galleryImages = [
    { title: "Trénink MMA", category: "Tréninky" },
    { title: "Dětská skupina", category: "Děti" },
    { title: "Sparring", category: "Tréninky" },
    { title: "Turnaj", category: "Akce" },
    { title: "Graduace", category: "Akce" },
    { title: "Kondiční trénink", category: "Tréninky" },
    { title: "Týmové foto", category: "Tým" },
    { title: "Workshop", category: "Akce" }
  ];

  return (
    <section className="container mx-auto py-16 px-4">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-4xl font-bold mb-8 text-center text-gray-900 dark:text-white">Fotogalerie</h1>
        <p className="text-center text-gray-600 dark:text-gray-300 mb-12 max-w-2xl mx-auto">
          Podívejte se na atmosféru našich tréninků, akcí a úspěchů našich členů.
        </p>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {galleryImages.map((image, index) => (
            <div key={index} className="group relative aspect-square bg-gradient-to-br from-gray-300 to-gray-500 dark:from-gray-600 dark:to-gray-800 rounded-lg overflow-hidden cursor-pointer hover:scale-105 transition-transform duration-200">
              <div className="absolute inset-0 bg-black/40 group-hover:bg-black/60 transition-colors duration-200 flex items-center justify-center">
                <div className="text-white text-center">
                  <div className="text-4xl mb-2">📸</div>
                  <p className="font-semibold">{image.title}</p>
                  <p className="text-sm opacity-75">{image.category}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-12 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center">
          <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">Sledujte nás na sociálních sítích</h2>
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            Více fotek a videí z našich tréninků a akcí najdete na našich sociálních sítích.
          </p>
          <div className="flex justify-center gap-4">
            <a href="#" className="px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors">
              Facebook
            </a>
            <a href="#" className="px-6 py-3 bg-pink-600 text-white rounded-lg font-semibold hover:bg-pink-700 transition-colors">
              Instagram
            </a>
            <a href="#" className="px-6 py-3 bg-red-600 text-white rounded-lg font-semibold hover:bg-red-700 transition-colors">
              YouTube
            </a>
          </div>
        </div>
      </div>
    </section>
  );
}

function ContactPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    message: '',
    training: ''
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    // Zde by byla logika pro odeslání formuláře
    alert('Děkujeme za zprávu! Ozveme se vám co nejdříve.');
    setFormData({ name: '', email: '', phone: '', message: '', training: '' });
  };

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <section className="container mx-auto py-16 px-4">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-4xl font-bold mb-8 text-center text-gray-900 dark:text-white">Kontakt</h1>
        
        <div className="grid lg:grid-cols-2 gap-12">
          {/* Kontaktní formulář */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
            <h2 className="text-2xl font-bold mb-6 text-gray-900 dark:text-white">Napište nám</h2>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                  Jméno a příjmení *
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                  E-mail *
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                  Telefon
                </label>
                <input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                  Zájem o trénink
                </label>
                <select
                  name="training"
                  value={formData.training}
                  onChange={handleChange}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                >
                  <option value="">Vyberte trénink</option>
                  <option value="mma-zacatecnici">MMA Začátečníci</option>
                  <option value="mma-pokrocili">MMA Pokročilí</option>
                  <option value="alkamfjitsu">ALKAMFjitsu</option>
                  <option value="detske-mma">Dětské MMA</option>
                  <option value="kondicni">Kondiční trénink</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                  Zpráva *
                </label>
                <textarea
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  required
                  rows={5}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="Napište nám svůj dotaz nebo požadavek..."
                />
              </div>
              
              <button
                type="submit"
                className="w-full px-6 py-3 bg-red-700 text-white rounded-lg font-semibold hover:bg-red-800 transition-colors"
              >
                Odeslat zprávu
              </button>
            </form>
          </div>

          {/* Kontaktní informace */}
          <div className="space-y-8">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
              <h2 className="text-2xl font-bold mb-6 text-gray-900 dark:text-white">Kontaktní údaje</h2>
              <div className="space-y-4">
                <div className="flex items-start">
                  <span className="text-red-500 text-xl mr-4">📍</span>
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-white">Adresa</h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Sportovní hala<br />
                      Strakonice 386 01<br />
                      Česká republika
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <span className="text-red-500 text-xl mr-4">📞</span>
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-white">Telefon</h3>
                    <p className="text-gray-600 dark:text-gray-300">+420 XXX XXX XXX</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <span className="text-red-500 text-xl mr-4">✉️</span>
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-white">E-mail</h3>
                    <p className="text-gray-600 dark:text-gray-300"><EMAIL></p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <span className="text-red-500 text-xl mr-4">🕐</span>
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-white">Otevírací doba</h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Po-Čt: 17:00-21:00<br />
                      Pá: 18:00-20:00<br />
                      So-Ne: Podle rozvrhu
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
              <h2 className="text-2xl font-bold mb-6 text-gray-900 dark:text-white">Najdete nás zde</h2>
              <div className="bg-gray-200 dark:bg-gray-700 rounded-lg h-64 flex items-center justify-center">
                <div className="text-center text-gray-500 dark:text-gray-400">
                  <div className="text-4xl mb-2">🗺️</div>
                  <p>Mapa bude zde</p>
                  <p className="text-sm">Google Maps integrace</p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
              <h2 className="text-2xl font-bold mb-6 text-gray-900 dark:text-white">Sledujte nás</h2>
              <div className="flex gap-4">
                <a href="#" className="flex-1 bg-blue-600 text-white text-center py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                  Facebook
                </a>
                <a href="#" className="flex-1 bg-pink-600 text-white text-center py-3 rounded-lg font-semibold hover:bg-pink-700 transition-colors">
                  Instagram
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
        <Header />
        <main className="flex-1">
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/about" element={<AboutPage />} />
            <Route path="/trainings" element={<TrainingsPage />} />
            <Route path="/coaches" element={<CoachesPage />} />
            <Route path="/gallery" element={<GalleryPage />} />
            <Route path="/contact" element={<ContactPage />} />
          </Routes>
        </main>
        <Footer />
      </div>
    </Router>
  );
}

export default App;
