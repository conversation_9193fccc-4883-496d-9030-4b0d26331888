import { useState, useEffect } from "react";
import { BrowserRouter as Router, Routes, Route, Link } from "react-router-dom";
import reactLogo from './assets/react.svg'
import viteLogo from '/vite.svg'
import './App.css'

function ThemeToggle() {
  const [dark, setDark] = useState(() =>
    window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
  );
  useEffect(() => {
    if (dark) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [dark]);
  return (
    <button
      className="ml-4 px-2 py-1 rounded bg-gray-200 dark:bg-gray-700 text-sm"
      onClick={() => setDark((d) => !d)}
      aria-label="Přepnout tmavý/světlý režim"
    >
      {dark ? '🌙' : '☀️'}
    </button>
  );
}

function Header() {
  return (
    <header className="bg-white dark:bg-gray-900 shadow sticky top-0 z-10">
      <nav className="container mx-auto flex items-center justify-between py-4 px-4">
        <Link to="/" className="text-2xl font-bold text-red-700 dark:text-red-400">COBRA RYU</Link>
        <div className="flex items-center gap-4">
          <Link to="/about" className="hover:underline">O nás</Link>
          <Link to="/trainings" className="hover:underline">Tréninky</Link>
          <Link to="/coaches" className="hover:underline">Trenéři</Link>
          <Link to="/gallery" className="hover:underline">Fotogalerie</Link>
          <Link to="/contact" className="hover:underline">Kontakt</Link>
          <ThemeToggle />
        </div>
      </nav>
    </header>
  );
}

function Footer() {
  return (
    <footer className="bg-gray-100 dark:bg-gray-800 text-center py-6 mt-12">
      <div className="container mx-auto text-gray-600 dark:text-gray-300 text-sm">
        &copy; {new Date().getFullYear()} COBRA RYU Strakonice &mdash; Moderní klub bojových umění
      </div>
    </footer>
  );
}

function HomePage() {
  return (
    <section className="container mx-auto py-16 px-4 text-center">
      <h1 className="text-4xl md:text-5xl font-extrabold mb-4 text-gray-900 dark:text-white">Vítejte v COBRA RYU Strakonice</h1>
      <p className="text-lg md:text-xl mb-8 text-gray-700 dark:text-gray-300">Moderní škola bojových umění pro všechny věkové kategorie. Přidejte se k nám!</p>
      <Link to="/contact" className="inline-block px-6 py-3 bg-red-700 text-white rounded-lg font-semibold shadow hover:bg-red-800 transition">Přidej se k nám</Link>
    </section>
  );
}

function AboutPage() {
  return <section className="container mx-auto py-12 px-4">O nás – info o klubu, filozofie, historie...</section>;
}
function TrainingsPage() {
  return <section className="container mx-auto py-12 px-4">Tréninky – rozpis, typy, pro koho...</section>;
}
function CoachesPage() {
  return <section className="container mx-auto py-12 px-4">Trenéři – profily, zkušenosti...</section>;
}
function GalleryPage() {
  return <section className="container mx-auto py-12 px-4">Fotogalerie – fotky, lightbox...</section>;
}
function ContactPage() {
  return <section className="container mx-auto py-12 px-4">Kontakt – formulář, údaje, mapa...</section>;
}

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
        <Header />
        <main className="flex-1">
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/about" element={<AboutPage />} />
            <Route path="/trainings" element={<TrainingsPage />} />
            <Route path="/coaches" element={<CoachesPage />} />
            <Route path="/gallery" element={<GalleryPage />} />
            <Route path="/contact" element={<ContactPage />} />
          </Routes>
        </main>
        <Footer />
      </div>
    </Router>
  );
}

export default App;
