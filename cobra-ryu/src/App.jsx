import { useState, useEffect } from "react";
import { BrowserRouter as Router, Routes, Route, Link } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import { 
  Menu, 
  X, 
  Sun, 
  Moon, 
  ChevronRight, 
  Play, 
  Users, 
  Trophy, 
  Target,
  Clock,
  MapPin,
  Phone,
  Mail,
  Star,
  Calendar,
  Award,
  Zap
} from "lucide-react";
import './App.css'

// Theme Toggle Component
function ThemeToggle() {
  const [dark, setDark] = useState(() =>
    window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
  );
  
  useEffect(() => {
    if (dark) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [dark]);
  
  return (
    <motion.button
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      className="p-2 rounded-lg bg-white/10 backdrop-blur-sm border border-white/20 text-white hover:bg-white/20 transition-all"
      onClick={() => setDark(!dark)}
      aria-label="Přepnout tmavý/svě<PERSON><PERSON> re<PERSON>"
    >
      {dark ? <Sun size={20} /> : <Moon size={20} />}
    </motion.button>
  );
}

// Header Component
function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 50);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <motion.header 
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        scrolled 
          ? 'bg-dark-900/95 backdrop-blur-md shadow-2xl' 
          : 'bg-transparent'
      }`}
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <nav className="container mx-auto flex items-center justify-between py-4 px-6">
        <Link to="/" className="flex items-center space-x-2">
          <motion.div
            whileHover={{ scale: 1.05 }}
            className="text-2xl md:text-3xl font-black text-white"
          >
            <span className="text-primary-500">COBRA</span> RYU
          </motion.div>
        </Link>
        
        {/* Desktop Navigation */}
        <div className="hidden lg:flex items-center space-x-8">
          {['O nás', 'Tréninky', 'Trenéři', 'Fotogalerie', 'Kontakt'].map((item, index) => (
            <motion.div
              key={item}
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Link 
                to={`/${item.toLowerCase().replace(' ', '-').replace('ř', 'r').replace('í', 'i')}`}
                className="text-white/90 hover:text-primary-400 font-medium transition-colors relative group"
              >
                {item}
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary-500 group-hover:w-full transition-all duration-300"></span>
              </Link>
            </motion.div>
          ))}
          <ThemeToggle />
        </div>

        {/* Mobile Menu Button */}
        <div className="lg:hidden flex items-center space-x-4">
          <ThemeToggle />
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            className="p-2 rounded-lg bg-white/10 backdrop-blur-sm border border-white/20 text-white"
          >
            {mobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </motion.button>
        </div>
      </nav>

      {/* Mobile Navigation Menu */}
      <AnimatePresence>
        {mobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="lg:hidden bg-dark-900/95 backdrop-blur-md border-t border-white/10"
          >
            <div className="container mx-auto px-6 py-4 space-y-4">
              {['O nás', 'Tréninky', 'Trenéři', 'Fotogalerie', 'Kontakt'].map((item, index) => (
                <motion.div
                  key={item}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Link 
                    to={`/${item.toLowerCase().replace(' ', '-').replace('ř', 'r').replace('í', 'i')}`}
                    className="block text-white/90 hover:text-primary-400 font-medium py-2 transition-colors"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    {item}
                  </Link>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.header>
  );
}

// Hero Section Component
function HeroSection() {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Video/Image Placeholder */}
      <div className="absolute inset-0 bg-gradient-to-br from-dark-900 via-primary-900 to-dark-900">
        <div className="absolute inset-0 hero-overlay"></div>
        {/* Animated background elements */}
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-primary-500/10 rounded-full blur-3xl animate-pulse-slow"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-primary-600/5 rounded-full blur-3xl animate-pulse-slow" style={{animationDelay: '1s'}}></div>
      </div>

      <div className="relative z-10 container mx-auto px-6 text-center">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="max-w-5xl mx-auto"
        >
          <motion.h1 
            className="text-5xl md:text-7xl lg:text-8xl font-black text-white mb-6 leading-tight"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.8 }}
          >
            <span className="text-primary-500">COBRA</span> RYU
            <br />
            <span className="text-3xl md:text-4xl lg:text-5xl font-normal text-primary-400">
              Strakonice
            </span>
          </motion.h1>
          
          <motion.p 
            className="text-xl md:text-2xl text-gray-200 mb-8 max-w-3xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.8 }}
          >
            Moderní škola bojových umění pro všechny věkové kategorie. 
            <br className="hidden md:block" />
            <strong className="text-primary-400">MMA, ALKAMFjitsu</strong> a další bojové sporty v srdci Strakonic.
          </motion.p>
          
          <motion.div 
            className="flex flex-col sm:flex-row gap-6 justify-center items-center"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.8 }}
          >
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Link to="/kontakt" className="btn-primary inline-flex items-center space-x-2">
                <span>Přidej se k nám</span>
                <ChevronRight size={20} />
              </Link>
            </motion.div>
            
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Link to="/treninky" className="btn-secondary inline-flex items-center space-x-2">
                <Calendar size={20} />
                <span>Rozvrh tréninků</span>
              </Link>
            </motion.div>
          </motion.div>
        </motion.div>

        {/* Scroll indicator */}
        <motion.div
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1, duration: 0.8 }}
        >
          <motion.div
            animate={{ y: [0, 10, 0] }}
            transition={{ repeat: Infinity, duration: 2 }}
            className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center"
          >
            <motion.div
              animate={{ y: [0, 12, 0] }}
              transition={{ repeat: Infinity, duration: 2 }}
              className="w-1 h-3 bg-white/50 rounded-full mt-2"
            />
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}

// Features Section Component
function FeaturesSection() {
  const features = [
    {
      icon: <Target className="w-12 h-12" />,
      title: "MMA & ALKAMFjitsu",
      description: "Moderní bojové umění kombinující různé techniky a styly pro maximální efektivitu",
      color: "from-primary-500 to-primary-600"
    },
    {
      icon: <Users className="w-12 h-12" />,
      title: "Pro všechny věky",
      description: "Specializované tréninky pro děti, mládež i dospělé všech úrovní a schopností",
      color: "from-blue-500 to-blue-600"
    },
    {
      icon: <Trophy className="w-12 h-12" />,
      title: "Zkušení trenéři",
      description: "Kvalifikovaní instruktoři s dlouholetými zkušenostmi a mezinárodními certifikáty",
      color: "from-yellow-500 to-yellow-600"
    }
  ];

  return (
    <section className="py-20 bg-white dark:bg-dark-900">
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
            Proč si vybrat <span className="text-primary-600">COBRA RYU</span>?
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Jsme více než jen sportovní klub. Vytváříme komunitu bojovníků, kteří se vzájemně podporují a rostou.
          </p>
        </motion.div>

        <div className="grid md:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.2, duration: 0.8 }}
              viewport={{ once: true }}
              whileHover={{ y: -10 }}
              className="group"
            >
              <div className="bg-white dark:bg-dark-800 rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 border border-gray-100 dark:border-dark-700">
                <div className={`w-16 h-16 bg-gradient-to-r ${feature.color} rounded-xl flex items-center justify-center text-white mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  {feature.icon}
                </div>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  {feature.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}

// Stats Section Component
function StatsSection() {
  const stats = [
    { number: "500+", label: "Aktivních členů", icon: <Users className="w-8 h-8" /> },
    { number: "15+", label: "Let zkušeností", icon: <Award className="w-8 h-8" /> },
    { number: "50+", label: "Turnajových vítězství", icon: <Trophy className="w-8 h-8" /> },
    { number: "24/7", label: "Podpora komunity", icon: <Zap className="w-8 h-8" /> }
  ];

  return (
    <section className="py-20 bg-gradient-to-r from-primary-600 to-primary-700">
      <div className="container mx-auto px-6">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0.5 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ delay: index * 0.1, duration: 0.5 }}
              viewport={{ once: true }}
              className="text-center text-white"
            >
              <div className="flex justify-center mb-4 text-white/80">
                {stat.icon}
              </div>
              <div className="text-4xl md:text-5xl font-black mb-2">
                {stat.number}
              </div>
              <div className="text-lg font-medium text-white/90">
                {stat.label}
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}

// CTA Section Component
function CTASection() {
  return (
    <section className="py-20 bg-dark-900 relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-r from-primary-600/20 to-primary-800/20"></div>
      <div className="container mx-auto px-6 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center max-w-4xl mx-auto"
        >
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
            Připravený začít svou <span className="text-primary-400">cestu bojovníka</span>?
          </h2>
          <p className="text-xl text-gray-300 mb-8 leading-relaxed">
            Přidej se k naší komunitě a objevuj své limity. První trénink je zdarma!
          </p>
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Link to="/kontakt" className="btn-primary inline-flex items-center space-x-2 text-lg">
              <span>Zkušební trénink zdarma</span>
              <ChevronRight size={24} />
            </Link>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}

// Footer Component
function Footer() {
  return (
    <footer className="bg-dark-950 text-white py-16">
      <div className="container mx-auto px-6">
        <div className="grid md:grid-cols-4 gap-8 mb-12">
          <div className="md:col-span-2">
            <div className="flex items-center space-x-2 mb-6">
              <span className="text-3xl font-black">
                <span className="text-primary-500">COBRA</span> RYU
              </span>
            </div>
            <p className="text-gray-400 mb-6 leading-relaxed max-w-md">
              Moderní škola bojových umění v srdci Strakonic. MMA, ALKAMFjitsu a další bojové sporty 
              pro všechny věkové kategorie v profesionálním a přátelském prostředí.
            </p>
            <div className="flex space-x-4">
              {['Facebook', 'Instagram', 'YouTube'].map((social) => (
                <motion.a
                  key={social}
                  href="#"
                  whileHover={{ scale: 1.1 }}
                  className="w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center hover:bg-primary-700 transition-colors"
                >
                  <span className="text-sm font-semibold">{social[0]}</span>
                </motion.a>
              ))}
            </div>
          </div>
          
          <div>
            <h4 className="font-bold text-lg mb-6">Rychlé odkazy</h4>
            <div className="space-y-3">
              {['O nás', 'Tréninky', 'Trenéři', 'Fotogalerie', 'Kontakt'].map((link) => (
                <Link
                  key={link}
                  to={`/${link.toLowerCase().replace(' ', '-').replace('ř', 'r').replace('í', 'i')}`}
                  className="block text-gray-400 hover:text-primary-400 transition-colors"
                >
                  {link}
                </Link>
              ))}
            </div>
          </div>
          
          <div>
            <h4 className="font-bold text-lg mb-6">Kontakt</h4>
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <MapPin className="w-5 h-5 text-primary-500" />
                <span className="text-gray-400">Strakonice, Česká republika</span>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="w-5 h-5 text-primary-500" />
                <span className="text-gray-400">+420 XXX XXX XXX</span>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="w-5 h-5 text-primary-500" />
                <span className="text-gray-400"><EMAIL></span>
              </div>
            </div>
          </div>
        </div>
        
        <div className="border-t border-gray-800 pt-8 text-center">
          <p className="text-gray-500">
            &copy; {new Date().getFullYear()} COBRA RYU Strakonice &mdash; Moderní klub bojových umění
          </p>
        </div>
      </div>
    </footer>
  );
}

// Home Page Component
function HomePage() {
  return (
    <div>
      <HeroSection />
      <FeaturesSection />
      <StatsSection />
      <CTASection />
    </div>
  );
}

// Placeholder pages (will be implemented later)
function AboutPage() {
  return (
    <div className="min-h-screen bg-white dark:bg-dark-900 pt-24">
      <div className="container mx-auto px-6 py-16">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-8">O nás</h1>
        <p className="text-gray-600 dark:text-gray-300">Stránka v přípravě...</p>
      </div>
    </div>
  );
}

function TrainingsPage() {
  return (
    <div className="min-h-screen bg-white dark:bg-dark-900 pt-24">
      <div className="container mx-auto px-6 py-16">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-8">Tréninky</h1>
        <p className="text-gray-600 dark:text-gray-300">Stránka v přípravě...</p>
      </div>
    </div>
  );
}

function CoachesPage() {
  return (
    <div className="min-h-screen bg-white dark:bg-dark-900 pt-24">
      <div className="container mx-auto px-6 py-16">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-8">Trenéři</h1>
        <p className="text-gray-600 dark:text-gray-300">Stránka v přípravě...</p>
      </div>
    </div>
  );
}

function GalleryPage() {
  return (
    <div className="min-h-screen bg-white dark:bg-dark-900 pt-24">
      <div className="container mx-auto px-6 py-16">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-8">Fotogalerie</h1>
        <p className="text-gray-600 dark:text-gray-300">Stránka v přípravě...</p>
      </div>
    </div>
  );
}

function ContactPage() {
  return (
    <div className="min-h-screen bg-white dark:bg-dark-900 pt-24">
      <div className="container mx-auto px-6 py-16">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-8">Kontakt</h1>
        <p className="text-gray-600 dark:text-gray-300">Stránka v přípravě...</p>
      </div>
    </div>
  );
}

// Main App Component
function App() {
  return (
    <Router>
      <div className="min-h-screen bg-white dark:bg-dark-900">
        <Header />
        <main>
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/o-nas" element={<AboutPage />} />
            <Route path="/treninky" element={<TrainingsPage />} />
            <Route path="/treneri" element={<CoachesPage />} />
            <Route path="/fotogalerie" element={<GalleryPage />} />
            <Route path="/kontakt" element={<ContactPage />} />
          </Routes>
        </main>
        <Footer />
      </div>
    </Router>
  );
}

export default App;
